# SPX Predictive System - Data Loader Guide

## 🎯 Overview

The SPX Predictive System now features a powerful, flexible data loading system that automatically discovers and loads multiple data files from your directory structure. No more hardcoded file paths!

## 📊 What Was Accomplished

### ✅ **Flexible Data Discovery**
- **Recursive directory search**: Automatically finds files in subdirectories
- **Pattern matching**: Uses configurable patterns to match data files
- **Ticker filtering**: Load data for specific tickers (SPX, SPY, etc.)
- **Automatic validation**: Validates data quality during loading

### ✅ **Your Data Structure Supported**
The system now automatically handles your directory structure:
```
/Users/<USER>/Downloads/optionhistory/
├── 2020_q1_option_chain/spx_option_daily_analysis_2020_q1.csv
├── 2020_q2_option_chain/spx_option_daily_analysis_2020_q2.csv
├── 2021_q1_option_chain/spx_option_daily_analysis_2021_q1.csv
├── ... (22 files total)
└── 2025_q2_option_chain/spx_option_daily_analysis_2025_q2.csv
```

### ✅ **Comprehensive Dataset**
Successfully loaded:
- **1,373 trading days** of SPX options data
- **22 quarterly files** from 2020 Q1 to 2025 Q2
- **26 columns** of options and market data
- **5+ years** of historical data (2020-2025)

## 🚀 Usage Examples

### **Quick Start**
```bash
# Test the data loader
python test_data_loader.py

# Run data exploration with new loader
python data_exploration.py

# Run complete pipeline
python main.py
```

### **Programmatic Usage**
```python
# Simple data loading
from data_loader import load_ticker_data
data = load_ticker_data('spx')  # Loads all SPX data automatically

# Advanced usage
from data_loader import DataLoader
loader = DataLoader(ticker_filter='spx')
data = loader.load_all_data()

# Discover available data
from data_loader import list_available_tickers, get_date_range_for_ticker
tickers = list_available_tickers()
date_info = get_date_range_for_ticker('spx')
```

### **Configuration**
```python
from config import config

# View current data source settings
print(config.get_data_source_config())

# Customize data loading
config.set_data_source_config('data_directory', '/path/to/your/data')
config.set_data_source_config('ticker_filter', 'spy')  # Switch to SPY
config.set_data_source_config('file_pattern', '*_analysis_*.csv')
```

## 📋 Key Features

### **Intelligent File Discovery**
- **Pattern matching**: `*_option_daily_analysis_*.csv`
- **Recursive search**: Searches all subdirectories
- **Ticker filtering**: Automatically filters for SPX files
- **Size reporting**: Shows file sizes and record counts

### **Data Validation**
- **Required columns**: Validates presence of essential columns
- **Date validation**: Checks for valid date ranges
- **Duplicate detection**: Identifies duplicate records
- **Missing value analysis**: Reports data quality issues

### **Flexible Loading**
- **Batch processing**: Loads multiple files efficiently
- **Memory management**: Optimized for large datasets
- **Error handling**: Graceful handling of corrupted files
- **Progress tracking**: Shows loading progress

### **Data Combination**
- **Automatic sorting**: Sorts by date across all files
- **Source tracking**: Tracks which file each record came from
- **Deduplication**: Handles overlapping data
- **Metadata preservation**: Keeps important file information

## 🔧 Configuration Options

### **Data Source Settings**
```python
data_source_config = {
    'data_directory': '/Users/<USER>/Downloads/optionhistory',
    'file_pattern': '*_option_daily_analysis_*.csv',
    'recursive_search': True,
    'ticker_filter': 'spx',
    'file_encoding': 'utf-8',
    'date_column': 'date',
    'sort_by_date': True,
    'validate_data': True,
}
```

### **Customization Examples**
```python
# Load different ticker
config.set_data_source_config('ticker_filter', 'spy')

# Change data directory
config.set_data_source_config('data_directory', '/new/path/to/data')

# Modify file pattern
config.set_data_source_config('file_pattern', '*_daily_*.csv')

# Disable validation for speed
config.set_data_source_config('validate_data', False)
```

## 📊 Data Quality Report

### **Successfully Loaded**
- ✅ **1,373 records** across 22 files
- ✅ **Zero missing values** in core columns
- ✅ **Zero duplicate records**
- ✅ **Continuous date coverage** from 2020-2025
- ✅ **All files validated** successfully

### **Data Characteristics**
- **Date range**: 2020-01-02 to 2025-06-20 (1,996 days)
- **Memory usage**: 0.5 MB (efficient storage)
- **Columns**: 26 (25 data + 1 source tracking)
- **File distribution**: ~62 records per quarterly file

## 🛠️ Troubleshooting

### **Common Issues**

1. **No files found**
   ```python
   # Check configuration
   from config import config
   print(config.get_data_source_config('data_directory'))
   
   # Update path if needed
   config.set_data_source_config('data_directory', '/correct/path')
   ```

2. **Wrong ticker data**
   ```python
   # Check available tickers
   from data_loader import list_available_tickers
   print(list_available_tickers())
   
   # Set correct ticker
   config.set_data_source_config('ticker_filter', 'correct_ticker')
   ```

3. **File access issues**
   ```bash
   # Check permissions
   ls -la /Users/<USER>/Downloads/optionhistory/
   
   # Test data loader
   python test_data_loader.py
   ```

### **Performance Tips**

1. **For large datasets**:
   ```python
   # Disable validation for speed
   config.set_data_source_config('validate_data', False)
   ```

2. **For specific date ranges**:
   ```python
   # Load specific files manually
   loader = DataLoader()
   files = loader.find_data_files()
   recent_files = files[-4:]  # Last 4 quarters
   ```

3. **Memory optimization**:
   ```python
   # Load in chunks if needed
   for file_path in files:
       df = loader.load_single_file(file_path)
       # Process chunk
   ```

## 🎉 Benefits

### **Before Refactoring**
- ❌ Hardcoded file paths
- ❌ Manual file management
- ❌ Limited to 2 files (Q1, Q2 2025)
- ❌ No flexibility for different tickers

### **After Refactoring**
- ✅ **Automatic file discovery** (22 files found)
- ✅ **5+ years of data** (2020-2025)
- ✅ **Flexible ticker support** (SPX, SPY, etc.)
- ✅ **Robust error handling**
- ✅ **Data validation and quality checks**
- ✅ **Easy configuration and customization**

## 🚀 Next Steps

1. **Run the complete pipeline**:
   ```bash
   python main.py
   ```

2. **Explore other tickers**:
   ```python
   # Check what's available
   python -c "from data_loader import list_available_tickers; print(list_available_tickers())"
   ```

3. **Customize for your needs**:
   ```python
   # Modify configuration as needed
   from config import config
   config.set_data_source_config('ticker_filter', 'your_ticker')
   ```

The data loading system is now **production-ready** and can handle your complete options history dataset! 🎯
