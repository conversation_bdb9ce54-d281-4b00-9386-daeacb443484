"""
SPX Predictive System Configuration
==================================

This module centralizes all directory paths and configuration settings
for the SPX Options Predictive Trading System.

Author: Manus AI
Date: June 23, 2025
"""

import os
from pathlib import Path

class Config:
    """Configuration class for SPX Predictive System"""
    
    def __init__(self, base_dir=None):
        """
        Initialize configuration with base directory
        
        Args:
            base_dir (str, optional): Base directory for data files. 
                                    Defaults to current working directory.
        """
        if base_dir is None:
            self.base_dir = Path.cwd()
        else:
            self.base_dir = Path(base_dir)
        
        # Ensure base directory exists
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # Data file paths
        self.data_files = {
            'engineered_data': self.base_dir / 'spx_engineered_data.csv',
            'processed_data': self.base_dir / 'spx_processed_data.csv',
            'feature_info': self.base_dir / 'feature_info.json',
            'selected_features': self.base_dir / 'selected_features.json',
            'model_results': self.base_dir / 'model_results.json',
            'backtest_results': self.base_dir / 'backtest_results.json',
            'strategy_params': self.base_dir / 'strategy_params.json'
        }
        
        # Model file paths
        self.model_files = {
            'best_model': self.base_dir / 'best_model.pkl',
            'scaler': self.base_dir / 'scaler.pkl',
            'spx_model': self.base_dir / 'spx_model.pkl',
            'spx_scaler': self.base_dir / 'spx_scaler.pkl'
        }
        
        # Output file paths
        self.output_files = {
            'best_trades': self.base_dir / 'best_trades.csv',
            'trading_performance_chart': self.base_dir / 'trading_performance.png',
            'spx_price_analysis': self.base_dir / 'spx_price_analysis.png',
            'options_flow_analysis': self.base_dir / 'options_flow_analysis.png',
            'wall_analysis': self.base_dir / 'wall_analysis.png',
            'correlation_analysis': self.base_dir / 'correlation_analysis.png',
            'predictive_analysis': self.base_dir / 'predictive_analysis.png',
            'html_report': self.base_dir / 'spx_performance_report.html'
        }
        
        # Trading strategy parameters
        self.strategy_params = {
            'confidence_threshold': 0.5,
            'stop_loss_pct': 2.0,
            'take_profit_pct': 3.0,
            'max_days_held': 3,
            'outlier_contamination': 0.1,
            'n_features': 30
        }
        
        # Model parameters
        self.model_params = {
            'n_estimators': 100,
            'learning_rate': 0.1,
            'max_depth': 5,
            'random_state': 42,
            'verbose': -1
        }

        # Pipeline configuration
        self.pipeline_config = {
            'auto_skip_existing': True,  # Skip stages if outputs already exist
            'force_retrain_models': False,  # Force model retraining
            'create_backups': True,  # Backup existing files before overwriting
            'parallel_processing': False,  # Enable parallel processing where possible
            'validation_split': 0.2,  # Validation split for model training
            'cross_validation_folds': 5,  # Number of CV folds
            'early_stopping_patience': 10,  # Early stopping patience
            'save_intermediate_results': True,  # Save intermediate results
            'log_level': 'INFO',  # Logging level: DEBUG, INFO, WARNING, ERROR
            'max_memory_usage_gb': 8,  # Maximum memory usage limit
        }

        # Data source configuration
        self.data_source_config = {
            'data_directory': Path('/Users/<USER>/Downloads/optionhistory'),  # Root directory containing data
            'file_pattern': '*_option_daily_analysis_*.csv',  # Pattern to match data files
            'recursive_search': True,  # Search subdirectories recursively
            'ticker_filter': 'spx',  # Filter for specific ticker (None for all)
            'file_encoding': 'utf-8',  # File encoding
            'date_column': 'date',  # Name of the date column
            'sort_by_date': True,  # Sort combined data by date
            'validate_data': True,  # Validate data after loading
            'backup_source_files': False,  # Create backups of source files
        }
    
    def get_data_file(self, key):
        """Get path to a data file"""
        return str(self.data_files.get(key, ''))
    
    def get_model_file(self, key):
        """Get path to a model file"""
        return str(self.model_files.get(key, ''))
    
    def get_output_file(self, key):
        """Get path to an output file"""
        return str(self.output_files.get(key, ''))
    
    def ensure_directories_exist(self):
        """Ensure all necessary directories exist"""
        self.base_dir.mkdir(parents=True, exist_ok=True)
        return True
    
    def get_all_paths(self):
        """Get dictionary of all configured paths"""
        all_paths = {}
        all_paths.update({f"data_{k}": str(v) for k, v in self.data_files.items()})
        all_paths.update({f"model_{k}": str(v) for k, v in self.model_files.items()})
        all_paths.update({f"output_{k}": str(v) for k, v in self.output_files.items()})
        return all_paths
    
    def get_pipeline_config(self, key=None):
        """Get pipeline configuration value(s)"""
        if key is None:
            return self.pipeline_config.copy()
        return self.pipeline_config.get(key)

    def set_pipeline_config(self, key, value):
        """Set pipeline configuration value"""
        self.pipeline_config[key] = value

    def get_data_source_config(self, key=None):
        """Get data source configuration value(s)"""
        if key is None:
            return self.data_source_config.copy()
        return self.data_source_config.get(key)

    def set_data_source_config(self, key, value):
        """Set data source configuration value"""
        self.data_source_config[key] = value

    def print_config(self):
        """Print current configuration"""
        print("=== SPX Predictive System Configuration ===")
        print(f"Base Directory: {self.base_dir}")
        print(f"Base Directory Exists: {self.base_dir.exists()}")

        print("\nData Files:")
        for key, path in self.data_files.items():
            exists = "✓" if path.exists() else "✗"
            print(f"  {key}: {path} {exists}")

        print("\nModel Files:")
        for key, path in self.model_files.items():
            exists = "✓" if path.exists() else "✗"
            print(f"  {key}: {path} {exists}")

        print("\nOutput Files:")
        for key, path in self.output_files.items():
            exists = "✓" if path.exists() else "✗"
            print(f"  {key}: {path} {exists}")

        print("\nPipeline Configuration:")
        for key, value in self.pipeline_config.items():
            print(f"  {key}: {value}")


# Create default configuration instance
# This can be imported and used directly: from config import config
config = Config()

# For backward compatibility, create individual path variables
# These match the original hardcoded paths but are now configurable
DATA_DIR = str(config.base_dir)
ENGINEERED_DATA_PATH = config.get_data_file('engineered_data')
PROCESSED_DATA_PATH = config.get_data_file('processed_data')
FEATURE_INFO_PATH = config.get_data_file('feature_info')
SELECTED_FEATURES_PATH = config.get_data_file('selected_features')
MODEL_RESULTS_PATH = config.get_data_file('model_results')
BACKTEST_RESULTS_PATH = config.get_data_file('backtest_results')
STRATEGY_PARAMS_PATH = config.get_data_file('strategy_params')

BEST_MODEL_PATH = config.get_model_file('best_model')
SCALER_PATH = config.get_model_file('scaler')
SPX_MODEL_PATH = config.get_model_file('spx_model')
SPX_SCALER_PATH = config.get_model_file('spx_scaler')

BEST_TRADES_PATH = config.get_output_file('best_trades')
TRADING_PERFORMANCE_CHART_PATH = config.get_output_file('trading_performance_chart')
SPX_PRICE_ANALYSIS_PATH = config.get_output_file('spx_price_analysis')
OPTIONS_FLOW_ANALYSIS_PATH = config.get_output_file('options_flow_analysis')
WALL_ANALYSIS_PATH = config.get_output_file('wall_analysis')
CORRELATION_ANALYSIS_PATH = config.get_output_file('correlation_analysis')
PREDICTIVE_ANALYSIS_PATH = config.get_output_file('predictive_analysis')


if __name__ == "__main__":
    # Print configuration when run directly
    config.print_config()
