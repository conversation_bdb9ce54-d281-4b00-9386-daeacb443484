# SPX Options Predictive Trading System - Requirements
# Python 3.11+ recommended

# Core Data Science Libraries
pandas>=2.0.0
numpy>=1.22.0
matplotlib>=3.5.0
seaborn>=0.11.0

# Machine Learning Libraries
scikit-learn>=1.7.0
xgboost>=3.0.0
lightgbm>=4.6.0

# Additional Scientific Computing
scipy>=1.8.0
joblib>=1.2.0

# Optional: For enhanced performance
threadpoolctl>=3.1.0

# Development and Testing (optional)
jupyter>=1.0.0
ipython>=8.0.0

# Note: Install with: pip install -r requirements.txt

