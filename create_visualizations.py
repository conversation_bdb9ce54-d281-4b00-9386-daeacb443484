import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Load processed data
data = pd.read_csv('/home/<USER>/spx_processed_data.csv')
data['date'] = pd.to_datetime(data['date'])

print("=== Creating Visualizations for SPX Option Data ===")

# Set up the plotting environment
plt.style.use('default')
fig_size = (15, 10)

# 1. SPX Price and Returns Over Time
fig, axes = plt.subplots(2, 2, figsize=fig_size)
fig.suptitle('SPX Price Analysis', fontsize=16, fontweight='bold')

# SPX Close Price
axes[0,0].plot(data['date'], data['spx_close'], linewidth=2, color='blue')
axes[0,0].set_title('SPX Close Price Over Time')
axes[0,0].set_ylabel('Price')
axes[0,0].grid(True, alpha=0.3)

# Daily Returns
axes[0,1].plot(data['date'], data['spx_return'] * 100, linewidth=1, color='green')
axes[0,1].axhline(y=0, color='red', linestyle='--', alpha=0.7)
axes[0,1].set_title('Daily Returns (%)')
axes[0,1].set_ylabel('Return %')
axes[0,1].grid(True, alpha=0.3)

# Return Distribution
axes[1,0].hist(data['spx_return'].dropna() * 100, bins=30, alpha=0.7, color='purple', edgecolor='black')
axes[1,0].set_title('Distribution of Daily Returns')
axes[1,0].set_xlabel('Return %')
axes[1,0].set_ylabel('Frequency')
axes[1,0].grid(True, alpha=0.3)

# Intraday Range
axes[1,1].plot(data['date'], data['spx_range_pct'], linewidth=1, color='orange')
axes[1,1].set_title('Intraday Range (%)')
axes[1,1].set_ylabel('Range %')
axes[1,1].grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('/home/<USER>/spx_price_analysis.png', dpi=300, bbox_inches='tight')
plt.close()

# 2. Options Flow and Gamma Analysis
fig, axes = plt.subplots(2, 2, figsize=fig_size)
fig.suptitle('Options Flow and Gamma Analysis', fontsize=16, fontweight='bold')

# Put/Call OI Ratio
axes[0,0].plot(data['date'], data['put_call_oi_ratio'], linewidth=2, color='red')
axes[0,0].set_title('Put/Call Open Interest Ratio')
axes[0,0].set_ylabel('Ratio')
axes[0,0].grid(True, alpha=0.3)

# Total Gamma
axes[0,1].plot(data['date'], data['total_gamma'], linewidth=2, color='blue')
axes[0,1].set_title('Total Gamma')
axes[0,1].set_ylabel('Gamma')
axes[0,1].grid(True, alpha=0.3)

# Put/Call Notional Ratio
axes[1,0].plot(data['date'], data['put_call_notional_ratio'], linewidth=2, color='green')
axes[1,0].set_title('Put/Call Notional Ratio')
axes[1,0].set_ylabel('Ratio')
axes[1,0].grid(True, alpha=0.3)

# Total Volume
axes[1,1].plot(data['date'], data['total_volume'], linewidth=2, color='purple')
axes[1,1].set_title('Total Options Volume')
axes[1,1].set_ylabel('Volume')
axes[1,1].grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('/home/<USER>/options_flow_analysis.png', dpi=300, bbox_inches='tight')
plt.close()

# 3. Wall Analysis
fig, axes = plt.subplots(2, 2, figsize=fig_size)
fig.suptitle('Options Wall Analysis', fontsize=16, fontweight='bold')

# SPX position relative to walls
axes[0,0].plot(data['date'], data['spx_to_put_wall'], label='Distance to Put Wall', color='red')
axes[0,0].plot(data['date'], data['spx_to_call_wall'], label='Distance to Call Wall', color='green')
axes[0,0].set_title('SPX Distance to Option Walls (%)')
axes[0,0].set_ylabel('Distance %')
axes[0,0].legend()
axes[0,0].grid(True, alpha=0.3)

# Put Wall vs SPX
axes[0,1].plot(data['date'], data['spx_close'], label='SPX Close', color='blue')
axes[0,1].plot(data['date'], data['put_wall_strike'], label='Put Wall Strike', color='red', alpha=0.7)
axes[0,1].set_title('SPX vs Put Wall Strike')
axes[0,1].set_ylabel('Price')
axes[0,1].legend()
axes[0,1].grid(True, alpha=0.3)

# Call Wall vs SPX
axes[1,0].plot(data['date'], data['spx_close'], label='SPX Close', color='blue')
axes[1,0].plot(data['date'], data['call_wall_strike'], label='Call Wall Strike', color='green', alpha=0.7)
axes[1,0].set_title('SPX vs Call Wall Strike')
axes[1,0].set_ylabel('Price')
axes[1,0].legend()
axes[1,0].grid(True, alpha=0.3)

# Gamma Exposure
axes[1,1].plot(data['date'], data['gamma_put_wall_exposure'], label='Put Gamma Exposure', color='red')
axes[1,1].plot(data['date'], data['gamma_call_wall_exposure'], label='Call Gamma Exposure', color='green')
axes[1,1].set_title('Gamma Exposure')
axes[1,1].set_ylabel('Exposure')
axes[1,1].legend()
axes[1,1].grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('/home/<USER>/wall_analysis.png', dpi=300, bbox_inches='tight')
plt.close()

# 4. Correlation Analysis
# Select key features for correlation
correlation_features = [
    'spx_return_next', 'spx_return', 'spx_range_pct', 'put_call_oi_ratio',
    'put_call_notional_ratio', 'total_gamma', 'total_vega', 'total_volume',
    'spx_to_put_wall', 'spx_to_call_wall', 'gamma_put_wall_exposure',
    'gamma_call_wall_exposure'
]

# Calculate correlation matrix
corr_data = data[correlation_features].copy()
# Handle extreme values in gamma exposure ratio
corr_data = corr_data.replace([np.inf, -np.inf], np.nan)
correlation_matrix = corr_data.corr()

# Create correlation heatmap
plt.figure(figsize=(12, 10))
mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='RdBu_r', center=0,
            square=True, fmt='.2f', cbar_kws={"shrink": .8})
plt.title('Feature Correlation Matrix', fontsize=16, fontweight='bold')
plt.tight_layout()
plt.savefig('/home/<USER>/correlation_analysis.png', dpi=300, bbox_inches='tight')
plt.close()

# 5. Predictive Feature Analysis
fig, axes = plt.subplots(2, 2, figsize=fig_size)
fig.suptitle('Predictive Feature Analysis', fontsize=16, fontweight='bold')

# Next day direction vs current features
up_days = data[data['spx_direction_next'] == 1]
down_days = data[data['spx_direction_next'] == 0]

# Put/Call ratio by direction
axes[0,0].boxplot([up_days['put_call_oi_ratio'].dropna(), down_days['put_call_oi_ratio'].dropna()],
                  labels=['Up Days', 'Down Days'])
axes[0,0].set_title('Put/Call OI Ratio by Next Day Direction')
axes[0,0].set_ylabel('Ratio')
axes[0,0].grid(True, alpha=0.3)

# Total Gamma by direction
axes[0,1].boxplot([up_days['total_gamma'].dropna(), down_days['total_gamma'].dropna()],
                  labels=['Up Days', 'Down Days'])
axes[0,1].set_title('Total Gamma by Next Day Direction')
axes[0,1].set_ylabel('Gamma')
axes[0,1].grid(True, alpha=0.3)

# Distance to put wall by direction
axes[1,0].boxplot([up_days['spx_to_put_wall'].dropna(), down_days['spx_to_put_wall'].dropna()],
                  labels=['Up Days', 'Down Days'])
axes[1,0].set_title('Distance to Put Wall by Next Day Direction')
axes[1,0].set_ylabel('Distance %')
axes[1,0].grid(True, alpha=0.3)

# Range by direction
axes[1,1].boxplot([up_days['spx_range_pct'].dropna(), down_days['spx_range_pct'].dropna()],
                  labels=['Up Days', 'Down Days'])
axes[1,1].set_title('Intraday Range by Next Day Direction')
axes[1,1].set_ylabel('Range %')
axes[1,1].grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('/home/<USER>/predictive_analysis.png', dpi=300, bbox_inches='tight')
plt.close()

# Print summary statistics
print("\n=== Key Statistics ===")
print(f"Total trading days: {len(data)}")
print(f"Up days: {sum(data['spx_direction_next'] == 1)} ({sum(data['spx_direction_next'] == 1)/len(data)*100:.1f}%)")
print(f"Down days: {sum(data['spx_direction_next'] == 0)} ({sum(data['spx_direction_next'] == 0)/len(data)*100:.1f}%)")
print(f"Average daily return: {data['spx_return'].mean()*100:.3f}%")
print(f"Return volatility: {data['spx_return'].std()*100:.3f}%")
print(f"Max daily gain: {data['spx_return'].max()*100:.2f}%")
print(f"Max daily loss: {data['spx_return'].min()*100:.2f}%")

# Correlation with next day returns
print(f"\n=== Correlations with Next Day Returns ===")
correlations = correlation_matrix['spx_return_next'].sort_values(key=abs, ascending=False)
for feature, corr in correlations.items():
    if feature != 'spx_return_next' and not pd.isna(corr):
        print(f"{feature}: {corr:.3f}")

print("\n=== Visualizations saved ===")
print("Files created:")
print("  - spx_price_analysis.png")
print("  - options_flow_analysis.png") 
print("  - wall_analysis.png")
print("  - correlation_analysis.png")
print("  - predictive_analysis.png")

