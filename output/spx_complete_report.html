
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>SPX Trading System - Complete Report</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
                .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
                .header { text-align: center; margin-bottom: 30px; padding: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px; }
                .header h1 { margin: 0; font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
                .current-signal { background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%); border: 3px solid #28a745; padding: 25px; border-radius: 15px; margin: 25px 0; text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                .signal-value { font-size: 2.5em; font-weight: bold; color: #28a745; text-shadow: 1px 1px 2px rgba(0,0,0,0.1); margin: 10px 0; }
                .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 20px; margin: 30px 0; }
                .metric-card { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 12px; text-align: center; border-left: 5px solid #007bff; box-shadow: 0 3px 10px rgba(0,0,0,0.1); transition: transform 0.3s ease; }
                .metric-card:hover { transform: translateY(-5px); }
                .metric-value { font-size: 2em; font-weight: bold; color: #007bff; margin-bottom: 5px; }
                .metric-label { color: #666; font-size: 0.9em; font-weight: 500; }
                .trades-section { margin: 30px 0; }
                .trades-table { width: 100%; border-collapse: collapse; margin: 20px 0; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
                .trades-table th { background: linear-gradient(135deg, #343a40 0%, #495057 100%); color: white; padding: 15px; text-align: left; font-weight: bold; }
                .trades-table td { padding: 12px 15px; border-bottom: 1px solid #dee2e6; }
                .trades-table tr:nth-child(even) { background-color: #f8f9fa; }
                .trades-table tr:hover { background-color: #e3f2fd; }
                .win { color: #28a745; font-weight: bold; }
                .loss { color: #dc3545; font-weight: bold; }
                .chart-container { text-align: center; margin: 40px 0; padding: 20px; background: #f8f9fa; border-radius: 15px; }
                .chart-container img { max-width: 100%; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.2); }
                .status-section { background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center; border: 2px solid #28a745; }
                .footer { text-align: center; margin-top: 40px; padding: 25px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 15px; color: #666; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚀 SPX Trading System</h1>
                    <h2>Complete Performance Report</h2>
                    <p>Generated: 2025-06-25 09:50:59</p>
                </div>
                
                <div class="current-signal">
                    <h3>🚨 CURRENT MARKET SIGNAL</h3>
                    <div class="signal-value">SHORT</div>
                    <p><strong>Date:</strong> 2025-06-20</p>
                    <p><strong>SPX:</strong> $5,967.84 | <strong>VIX:</strong> 20.62</p>
                    <p><strong>Prediction:</strong> DOWN | <strong>Confidence:</strong> 53.9%</p>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">80.4%</div>
                        <div class="metric-label">Win Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">189.7%</div>
                        <div class="metric-label">Total Return</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">408</div>
                        <div class="metric-label">Total Trades</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">100.0%</div>
                        <div class="metric-label">Monthly Win Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">0.466%</div>
                        <div class="metric-label">Avg Return/Trade</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">71.2%</div>
                        <div class="metric-label">Prediction Accuracy</div>
                    </div>
                </div>
                
                <div class="trades-section">
                    <h3>📊 Last 5 Trades</h3>
                    <table class="trades-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Direction</th>
                                <th>Entry Price</th>
                                <th>Return</th>
                                <th>Result</th>
                                <th>Confidence</th>
                            </tr>
                        </thead>
                        <tbody>
        
                            <tr>
                                <td>2025-05-30</td>
                                <td>📈 LONG</td>
                                <td>$5,911.69</td>
                                <td class="win">+0.410%</td>
                                <td class="win">WIN ✅</td>
                                <td>52.3%</td>
                            </tr>
            
                            <tr>
                                <td>2025-06-05</td>
                                <td>📈 LONG</td>
                                <td>$5,939.30</td>
                                <td class="win">+1.028%</td>
                                <td class="win">WIN ✅</td>
                                <td>53.2%</td>
                            </tr>
            
                            <tr>
                                <td>2025-06-13</td>
                                <td>📉 SHORT</td>
                                <td>$5,976.97</td>
                                <td class="loss">-0.939%</td>
                                <td class="loss">LOSS ❌</td>
                                <td>55.2%</td>
                            </tr>
            
                            <tr>
                                <td>2025-06-16</td>
                                <td>📉 SHORT</td>
                                <td>$6,033.11</td>
                                <td class="win">+0.835%</td>
                                <td class="win">WIN ✅</td>
                                <td>53.9%</td>
                            </tr>
            
                            <tr>
                                <td>2025-06-20</td>
                                <td>📉 SHORT</td>
                                <td>$5,967.84</td>
                                <td class="loss">+nan%</td>
                                <td class="loss">LOSS ❌</td>
                                <td>53.9%</td>
                            </tr>
            
                        </tbody>
                    </table>
                </div>
                
                <div class="chart-container">
                    <h3>📈 Equity Curve</h3>
                    <img src="../output/spx_equity_curve.png" alt="Equity Curve">
                </div>
                
                <div class="status-section">
                    <h3>✅ System Status: OPERATIONAL</h3>
                    <p><strong>Last 5 Trades:</strong> 3/5 wins (60.0%)</p>
                    <p><strong>Last 5 Return:</strong> +1.33%</p>
                    <p><strong>Model Health:</strong> 71.2% accuracy</p>
                    <p><strong>System Ready:</strong> Live trading analysis enabled</p>
                </div>
                
                <div class="footer">
                    <h4>🎯 SPX Trading System</h4>
                    <p>Powered by 15 carefully selected features</p>
                    <p>Conservative, profitable approach with 80.4% win rate</p>
                    <p>Built with Random Forest ML and robust risk management</p>
                </div>
            </div>
        </body>
        </html>
        