#!/usr/bin/env python3
"""
Create Equity Curve for Enhanced SPX + VIX System
================================================

Creates comprehensive equity curve and performance visualizations.

Author: Manus AI
Date: June 23, 2025
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')

def create_equity_curve():
    """Create comprehensive equity curve and performance charts"""
    
    print("📈 CREATING EQUITY CURVE FOR ENHANCED SPX + VIX SYSTEM")
    print("=" * 80)
    
    try:
        # Load trading data
        print("1. Loading trading data...")
        trades_df = pd.read_csv('best_trades.csv')
        trades_df['entry_date'] = pd.to_datetime(trades_df['entry_date'])
        trades_df['exit_date'] = pd.to_datetime(trades_df['exit_date'])
        
        # Load backtest results
        with open('backtest_results.json', 'r') as f:
            backtest_results = json.load(f)
        
        metrics = backtest_results['rsquared_optimized']
        
        print(f"   📊 Loaded {len(trades_df)} trades")
        print(f"   📅 Date range: {trades_df['entry_date'].min().date()} to {trades_df['entry_date'].max().date()}")
        
        # Create equity curve
        print("2. Creating equity curve...")
        
        # Sort trades by entry date
        trades_sorted = trades_df.sort_values('entry_date').copy()
        
        # Calculate cumulative returns
        trades_sorted['cumulative_return'] = (1 + trades_sorted['pnl_pct'] / 100).cumprod()
        trades_sorted['equity_value'] = trades_sorted['cumulative_return'] * 10000  # Starting with $10,000
        
        # Create rolling statistics
        trades_sorted['rolling_win_rate'] = trades_sorted['pnl_pct'].rolling(20).apply(lambda x: (x > 0).mean())
        trades_sorted['rolling_avg_return'] = trades_sorted['pnl_pct'].rolling(20).mean()
        
        # Create the main figure with subplots
        fig = plt.figure(figsize=(16, 12))
        fig.suptitle('Enhanced SPX + VIX System - Comprehensive Performance Analysis', 
                    fontsize=16, fontweight='bold', y=0.98)
        
        # 1. Equity Curve (main chart)
        ax1 = plt.subplot(3, 2, (1, 2))
        ax1.plot(trades_sorted['entry_date'], trades_sorted['equity_value'], 
                linewidth=2, color='#2E86AB', label='Portfolio Value')
        ax1.fill_between(trades_sorted['entry_date'], 10000, trades_sorted['equity_value'], 
                        alpha=0.3, color='#2E86AB')
        
        # Add benchmark line
        ax1.axhline(y=10000, color='gray', linestyle='--', alpha=0.7, label='Starting Value')
        
        ax1.set_title('Portfolio Equity Curve', fontsize=14, fontweight='bold', pad=20)
        ax1.set_ylabel('Portfolio Value ($)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Format x-axis
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        
        # Add performance metrics text
        final_value = trades_sorted['equity_value'].iloc[-1]
        total_return = (final_value - 10000) / 10000 * 100
        
        metrics_text = f"""Performance Metrics:
Total Return: {total_return:.1f}%
Win Rate: {metrics['win_rate']*100:.1f}%
Total Trades: {metrics['total_trades']}
Sharpe Ratio: {metrics['sharpe_ratio']:.3f}
Avg Days Held: {metrics['avg_days_held']:.1f}"""
        
        ax1.text(0.02, 0.98, metrics_text, transform=ax1.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                fontsize=10, fontfamily='monospace')
        
        # 2. Monthly Returns Heatmap
        ax2 = plt.subplot(3, 2, 3)
        
        # Calculate monthly returns
        trades_sorted['year_month'] = trades_sorted['entry_date'].dt.to_period('M')
        monthly_returns = trades_sorted.groupby('year_month')['pnl_pct'].sum()
        
        # Create monthly returns bar chart
        colors = ['green' if x > 0 else 'red' for x in monthly_returns.values]
        bars = ax2.bar(range(len(monthly_returns)), monthly_returns.values, color=colors, alpha=0.7)
        
        ax2.set_title('Monthly Returns (%)', fontsize=12, fontweight='bold')
        ax2.set_ylabel('Return (%)', fontsize=10)
        ax2.grid(True, alpha=0.3, axis='y')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # Set x-axis labels
        ax2.set_xticks(range(0, len(monthly_returns), max(1, len(monthly_returns)//6)))
        ax2.set_xticklabels([str(monthly_returns.index[i]) for i in range(0, len(monthly_returns), max(1, len(monthly_returns)//6))], 
                           rotation=45, fontsize=8)
        
        # 3. Rolling Win Rate
        ax3 = plt.subplot(3, 2, 4)
        ax3.plot(trades_sorted['entry_date'], trades_sorted['rolling_win_rate'] * 100, 
                linewidth=2, color='#F18F01', label='20-Trade Rolling Win Rate')
        ax3.axhline(y=50, color='gray', linestyle='--', alpha=0.7, label='50% Breakeven')
        ax3.set_title('Rolling Win Rate (20 Trades)', fontsize=12, fontweight='bold')
        ax3.set_ylabel('Win Rate (%)', fontsize=10)
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        ax3.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        plt.setp(ax3.xaxis.get_majorticklabels(), rotation=45)
        
        # 4. Trade Distribution
        ax4 = plt.subplot(3, 2, 5)
        
        # Create histogram of returns
        wins = trades_df[trades_df['pnl_pct'] > 0]['pnl_pct']
        losses = trades_df[trades_df['pnl_pct'] <= 0]['pnl_pct']
        
        ax4.hist(wins, bins=20, alpha=0.7, color='green', label=f'Wins ({len(wins)})', density=True)
        ax4.hist(losses, bins=20, alpha=0.7, color='red', label=f'Losses ({len(losses)})', density=True)
        
        ax4.set_title('Trade Return Distribution', fontsize=12, fontweight='bold')
        ax4.set_xlabel('Return (%)', fontsize=10)
        ax4.set_ylabel('Density', fontsize=10)
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.axvline(x=0, color='black', linestyle='-', alpha=0.5)
        
        # 5. VIX Features Impact
        ax5 = plt.subplot(3, 2, 6)
        
        # Load selected features to show VIX impact
        with open('selected_features.json', 'r') as f:
            selected_features = json.load(f)
        
        vix_features = [f for f in selected_features if f.startswith('vix_')]
        spx_features = [f for f in selected_features if not f.startswith('vix_')]
        
        # Create pie chart of feature types
        sizes = [len(vix_features), len(spx_features)]
        labels = [f'VIX Features\n({len(vix_features)})', f'SPX Features\n({len(spx_features)})']
        colors = ['#FF6B6B', '#4ECDC4']
        
        wedges, texts, autotexts = ax5.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', 
                                          startangle=90, textprops={'fontsize': 10})
        ax5.set_title('Feature Composition in Final Model', fontsize=12, fontweight='bold')
        
        # Adjust layout
        plt.tight_layout()
        plt.subplots_adjust(top=0.93, hspace=0.3, wspace=0.3)
        
        # Save the figure
        plt.savefig('enhanced_equity_curve.png', dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.savefig('enhanced_equity_curve.pdf', bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        
        print("   ✅ Equity curve saved as enhanced_equity_curve.png")
        print("   ✅ PDF version saved as enhanced_equity_curve.pdf")
        
        # Create summary statistics
        print("\n3. Performance summary:")
        print(f"   📈 Final Portfolio Value: ${final_value:,.0f}")
        print(f"   📊 Total Return: {total_return:.1f}%")
        print(f"   🏆 Win Rate: {metrics['win_rate']*100:.1f}%")
        print(f"   📅 Trading Period: {(trades_sorted['entry_date'].max() - trades_sorted['entry_date'].min()).days} days")
        print(f"   ⚡ Avg Days per Trade: {metrics['avg_days_held']:.1f}")
        print(f"   📊 Best Month: {monthly_returns.max():.1f}%")
        print(f"   📉 Worst Month: {monthly_returns.min():.1f}%")
        
        # Calculate additional metrics
        winning_months = (monthly_returns > 0).sum()
        total_months = len(monthly_returns)
        monthly_win_rate = winning_months / total_months * 100
        
        print(f"   📅 Monthly Win Rate: {monthly_win_rate:.1f}% ({winning_months}/{total_months})")
        
        return 'enhanced_equity_curve.png'
        
    except Exception as e:
        print(f"❌ Equity curve creation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    curve_file = create_equity_curve()
    if curve_file:
        print(f"\n🎉 Equity curve created successfully!")
        print(f"📊 File: {curve_file}")
        print("📈 Comprehensive performance analysis complete")
    else:
        print("❌ Failed to create equity curve")
