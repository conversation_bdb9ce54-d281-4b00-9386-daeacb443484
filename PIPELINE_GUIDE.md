# SPX Predictive System - Pipeline Guide

## 🚀 Complete Pipeline Overview

The `main.py` script provides a comprehensive pipeline that orchestrates the entire machine learning workflow:

```
Data Loading → Feature Engineering → Model Training → Backtesting → Visualization
```

## 📋 Pipeline Stages

### Stage 1: Data Exploration & Processing
- Loads raw market data
- Performs initial data cleaning and validation
- Creates derived features (returns, ratios, etc.)
- **Output**: `spx_processed_data.csv`

### Stage 2: Feature Engineering
- Creates advanced engineered features (153 total)
- Generates lagged features, rolling statistics, momentum indicators
- Creates options-specific features and interaction terms
- **Output**: `spx_engineered_data.csv`, `feature_info.json`

### Stage 3: Model Training
- Trains multiple ML models (LightGBM, XGBoost, Random Forest, etc.)
- Performs feature selection (top 30 features)
- Creates ensemble models with voting classifiers
- **Output**: `best_model.pkl`, `scaler.pkl`, `selected_features.json`

### Stage 4: Backtesting
- Tests trading strategy with different confidence thresholds
- Calculates performance metrics (win rate, returns, Sharpe ratio)
- Generates detailed trade records
- **Output**: `backtest_results.json`, `best_trades.csv`

### Stage 5: Visualization
- Creates comprehensive charts and analysis plots
- Generates performance visualizations
- **Output**: Multiple PNG files with charts

## 🎯 Usage Examples

### Complete Pipeline (Recommended)
```bash
# Run everything from scratch
python main.py

# With verbose logging
python main.py --verbose

# Save logs to file
python main.py --log-file pipeline.log --log-level DEBUG
```

### Partial Pipeline Runs
```bash
# Data processing only
python main.py --data-only

# Skip training (use existing models)
python main.py --skip-training

# Force retrain models
python main.py --force-retrain

# Skip backtesting
python main.py --skip-backtest
```

### Development Workflows
```bash
# Data scientist workflow: process data and train models
python main.py --skip-backtest

# Trader workflow: use existing models, focus on backtesting
python main.py --skip-training

# Research workflow: force retrain with new parameters
python main.py --force-retrain --verbose
```

## ⚙️ Configuration Options

### Pipeline Configuration
The pipeline behavior is controlled by `config.py`:

```python
from config import config

# View current pipeline settings
print(config.get_pipeline_config())

# Modify settings
config.set_pipeline_config('auto_skip_existing', False)  # Always rerun stages
config.set_pipeline_config('force_retrain_models', True)  # Always retrain
```

### Key Configuration Parameters
- `auto_skip_existing`: Skip stages if outputs already exist (default: True)
- `force_retrain_models`: Force model retraining (default: False)
- `create_backups`: Backup existing files before overwriting (default: True)
- `log_level`: Logging verbosity (default: 'INFO')
- `max_memory_usage_gb`: Memory usage limit (default: 8)

## 📊 Progress Tracking

The pipeline provides detailed progress tracking:

```
================================================================================
🚀 STAGE 2/5: FEATURE ENGINEERING
📊 Progress: 20.0%
⏰ Started: 15:03:30
================================================================================
🔧 Running feature engineering...
✅ Feature engineering completed successfully
✅ COMPLETED: Feature Engineering
⏱️  Stage Duration: 45.2s
📊 Overall Progress: 40.0%
```

## 🔍 Monitoring and Logging

### Log Levels
- **DEBUG**: Detailed debugging information
- **INFO**: General information about pipeline progress
- **WARNING**: Warning messages about potential issues
- **ERROR**: Error messages for failed operations

### Log File Example
```bash
# Save detailed logs
python main.py --log-file pipeline_$(date +%Y%m%d_%H%M%S).log --log-level DEBUG
```

## 🛠️ Troubleshooting

### Common Issues

1. **Memory Issues**
   ```bash
   # Reduce memory usage
   python main.py --log-level WARNING  # Less verbose logging
   ```

2. **Disk Space**
   ```bash
   # Check available space
   df -h .
   
   # Clean up old files if needed
   rm -f *.log old_*.csv
   ```

3. **Model Training Failures**
   ```bash
   # Skip training and use existing models
   python main.py --skip-training
   
   # Or force retrain with verbose output
   python main.py --force-retrain --verbose
   ```

4. **Feature Engineering Issues**
   ```bash
   # Run only data processing
   python main.py --data-only --verbose
   ```

### Error Recovery

If a stage fails, you can resume from where it left off:

```bash
# If data processing completed but feature engineering failed
python main.py  # Will skip data processing, retry feature engineering

# If you want to force restart from a specific stage
rm spx_engineered_data.csv  # Remove output to force re-run
python main.py
```

## 📈 Performance Optimization

### For Faster Execution
```bash
# Skip visualization for faster runs
python main.py --skip-backtest

# Use existing models
python main.py --skip-training
```

### For Development
```bash
# Data-only mode for feature development
python main.py --data-only --verbose

# Force retrain for model development
python main.py --force-retrain --log-level DEBUG
```

## 🎯 Expected Outputs

After successful pipeline completion, you should have:

### Data Files
- ✅ `spx_processed_data.csv` - Cleaned and processed data
- ✅ `spx_engineered_data.csv` - Engineered features dataset
- ✅ `feature_info.json` - Feature metadata and categories

### Model Files
- ✅ `best_model.pkl` - Trained LightGBM model
- ✅ `scaler.pkl` - Feature scaling parameters
- ✅ `selected_features.json` - Top 30 selected features

### Results Files
- ✅ `backtest_results.json` - Backtesting performance metrics
- ✅ `best_trades.csv` - Detailed trade records
- ✅ `strategy_params.json` - Optimal strategy parameters

### Visualization Files
- ✅ `trading_performance.png` - Performance charts
- ✅ `spx_price_analysis.png` - Price analysis charts
- ✅ `options_flow_analysis.png` - Options flow charts
- ✅ `correlation_analysis.png` - Feature correlation heatmap

## 🚀 Next Steps

After pipeline completion:

1. **Review Results**: Check `backtest_results.json` for performance metrics
2. **Analyze Trades**: Examine `best_trades.csv` for trade details
3. **Run Predictions**: Use `python demo_prediction_system.py`
4. **Customize Strategy**: Modify parameters in `config.py`
5. **Deploy**: Integrate with your trading system

## 💡 Tips

- **First Run**: Use `python main.py --verbose` to see detailed progress
- **Development**: Use `--data-only` for faster iteration on features
- **Production**: Use `python main.py` with default settings
- **Debugging**: Use `--log-file` to save detailed logs
- **Memory**: Monitor system resources during training stages
