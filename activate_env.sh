#!/bin/bash

# SPX Predictive System - Virtual Environment Activation Script
# =============================================================

echo "=== SPX Predictive System Environment Setup ==="

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "❌ Virtual environment not found!"
    echo "Please run: python3 -m venv .venv"
    echo "Then run this script again."
    exit 1
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source .venv/bin/activate

# Verify activation
if [ "$VIRTUAL_ENV" != "" ]; then
    echo "✅ Virtual environment activated successfully!"
    echo "📍 Virtual environment path: $VIRTUAL_ENV"
    echo "🐍 Python version: $(python --version)"
    echo ""
    
    # Check if dependencies are installed
    echo "🔍 Checking key dependencies..."
    python -c "
import sys
packages = ['pandas', 'numpy', 'sklearn', 'xgboost', 'lightgbm', 'matplotlib', 'seaborn']
package_names = ['pandas', 'numpy', 'scikit-learn', 'xgboost', 'lightgbm', 'matplotlib', 'seaborn']
missing = []
for i, pkg in enumerate(packages):
    try:
        __import__(pkg)
        print(f'✅ {package_names[i]}')
    except ImportError:
        missing.append(package_names[i])
        print(f'❌ {package_names[i]}')

if missing:
    print(f'\n⚠️  Missing packages: {missing}')
    print('Run: pip install -r requirements.txt')
else:
    print('\n🎉 All key dependencies are installed!')
"
    
    echo ""
    echo "=== USAGE INSTRUCTIONS ==="
    echo ""
    echo "📊 To run the main prediction system:"
    echo "   python demo_prediction_system.py"
    echo ""
    echo "🔧 To test the configuration:"
    echo "   python config.py"
    echo ""
    echo "📈 To run backtesting:"
    echo "   python backtesting.py"
    echo ""
    echo "🔬 To develop models:"
    echo "   python model_development_fixed.py"
    echo ""
    echo "📋 To view current configuration:"
    echo "   python -c \"from config import config; config.print_config()\""
    echo ""
    echo "🚪 To deactivate the environment:"
    echo "   deactivate"
    echo ""
    echo "=== CONFIGURATION ==="
    echo "The system now uses a centralized configuration in config.py"
    echo "All file paths are configurable and default to the current directory."
    echo "You can modify the base directory by editing config.py or creating"
    echo "a custom Config instance with a different base_dir parameter."
    echo ""
    
else
    echo "❌ Failed to activate virtual environment!"
    exit 1
fi
