(self.webpackChunkcatboost_widget=self.webpackChunkcatboost_widget||[]).push([[138],{214:(t,e,a)=>{"use strict";a.d(e,{Z:()=>n});var s=a(645),o=a.n(s)()((function(t){return t[1]}));o.push([t.id,".highcharts-tooltip {\n    display: none !important;\n}\n.highcharts-halo {\n    display: none !important;\n}\n\n.catboost {\n    position: relative;\n}\n\n.catboost-panel {\n    position: absolute;\n    height: 100%;\n    width: 280px;\n}\n\n.catboost-panel__controls {\n    margin-left: 0;\n}\n\n.catboost-panel__controls_label {\n    padding: 5px 0 0 8px;\n    cursor: pointer;\n    width: 80px;\n    box-sizing: content-box;\n}\n.catboost-panel__controls_label_time {\n    width: inherit;\n}\n\n.catboost-panel__controls2 {\n    margin-top: 10px;\n}\n\n.catboost-panel__controls2_label {\n    padding: 5px 11px 0 8px;\n    cursor: pointer;\n    width: 90px;\n    box-sizing: content-box;\n}\n.catboost-panel__controls2_label-long {\n    width: 170px;\n}\n\n.catboost-panel__series {\n    height: 340px;\n    overflow-y: auto;\n}\n\n.catboost-graph {\n    margin-left: 290px;\n}\n\n.catboost-graph__tabs {\n    padding: 0 0 0 20px;\n}\n\n.catboost-graph__tab {\n    display: inline-block;\n    padding: 5px 10px 0 0;\n}\n\n.catboost-graph__tab {\n    color: #999;\n    cursor: pointer;\n    transition: color 0.1s linear;\n}\n\n.catboost-graph__tab:hover {\n    color: #333;\n}\n\n.catboost-graph__tab_active {\n    color: #000;\n    cursor: auto;\n}\n\n.catboost-graph__charts {\n    padding-top: 20px;\n}\n\n.catboost-graph__chart {\n    display: none;\n}\n\n.catboost-graph__chart_active {\n    display: block;\n}\n\n.catboost-panel__serie {\n    padding-bottom: 5px;\n    border-bottom: 1px solid rgba(0, 0, 0, 0.3);\n    position: relative;\n}\n\n.catboost-panel__serie_bottom,\n.catboost-panel__serie_middle,\n.catboost-panel__serie_top {\n    white-space: nowrap;\n    position: relative;\n}\n\n#catboost-control-test {\n    margin-left: 11px;\n}\n\n.catboost-panel__serie_label {\n    padding: 0 0 0 8px;\n    width: 200px;\n    text-overflow: ellipsis;\n    box-sizing: border-box;\n    cursor: pointer;\n    margin-bottom: 0;\n    overflow: hidden;\n    position: relative;\n    top: 5px;\n}\n\n.catboost-panel__serie_hint {\n    position: absolute;\n    font-size: 9px;\n    left: 0;\n}\n\n.catboost-panel__serie__learn_hint {\n    top: 56px;\n}\n\n.catboost-panel__serie__test_hint {\n    top: 82px;\n}\n\n.catboost-panel__serie_bottom {\n    padding-bottom: 6px;\n}\n\n.catboost-panel__serie_time {\n    position: absolute;\n    top: 5px;\n    right: 2px;\n    height: 20px;\n    padding: 0 0 0 20px;\n    margin-bottom: 3px;\n    overflow: hidden;\n\n    text-overflow: ellipsis;\n    box-sizing: border-box;\n    text-align: left;\n}\n\n.catboost-panel__serie_learn_pic,\n.catboost-panel__serie_test_pic {\n    width: 13px;\n    height: 1px;\n    border-top-width: 1px;\n    position: relative;\n    top: -3px;\n    margin-right: 5px;\n}\n\n.catboost-panel__serie_learn_pic {\n    border-top-style: dashed;\n}\n\n.catboost-panel__serie_test_pic {\n    border-top-style: solid;\n}\n\n.catboost-panel__serie-value {\n    display: inline-block;\n    min-width: 30px;\n    margin-right: 2px;\n}\n\n.catboost-panel__controls_label .catboost-panel__serie_learn_pic {\n    padding-left: 4px;\n}\n\n.catboost-panel__serie_names {\n    white-space: nowrap;\n}\n\n.catboost-panel__serie_scroll {\n    width: 240px;\n    overflow-x: auto;\n    margin-left: 20px;\n}\n\n.catboost-panel__serie_learn_name,\n.catboost-panel__serie_test_name,\n.catboost-panel__serie_learn_value,\n.catboost-panel__serie_test_value,\n.catboost-panel__serie_best_learn_value,\n.catboost-panel__serie_best_test_value {\n    width: 85px;\n    position: relative;\n    padding: 0 8px 0 0;\n    box-sizing: content-box;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    top: 5px;\n}\n\n.catboost-panel__serie_iteration,\n.catboost-panel__serie_best_iteration {\n    display: inline-block;\n    position: absolute;\n    box-sizing: content-box;\n    overflow: hidden;\n    right: 2px;\n}\n\n.catboost-panel__serie_iteration {\n    top: 55px;\n}\n\n.catboost-panel__serie_best_iteration {\n    top: 80px;\n}\n\n.catboost-panel__control_slider {\n    width: 100px !important;\n    margin-left: 0;\n    position: relative;\n    display: inline-block !important;\n    top: 3px;\n}\n\n.catboost-panel__control_slidervalue {\n    width: 50px;\n    padding: 2px 3px;\n    margin-left: 4px;\n}\n\n.catboost-panel__serie_time_spend,\n.catboost-panel__serie_time_left {\n    display: inline-block;\n}\n\n.catboost-panel__serie_time_left {\n    margin-left: 10px;\n}\n\n.catboost-panel__serie_learn_pic,\n.catboost-panel__serie_learn_name,\n.catboost-panel__serie_learn_value,\n.catboost-panel__serie_best_learn_value {\n    display: inline-block;\n}\n.catboost-panel__serie_test_pic,\n.catboost-panel__serie_test_name,\n.catboost-panel__serie_test_value,\n.catboost-panel__serie_best_test_value {\n    display: inline-block;\n}\n\n.catboost-panel__series_learn_disabled .catboost-panel__serie_learn_pic,\n.catboost-panel__series_learn_disabled .catboost-panel__serie_learn_name,\n.catboost-panel__series_learn_disabled .catboost-panel__serie_learn_value,\n.catboost-panel__series_learn_disabled .catboost-panel__serie_best_learn_value {\n    display: none;\n}\n.catboost-panel__series_test_disabled .catboost-panel__serie_test_pic,\n.catboost-panel__series_test_disabled .catboost-panel__serie_test_name,\n.catboost-panel__series_test_disabled .catboost-panel__serie_test_value,\n.catboost-panel__series_test_disabled .catboost-panel__serie_best_test_value {\n    display: none;\n}\n\n/*\n.catboost-panel__series_learn_disabled .catboost-panel__serie_test_value,\n.catboost-panel__series_learn_disabled .catboost-panel__serie_best_test_value {\n    width: 216px;\n}\n.catboost-panel__series_test_disabled .catboost-panel__serie_learn_value,\n.catboost-panel__series_test_disabled .catboost-panel__serie_best_learn_value {\n    width: 216px;\n}\n*/\n.catboost-panel__series_test_disabled .catboost-panel__serie__test_hint,\n.catboost-panel__series_test_disabled .catboost-panel__serie_best_iteration {\n    display: none;\n}\n\n.catboost-panel__series_test_disabled.catboost-panel__series_learn_disabled .catboost-panel__serie_middle {\n    display: none;\n}\n\n.catboost-panel__series_test_disabled .catboost-panel__serie_bottom {\n    display: none;\n}\n",""]);const n=o},645:t=>{"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var a=t(e);return e[2]?"@media ".concat(e[2]," {").concat(a,"}"):a})).join("")},e.i=function(t,a,s){"string"==typeof t&&(t=[[null,t,""]]);var o={};if(s)for(var n=0;n<this.length;n++){var i=this[n][0];null!=i&&(o[i]=!0)}for(var r=0;r<t.length;r++){var c=[].concat(t[r]);s&&o[c[0]]||(a&&(c[2]?c[2]="".concat(a," and ").concat(c[2]):c[2]=a),e.push(c))}},e}},78:(t,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>i});var s=a(379),o=a.n(s),n=a(214);o()(n.Z,{insert:"head",singleton:!1});const i=n.Z.locals||{}},379:(t,e,a)=>{"use strict";var s,o=function(){var t={};return function(e){if(void 0===t[e]){var a=document.querySelector(e);if(window.HTMLIFrameElement&&a instanceof window.HTMLIFrameElement)try{a=a.contentDocument.head}catch(t){a=null}t[e]=a}return t[e]}}(),n=[];function i(t){for(var e=-1,a=0;a<n.length;a++)if(n[a].identifier===t){e=a;break}return e}function r(t,e){for(var a={},s=[],o=0;o<t.length;o++){var r=t[o],c=e.base?r[0]+e.base:r[0],l=a[c]||0,d="".concat(c," ").concat(l);a[c]=l+1;var p=i(d),h={css:r[1],media:r[2],sourceMap:r[3]};-1!==p?(n[p].references++,n[p].updater(h)):n.push({identifier:d,updater:v(h,e),references:1}),s.push(d)}return s}function c(t){var e=document.createElement("style"),s=t.attributes||{};if(void 0===s.nonce){var n=a.nc;n&&(s.nonce=n)}if(Object.keys(s).forEach((function(t){e.setAttribute(t,s[t])})),"function"==typeof t.insert)t.insert(e);else{var i=o(t.insert||"head");if(!i)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");i.appendChild(e)}return e}var l,d=(l=[],function(t,e){return l[t]=e,l.filter(Boolean).join("\n")});function p(t,e,a,s){var o=a?"":s.media?"@media ".concat(s.media," {").concat(s.css,"}"):s.css;if(t.styleSheet)t.styleSheet.cssText=d(e,o);else{var n=document.createTextNode(o),i=t.childNodes;i[e]&&t.removeChild(i[e]),i.length?t.insertBefore(n,i[e]):t.appendChild(n)}}function h(t,e,a){var s=a.css,o=a.media,n=a.sourceMap;if(o?t.setAttribute("media",o):t.removeAttribute("media"),n&&"undefined"!=typeof btoa&&(s+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(n))))," */")),t.styleSheet)t.styleSheet.cssText=s;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(s))}}var _=null,b=0;function v(t,e){var a,s,o;if(e.singleton){var n=b++;a=_||(_=c(e)),s=p.bind(null,a,n,!1),o=p.bind(null,a,n,!0)}else a=c(e),s=h.bind(null,a,e),o=function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(a)};return s(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;s(t=e)}else o()}}t.exports=function(t,e){(e=e||{}).singleton||"boolean"==typeof e.singleton||(e.singleton=(void 0===s&&(s=Boolean(window&&document&&document.all&&!window.atob)),s));var a=r(t=t||[],e);return function(t){if(t=t||[],"[object Array]"===Object.prototype.toString.call(t)){for(var s=0;s<a.length;s++){var o=i(a[s]);n[o].references--}for(var c=r(t,e),l=0;l<a.length;l++){var d=i(a[l]);0===n[d].references&&(n[d].updater(),n.splice(d,1))}a=c}}}},294:(t,e,a)=>{a(78);var s=a(395),o=a(439),n=a(261),i=a(646),r=a(147).version;function c(){}c.prototype.init=function(){this.charts={},this.traces={},this.hovertextParameters=[],this.chartsToRedraw={},this.lastIndexes={},this.smoothness=-1,this.layoutDisabled={series:{},traces:{}},this.clickMode=!1,this.logarithmMode="linear",this.lastSmooth=0,this.layout=null,this.activeTab="",this.meta={},this.timeLeft={},this.hasCVMode=!1,this.stddevEnabled=!1,this.colors=["#68E256","#56AEE2","#CF56E2","#E28956","#56E289","#5668E2","#E256AE","#E2CF56","#56E2CF","#8A56E2","#E25668","#AEE256"],this.colorsByPath={},this.colorIndex=0,this.lossFuncs={},this.isCVinited=!1},c.prototype.loadStyles=function(t,e,a){i('link[catboost="1"]').remove();var s,o,n=document.getElementsByTagName("head")[0],r=document.createElement("link");r.setAttribute("href",t),r.setAttribute("rel","stylesheet"),r.setAttribute("type","text/css"),r.setAttribute("catboost","1"),"sheet"in r?(s="sheet",o="cssRules"):(s="styleSheet",o="rules");var c=setInterval((function(){try{r[s]&&r[s][o].length&&(clearInterval(c),clearTimeout(l),e.call(a||window,!0,r))}catch(t){}}),50),l=setTimeout((function(){clearInterval(c),clearTimeout(l),n.removeChild(r),e.call(a||window,!1,r)}),15e3);return n.appendChild(r),r},c.prototype.resizeCharts=function(){i(".catboost-graph__charts",this.layout).css({width:i(".catboost-graph").width()}),this.plotly.Plots.resize(this.traces[this.activeTab].parent)},c.prototype.addMeta=function(t,e){this.meta[t]=e},c.prototype.addLayout=function(t){if(!this.layout){var e="";this.hasCVMode&&(e='<div><input type="checkbox" class="catboost-panel__control_checkbox" id="catboost-control2-cvstddev'+this.index+'"'+(this.stddevEnabled?' checked="checked"':"")+'></input><label for="catboost-control2-cvstddev'+this.index+'" class="catboost-panel__controls2_label catboost-panel__controls2_label-long">Standard Deviation</label></div>'),this.layout=i('<div class="catboost"><div class="catboost-panel"><div class="catboost-panel__controls"><input type="checkbox" class="catboost-panel__controls_checkbox" id="catboost-control-learn'+this.index+'" '+(this.layoutDisabled.learn?"":' checked="checked"')+'></input><label for="catboost-control-learn'+this.index+'" class="catboost-panel__controls_label"><div class="catboost-panel__serie_learn_pic" style="border-color:#999"></div>Learn</label><input type="checkbox" class="catboost-panel__controls_checkbox" id="catboost-control-test'+this.index+'" '+(this.layoutDisabled.test?"":' checked="checked"')+'></input><label for="catboost-control-test'+this.index+'" class="catboost-panel__controls_label"><div class="catboost-panel__serie_test_pic" style="border-color:#999"></div>Eval</label></div><div class="catboost-panel__series '+(this.layoutDisabled.learn?" catboost-panel__series_learn_disabled":"")+'"></div><div class="catboost-panel__controls2"><input type="checkbox" class="catboost-panel__control_checkbox" id="catboost-control2-clickmode'+this.index+'"></input><label for="catboost-control2-clickmode'+this.index+'" class="catboost-panel__controls2_label">Click Mode</label><input type="checkbox" class="catboost-panel__control_checkbox" id="catboost-control2-log'+this.index+'"></input><label for="catboost-control2-log'+this.index+'" class="catboost-panel__controls2_label">Logarithm</label><div><input type="checkbox" class="catboost-panel__control_checkbox" id="catboost-control2-smooth'+this.index+'"></input><label for="catboost-control2-smooth'+this.index+'" class="catboost-panel__controls2_label">Smooth</label><input id="catboost-control2-slider'+this.index+'" disabled="disabled" class="catboost-panel__control_slider" type ="range" value="0" min="0" max="1" step ="0.01" for="rangeInputValue" name="rangeInput"/><input id="catboost-control2-slidervalue'+this.index+'" disabled="disabled" class="catboost-panel__control_slidervalue" value="0" min="0" max="1" for="rangeInput" name="rangeInputValue"/></div>'+e+'</div></div><div class="catboost-graph"><div class="catboost-graph__tabs"></div><div class="catboost-graph__charts"></div></div></div>'),i(t).append(this.layout),this.addTabEvents(),this.addControlEvents()}},c.prototype.addTabEvents=function(){var t=this;i(".catboost-graph__tabs",this.layout).click((function(e){if(i(e.target).is(".catboost-graph__tab:not(.catboost-graph__tab_active)")){var a=i(e.target).attr("tabid");t.activeTab=a,i(".catboost-graph__tab_active",t.layout).removeClass("catboost-graph__tab_active"),i(".catboost-graph__chart_active",t.layout).removeClass("catboost-graph__chart_active"),i('.catboost-graph__tab[tabid="'+a+'"]',t.layout).addClass("catboost-graph__tab_active"),i('.catboost-graph__chart[tabid="'+a+'"]',t.layout).addClass("catboost-graph__chart_active"),t.cleanSeries(),t.redrawActiveChart(),t.resizeCharts()}}))},c.prototype.addControlEvents=function(){var t=this;i("#catboost-control-learn"+this.index,this.layout).click((function(){t.layoutDisabled.learn=!i(this)[0].checked,i(".catboost-panel__series",t.layout).toggleClass("catboost-panel__series_learn_disabled",t.layoutDisabled.learn),t.redrawActiveChart()})),i("#catboost-control-test"+this.index,this.layout).click((function(){t.layoutDisabled.test=!i(this)[0].checked,i(".catboost-panel__series",t.layout).toggleClass("catboost-panel__series_test_disabled",t.layoutDisabled.test),t.redrawActiveChart()})),i("#catboost-control2-clickmode"+this.index,this.layout).click((function(){t.clickMode=i(this)[0].checked})),i("#catboost-control2-log"+this.index,this.layout).click((function(){t.logarithmMode=i(this)[0].checked?"log":"linear",t.forEveryLayout((function(e){e.yaxis={type:t.logarithmMode}})),t.redrawActiveChart()}));var e=i("#catboost-control2-slider"+this.index),a=i("#catboost-control2-slidervalue"+this.index);i("#catboost-control2-smooth"+this.index,this.layout).click((function(){var s=i(this)[0].checked;t.setSmoothness(s?t.lastSmooth:-1),e.prop("disabled",!s),a.prop("disabled",!s),t.redrawActiveChart()})),i("#catboost-control2-cvstddev"+this.index,this.layout).click((function(){var e=i(this)[0].checked;t.setStddev(e),t.redrawActiveChart()})),e.on("input change",(function(){var e=Number(i(this).val());a.val(isNaN(e)?0:e),t.setSmoothness(e),t.lastSmooth=e,t.redrawActiveChart()})),a.on("input change",(function(){var a=Number(i(this).val());e.val(isNaN(a)?0:a),t.setSmoothness(a),t.lastSmooth=a,t.redrawActiveChart()}))},c.prototype.setTraceVisibility=function(t,e){t&&(t.visible=e)},c.prototype.updateTracesVisibility=function(){var t,e=this.groupTraces(),a=-1===this.getSmoothness(),s=this;for(var o in e)e.hasOwnProperty(o)&&(t=e[o].traces,this.layoutDisabled.traces[o]?t.forEach((function(t){s.setTraceVisibility(t,!1)})):(t.forEach((function(t){s.setTraceVisibility(t,!0)})),this.hasCVMode&&(this.stddevEnabled?(s.filterTracesOne(t,{type:"learn"}).forEach((function(t){s.setTraceVisibility(t,!1)})),s.filterTracesOne(t,{type:"test"}).forEach((function(t){s.setTraceVisibility(t,!1)})),s.filterTracesEvery(t,this.getTraceDefParams({type:"learn",cv_avg:!0})).forEach((function(t){s.setTraceVisibility(t,!0)})),s.filterTracesEvery(t,this.getTraceDefParams({type:"test",cv_avg:!0})).forEach((function(t){s.setTraceVisibility(t,!0)})),s.filterTracesEvery(t,this.getTraceDefParams({type:"learn",cv_avg:!0,smoothed:!0})).forEach((function(t){s.setTraceVisibility(t,!0)})),s.filterTracesEvery(t,this.getTraceDefParams({type:"test",cv_avg:!0,smoothed:!0})).forEach((function(t){s.setTraceVisibility(t,!0)})),s.filterTracesEvery(t,this.getTraceDefParams({type:"test",cv_avg:!0,best_point:!0})).forEach((function(t){s.setTraceVisibility(t,!0)})),s.filterTracesOne(t,{cv_stddev_first:!0}).forEach((function(t){s.setTraceVisibility(t,!0)})),s.filterTracesOne(t,{cv_stddev_last:!0}).forEach((function(t){s.setTraceVisibility(t,!0)}))):(s.filterTracesOne(t,{cv_stddev_first:!0}).forEach((function(t){s.setTraceVisibility(t,!1)})),s.filterTracesOne(t,{cv_stddev_last:!0}).forEach((function(t){s.setTraceVisibility(t,!1)})),s.filterTracesEvery(t,this.getTraceDefParams({type:"learn",cv_avg:!0})).forEach((function(t){s.setTraceVisibility(t,!1)})),s.filterTracesEvery(t,this.getTraceDefParams({type:"test",cv_avg:!0})).forEach((function(t){s.setTraceVisibility(t,!1)})),s.filterTracesEvery(t,this.getTraceDefParams({type:"learn",cv_avg:!0,smoothed:!0})).forEach((function(t){s.setTraceVisibility(t,!1)})),s.filterTracesEvery(t,this.getTraceDefParams({type:"test",cv_avg:!0,smoothed:!0})).forEach((function(t){s.setTraceVisibility(t,!1)})),s.filterTracesEvery(t,this.getTraceDefParams({type:"test",cv_avg:!0,best_point:!0})).forEach((function(t){s.setTraceVisibility(t,!1)})))),a&&s.filterTracesOne(t,{smoothed:!0}).forEach((function(t){s.setTraceVisibility(t,!1)})),this.layoutDisabled.learn&&s.filterTracesOne(t,{type:"learn"}).forEach((function(t){s.setTraceVisibility(t,!1)})),this.layoutDisabled.test&&s.filterTracesOne(t,{type:"test"}).forEach((function(t){s.setTraceVisibility(t,!1)}))))},c.prototype.getSmoothness=function(){return this.smoothness&&this.smoothness>-1?this.smoothness:-1},c.prototype.setSmoothness=function(t){t<0&&-1!==t||t>1||(this.smoothness=t)},c.prototype.setStddev=function(t){this.stddevEnabled=t},c.prototype.redrawActiveChart=function(){this.chartsToRedraw[this.activeTab]=!0,this.redrawAll()},c.prototype.redraw=function(){this.chartsToRedraw[this.activeTab]&&(this.chartsToRedraw[this.activeTab]=!1,this.updateTracesVisibility(),this.updateTracesCV(),this.updateTracesBest(),this.updateTracesValues(),this.updateTracesSmoothness(),this.plotly.redraw(this.traces[this.activeTab].parent)),this.drawTraces()},c.prototype.addRedrawFunc=function(){this.redrawFunc=function(t,e,a,s){var o=typeof a;"undefined"===o?a=!0:3===arguments.length&&"boolean"!==o&&(s=a,a=!0);var n,i,r,c=function(){r?(t.apply(s,i),r=!1,n=setTimeout(c,e)):n=null};return function(){i=arguments,s||(s=this),r=!0,n||(a?c():n=setTimeout(c,e))}}(this.redraw,400,!1,this)},c.prototype.redrawAll=function(){this.redrawFunc||this.addRedrawFunc(),this.redrawFunc()},c.prototype.addPoints=function(t,e){var a=this;e.chunks.forEach((function(s){void 0!==s.remaining_time&&void 0!==s.passed_time&&(a.timeLeft[e.path]||(a.timeLeft[e.path]=[]),a.timeLeft[e.path][s.iteration]=[s.remaining_time,s.passed_time]),["test","learn"].forEach((function(o){for(var n=a.meta[e.path][o+"_sets"],r=a.meta[e.path][o+"_metrics"],c=0;c<r.length;c++){var l=r[c].name,d=!1;hovertextParametersAdded=!1,a.lossFuncs[l]=r[c].best_value;for(var p=0;p<n.length;p++){var h=n[p],_={chartName:l,index:c,train:e.train,type:o,path:e.path,indexOfSet:p,nameOfSet:h},b=a.getKey(_),v=a.getLaunchMode(e.path);a.activeTab||(a.activeTab=b.chartId),"CV"===v&&(a.hasCVMode=!0,a.isCVinited||(a.layoutDisabled.learn=!0,a.setStddev(!0),a.isCVinited=!0));var u=s[h],f=u[c],m=s.iteration,y=a.getTrace(t,_),g=a.getTrace(t,i.extend({smoothed:!0},_)),x=null;if("test"===o&&("CV"!==v&&a.getTrace(t,i.extend({best_point:!0},_)),"number"==typeof a.lossFuncs[l]&&(x=a.getTrace(t,i.extend({best_value:!0},_)))),"inf"!==f&&"nan"!==f){if(y.x[m]=m,y.y[m]=u[c],y.hovertext[m]=h+": "+u[c].toPrecision(7),s.hasOwnProperty("parameters")){for(var T in a.hovertextParameters[m]="",s.parameters[0])s.parameters[0].hasOwnProperty(T)&&(valueOfParameter=s.parameters[0][T],a.hovertextParameters[m]+="<br>"+T+" : "+valueOfParameter);hovertextParametersAdded||"test"!==o||(hovertextParametersAdded=!0,y.hovertext[m]+=a.hovertextParameters[m])}g.x[m]=m}x&&(x.x[m]=m,x.y[m]=a.lossFuncs[l]),"CV"!==v||d||(d=!0,a.getTrace(t,i.extend({cv_stddev_first:!0},_)),a.getTrace(t,i.extend({cv_stddev_last:!0},_)),a.getTrace(t,i.extend({cv_stddev_first:!0,smoothed:!0},_)),a.getTrace(t,i.extend({cv_stddev_last:!0,smoothed:!0},_)),a.getTrace(t,i.extend({cv_avg:!0},_)),a.getTrace(t,i.extend({cv_avg:!0,smoothed:!0},_)),"test"===o&&a.getTrace(t,i.extend({cv_avg:!0,best_point:!0},_)))}a.chartsToRedraw[b.chartId]=!0,a.redrawAll()}}))}))},c.prototype.getLaunchMode=function(t){return this.meta[t].launch_mode},c.prototype.getChartNode=function(t,e){var a=i('<div class="catboost-graph__chart" tabid="'+t.id+'"></div>');return e&&a.addClass("catboost-graph__chart_active"),a},c.prototype.getChartTab=function(t,e){var a=i('<div class="catboost-graph__tab" tabid="'+t.id+'">'+t.name+"</div>");return e&&a.addClass("catboost-graph__tab_active"),a},c.prototype.forEveryChart=function(t){for(var e in this.traces)this.traces.hasOwnProperty(e)&&t(this.traces[e])},c.prototype.forEveryLayout=function(t){this.forEveryChart((function(e){t(e.layout)}))},c.prototype.getChart=function(t,e){var a=e.id,s=this;if(this.charts[a])return this.charts[a];this.addLayout(t);var o=this.activeTab===e.id,n=this.getChartNode(e,o),r=this.getChartTab(e,o);return i(".catboost-graph__charts",this.layout).append(n),i(".catboost-graph__tabs",this.layout).append(r),this.traces[a]={id:e.id,name:e.name,parent:n[0],traces:[],layout:{xaxis:{range:[0,Number(this.meta[e.path].iteration_count)],type:"linear",tickmode:"auto",showspikes:!0,spikethickness:1,spikedash:"longdashdot",spikemode:"across",zeroline:!1,showgrid:!1},yaxis:{zeroline:!1},separators:". ",margin:{l:38,r:0,t:35,b:30},autosize:!0,showlegend:!1},options:{scrollZoom:!1,modeBarButtonsToRemove:["toggleSpikelines"],displaylogo:!1}},this.charts[a]=this.plotly.plot(n[0],this.traces[a].traces,this.traces[a].layout,this.traces[a].options),n[0].on("plotly_hover",(function(t){s.updateTracesValues(t.points[0].x)})),n[0].on("plotly_click",(function(t){s.updateTracesValues(t.points[0].x,!0)})),this.charts[a]},c.prototype.getTrace=function(t,e){var a=this.getKey(e),s=[];if(this.traces[a.chartId]&&(s=this.traces[a.chartId].traces.filter((function(t){return t.name===a.traceName}))),s.length)return s[0];this.getChart(t,{id:a.chartId,name:e.chartName,path:e.path});var o={color:this.getNextColor(e.path,e.smoothed?.2:1),fillsmoothcolor:this.getNextColor(e.path,.1),fillcolor:this.getNextColor(e.path,.4),hoverinfo:e.cv_avg?"skip":"text+x",width:e.cv_avg?2:1,dash:"test"===e.type?"solid":"dot"},n={name:a.traceName,_params:e,x:[],y:[],hovertext:[],hoverinfo:o.hoverinfo,line:{width:o.width,dash:o.dash,color:o.color},mode:"lines",hoveron:"points",connectgaps:!0};return e.best_point&&(n={name:a.traceName,_params:e,x:[],y:[],marker:{width:2,color:o.color},hovertext:[],hoverinfo:"text",mode:"markers",type:"scatter"}),e.best_value&&(n={name:a.traceName,_params:e,x:[],y:[],line:{width:1,dash:"dash",color:"#CCCCCC"},mode:"lines",connectgaps:!0,hoverinfo:"skip"}),e.cv_stddev_last&&(n.fill="tonexty"),n._params.plotParams=o,this.traces[a.chartId].traces.push(n),n},c.prototype.getKey=function(t){var e=[t.train,t.type,t.indexOfSet,t.smoothed?"smoothed":"",t.best_point?"best_pount":"",t.best_value?"best_value":"",t.cv_avg?"cv_avg":"",t.cv_stddev_first?"cv_stddev_first":"",t.cv_stddev_last?"cv_stddev_last":""].join(";");return{chartId:t.chartName,traceName:e,colorId:t.train}},c.prototype.filterTracesEvery=function(t,e){return(t=t||this.traces[this.activeTab].traces).filter((function(t){for(var a in e)if(e.hasOwnProperty(a)&&e[a]!==t._params[a])return!1;return!0}))},c.prototype.filterTracesOne=function(t,e){return(t=t||this.traces[this.activeTab].traces).filter((function(t){for(var a in e)if(e.hasOwnProperty(a)&&e[a]===t._params[a])return!0;return!1}))},c.prototype.cleanSeries=function(){i(".catboost-panel__series",this.layout).html("")},c.prototype.groupTraces=function(){var t=this.traces[this.activeTab].traces,e=0,a={};return t.map((function(t){var s=t._params.train;a[s]||(a[s]={index:e,traces:[],info:{path:t._params.path,color:t._params.plotParams.color}},e++),a[s].traces.push(t)})),a},c.prototype.drawTraces=function(){var t="",e=this.groupTraces(),a=i(".catboost-panel__series .catboost-panel__serie",this.layout).length;if(Object.keys(e).filter(hasOwnProperty.bind(e)).length!==a){for(var s in e)e.hasOwnProperty(s)&&(t+=this.drawTrace(s,e[s]));i(".catboost-panel__series",this.layout).html(t),this.updateTracesValues(),this.addTracesEvents()}},c.prototype.getTraceDefParams=function(t){var e={smoothed:void 0,best_point:void 0,best_value:void 0,cv_avg:void 0,cv_stddev_first:void 0,cv_stddev_last:void 0};return t?i.extend(e,t):e},c.prototype.drawTrace=function(t,e){var a=e.info,s="catboost-serie-"+this.index+"-"+e.index,o={learn:this.filterTracesEvery(e.traces,this.getTraceDefParams({type:"learn"})),test:this.filterTracesEvery(e.traces,this.getTraceDefParams({type:"test"}))},n={learn:{middle:"",bottom:""},test:{middle:"",bottom:""}},i="";return["learn","test"].forEach((function(t){o[t].forEach((function(e){n[t].middle+='<div class="catboost-panel__serie_'+t+'_pic" style="border-color:'+a.color+'"></div><div data-index="'+e._params.indexOfSet+'" class="catboost-panel__serie_'+t+'_value"></div>',n[t].bottom+='<div class="catboost-panel__serie_'+t+'_pic" style="border-color:transparent"></div><div data-index="'+e._params.indexOfSet+'" class="catboost-panel__serie_best_'+t+'_value"></div>',i+='<div class="catboost-panel__serie_'+t+'_pic" style="border-color:'+a.color+'"></div><div class="catboost-panel__serie_'+t+'_name">'+e._params.nameOfSet+"</div>"}))})),'<div id="'+s+'" class="catboost-panel__serie" style="color:'+a.color+'"><div class="catboost-panel__serie_top"><input type="checkbox" data-seriename="'+t+'" class="catboost-panel__serie_checkbox" id="'+s+'-box" '+(this.layoutDisabled.series[t]?"":'checked="checked"')+"></input><label title="+this.meta[a.path].name+' for="'+s+'-box" class="catboost-panel__serie_label">'+t+'<div class="catboost-panel__serie_time_left" title="Estimate time"></div></label>'+("Eval"!==this.getLaunchMode(a.path)?'<div class="catboost-panel__serie_time"><div class="catboost-panel__serie_time_spend" title="Time spend"></div></div>':"")+'</div><div class="catboost-panel__serie_hint catboost-panel__serie__learn_hint">curr</div><div class="catboost-panel__serie_hint catboost-panel__serie__test_hint">best</div><div class="catboost-panel__serie_iteration" title="curr iteration"></div><div class="catboost-panel__serie_best_iteration" title="best '+(this.hasCVMode?"avg ":"")+'iteration"></div><div class="catboost-panel__serie_scroll"><div class="catboost-panel__serie_names">'+i+'</div><div class="catboost-panel__serie_middle">'+n.learn.middle+n.test.middle+'</div><div class="catboost-panel__serie_bottom">'+n.learn.bottom+n.test.bottom+"</div></div></div>"},c.prototype.updateTracesValues=function(t,e){var a=this.groupTraces();for(var s in a)a.hasOwnProperty(s)&&!this.layoutDisabled.traces[s]&&this.updateTraceValues(s,a[s],t,e)},c.prototype.updateTracesBest=function(){var t=this.groupTraces();for(var e in t)t.hasOwnProperty(e)&&!this.layoutDisabled.traces[e]&&this.updateTraceBest(e,t[e])},c.prototype.getBestValue=function(t){if(!t.length)return{best:void 0,index:-1};for(var e=t[0],a=0,s=this.lossFuncs[this.traces[this.activeTab].name],o="number"==typeof s?Math.abs(t[0]-s):0,n=1,i=t.length;n<i;n++)"Min"===s&&t[n]<e&&(e=t[n],a=n),"Max"===s&&t[n]>e&&(e=t[n],a=n),"number"==typeof s&&Math.abs(t[n]-s)<o&&(e=t[n],o=Math.abs(t[n]-s),a=n);return{best:e,index:a,func:s}},c.prototype.updateTracesCV=function(){this.updateTracesCVAvg(),this.hasCVMode&&this.stddevEnabled&&this.updateTracesCVStdDev()},c.prototype.updateTracesCVAvg=function(){var t=this.groupTraces(),e=this.filterTracesEvery(t.traces,this.getTraceDefParams({cv_avg:!0})),a=this;e.forEach((function(e){var s=a.filterTracesEvery(t.traces,a.getTraceDefParams({train:e._params.train,type:e._params.type,smoothed:e._params.smoothed}));s.length&&a.cvAvgFunc(s,e)}))},c.prototype.cvAvgFunc=function(t,e){var a,s,o=t.length,n=-1;t.forEach((function(t){t.y.length>n&&(n=t.y.length)}));for(var i=0;i<n;i++){s=0,a=0;for(var r=0;r<o;r++)void 0!==t[r].y[i]&&(s+=t[r].y[i],a++);a>0&&(e.x[i]=i,e.y[i]=s/a)}},c.prototype.updateTracesCVStdDev=function(){var t=this.groupTraces(),e=this.filterTracesOne(t.traces,{cv_stddev_first:!0}),a=this;e.forEach((function(e){var s=a.filterTracesEvery(t.traces,a.getTraceDefParams({train:e._params.train,type:e._params.type,smoothed:e._params.smoothed})),o=a.filterTracesEvery(t.traces,a.getTraceDefParams({train:e._params.train,type:e._params.type,smoothed:e._params.smoothed,cv_stddev_last:!0}));s.length&&1===o.length&&a.cvStdDevFunc(s,e,o[0])}))},c.prototype.cvStdDevFunc=function(t,e,a){var s,o,n,i,r=t.length,c=-1;for(t.forEach((function(t){t.y.length>c&&(c=t.y.length)})),n=0;n<c;n++){for(o=0,s=0,i=0;i<r;i++)void 0!==t[i].y[n]&&(o+=t[i].y[n],s++);if(!(s<=0)){var l=0,d=o/s;for(i=0;i<r;i++)void 0!==t[i].y[n]&&(l+=Math.pow(t[i].y[n]-d,2));l/=s-1,l=Math.pow(l,.5),e.x[n]=n,e.y[n]=d-l,e.hovertext[n]=e._params.type+" std: "+d.toFixed(7)+"-"+l.toFixed(7),a.x[n]=n,a.y[n]=d+l,a.hovertext[n]=a._params.type+" std: "+d.toFixed(7)+"+"+l.toFixed(7),this.hovertextParameters.length>n&&(e.hovertext[n]+=this.hovertextParameters[n],a.hovertext[n]+=this.hovertextParameters[n])}}},c.prototype.updateTracesSmoothness=function(){var t=this.groupTraces(),e=this.filterTracesOne(t.traces,{smoothed:!0}),a=this.getSmoothness()>-1,s=this;e.forEach((function(e){var o=s.filterTracesEvery(t.traces,s.getTraceDefParams({train:e._params.train,type:e._params.type,indexOfSet:e._params.indexOfSet,cv_avg:e._params.cv_avg,cv_stddev_first:e._params.cv_stddev_first,cv_stddev_last:e._params.cv_stddev_last})),n=!1;1===o.length&&(o=o[0]).visible&&(a&&(s.smoothFunc(o,e),n=!0),s.highlightSmoothedTrace(o,e,n))}))},c.prototype.highlightSmoothedTrace=function(t,e,a){a?(e.line.color=t._params.plotParams.color,t.line.color=e._params.plotParams.color,t.hoverinfo="skip",t._params.cv_stddev_last&&(t.fillcolor=t._params.plotParams.fillsmoothcolor)):(t.line.color=t._params.plotParams.color,t.hoverinfo=t._params.plotParams.hoverinfo,t._params.cv_stddev_last&&(t.fillcolor=t._params.plotParams.fillcolor))},c.prototype.smoothFunc=function(t,e){var a=t.y,s=this.smooth(a,this.getSmoothness()),o=0,n=this;s.length&&a.forEach((function(t,a){e.x[a]||(e.x[a]=a);var i=e._params.nameOfSet;(e._params.cv_stddev_first||e._params.cv_stddev_last)&&(i=e._params.type+" std"),e.y[a]=s[o],e.hovertext[a]=i+"`: "+s[o].toPrecision(7),n.hovertextParameters.length>a&&(e.hovertext[a]+=n.hovertextParameters[a]),o++}))},c.prototype.formatItemValue=function(t,e,a){return void 0===t?"":'<span title="'+(a=a||"")+"value "+t+'">'+t+"</span>"},c.prototype.updateTraceBest=function(t,e){var a=this.filterTracesOne(e.traces,{best_point:!0}),s=this;a.forEach((function(t){var a=s.filterTracesEvery(e.traces,s.getTraceDefParams({train:t._params.train,type:"test",indexOfSet:t._params.indexOfSet}));s.hasCVMode&&(a=s.filterTracesEvery(e.traces,s.getTraceDefParams({train:t._params.train,type:"test",cv_avg:!0})));var o=s.getBestValue(1===a.length?a[0].y:[]);-1!==o.index&&(t.x[0]=o.index,t.y[0]=o.best,t.hovertext[0]=o.func+" ("+(s.hasCVMode?"avg":t._params.nameOfSet)+"): "+o.index+" "+o.best)}))},c.prototype.updateTraceValues=function(t,e,a,s){var o="catboost-serie-"+this.index+"-"+e.index,n={learn:this.filterTracesEvery(e.traces,this.getTraceDefParams({type:"learn"})),test:this.filterTracesEvery(e.traces,this.getTraceDefParams({type:"test"}))},r=e.info.path,c=this;if(["learn","test"].forEach((function(t){n[t].forEach((function(n){var l=n.y||[],d=void 0!==a&&a<l.length-1?a:l.length-1,p=l.length?l[d]:void 0,h=c.filterTracesEvery(e.traces,c.getTraceDefParams({type:"test",indexOfSet:n._params.indexOfSet})),_=c.getBestValue(1===h.length?h[0].y:[]),b="",v="";!s&&c.clickMode||(i("#"+o+" .catboost-panel__serie_"+t+"_value[data-index="+n._params.indexOfSet+"]",c.layout).html(c.formatItemValue(p,d,t+" ")),i("#"+o+" .catboost-panel__serie_iteration",c.layout).html(d),c.timeLeft[r]&&c.timeLeft[r][l.length-1]&&(b=c.timeLeft[r][l.length-1][0]),i("#"+o+" .catboost-panel__serie_time_left",c.layout).html(b?"~"+c.convertTime(b):""),c.timeLeft[r]&&c.timeLeft[r][d]&&(v=c.timeLeft[r][d][1]),i("#"+o+" .catboost-panel__serie_time_spend",c.layout).html(c.convertTime(v)),i("#"+o+" .catboost-panel__serie_best_iteration",c.layout).html(_.index>-1?_.index:""),i("#"+o+" .catboost-panel__serie_best_test_value[data-index="+n._params.indexOfSet+"]",c.layout).html(c.formatItemValue(_.best,_.index,"best "+n._params.nameOfSet+" ")))}))})),this.hasCVMode){var l=this.filterTracesEvery(e.traces,this.getTraceDefParams({type:"test",cv_avg:!0})),d=this.getBestValue(1===l.length?l[0].y:[]);i("#"+o+" .catboost-panel__serie_best_iteration",this.layout).html(d.index>-1?d.index:"")}s&&(this.clickMode=!0,i("#catboost-control2-clickmode"+this.index,this.layout)[0].checked=!0)},c.prototype.addTracesEvents=function(){var t=this;i(".catboost-panel__serie_checkbox",this.layout).click((function(){var e=i(this).data("seriename");t.layoutDisabled.traces[e]=!i(this)[0].checked,t.redrawActiveChart()}))},c.prototype.getNextColor=function(t,e){var a;return this.colorsByPath[t]?a=this.colorsByPath[t]:(a=this.colors[this.colorIndex],this.colorsByPath[t]=a,this.colorIndex++,this.colorIndex>this.colors.length-1&&(this.colorIndex=0)),this.hexToRgba(a,e)},c.prototype.hexToRgba=function(t,e){t.length<6&&(t=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])/i,(function(t,e,a,s){return"#"+e+e+a+a+s+s})));var a=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})/i.exec(t);return"rgba("+parseInt(a[1],16)+","+parseInt(a[2],16)+","+parseInt(a[3],16)+","+e+")"},c.prototype.convertTime=function(t){if(!t)return"0s";var e=(t=Math.floor(1e3*t))%1e3,a=(t=parseInt(t/1e3,10))%60,s=(t=parseInt(t/60,10))%60,o=(t=parseInt(t/60,10))%24,n="";return o&&o>0&&(n+=o+"h ",a=0,e=0),s&&s>0&&(n+=s+"m ",e=0),a&&a>0&&(n+=a+"s "),e&&e>0&&(n+=e+"ms"),n.trim()},c.prototype.mean=function(t,e){var a,s=t.length,o=s,n=-1,i=0,r=function(t){return null===t?NaN:+t};if(null===e)for(;++n<s;)isNaN(a=r(t[n]))?--o:i+=a;else for(;++n<s;)isNaN(a=r(e(t[n],n,t)))?--o:i+=a;if(o)return i/o},c.prototype.smooth=function(t,e){var a=(Math.pow(1e3,e)-1)/999,s=Math.floor(t.length*a/2),o=[],n=this;return t.forEach((function(e,a){var i=Math.min(s,a,t.length-a-1),r=a-i,c=a+i+1,l=e;isFinite(l)?o.push(n.mean(t.slice(r,c).filter((function(t){return isFinite(t)})),null)):o.push(l)})),o};var l=function(t){var e=i(t).attr("catboost-id");return e?(e=e.replace("catboost_",""),window.catboostIpythonInstances[e]?window.catboostIpythonInstances[e]:null):null};class d extends s.DOMWidgetView{initialize(){s.DOMWidgetView.prototype.initialize.apply(this,arguments),window.catboostIpythonInstances||(window.catboostIpythonInstances={}),void 0===window.catboostIpythonIndex&&(window.catboostIpythonIndex=0);var t=l(this.el);t||(t=function(t){i(t).attr("catboost-id","catboost_"+window.catboostIpythonIndex);var e=new c;return e.index=catboostIpythonIndex,e.plotly=n,window.catboostIpythonInstances[window.catboostIpythonIndex]=e,window.catboostIpythonIndex++,e}(this.el)),t.init()}render(){this.value_changed(),this.model.on("change:value",this.value_changed,this)}update(){this.value_changed()}value_changed(){this.el.style.width=this.model.get("width"),this.el.style.height=this.model.get("height"),this.displayed.then(o.bind(this.render_charts,this))}process_all(t,e){var a=e.data;for(var s in a)a.hasOwnProperty(s)&&this.process_row(t,a[s])}process_row(t,e){var a=l(t),s=e.path,o=e.content,n=o.data.iterations,i=0,r=[];if(n&&n.length){a.lastIndex||(a.lastIndex={}),a.lastIndex[s]&&(i=a.lastIndex[s]+1),a.lastIndex[s]=n.length-1;for(var c=i;c<n.length;c++)r.push(n[c]);a.addMeta(e.path,o.data.meta),a.addPoints(t,{chunks:r,train:e.name,path:e.path})}}render_charts(){return this.process_all(this.el,{data:this.model.get("data")}),this}}class p extends s.DOMWidgetModel{defaults(){return Object.assign({},super.defaults(),{_model_name:"CatboostWidgetModel",_view_name:"CatboostWidgetView",_model_module:"catboost-widget",_view_module:"catboost-widget",_model_module_version:r,_view_module_version:r})}}t.exports={CatboostWidgetModel:p,CatboostWidgetView:d}},138:(t,e,a)=>{t.exports=a(294),t.exports.version=a(147).version},147:t=>{"use strict";t.exports={version:"1.2.8"}}}]);