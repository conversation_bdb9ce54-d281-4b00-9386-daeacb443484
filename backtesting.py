import pandas as pd
import numpy as np
import json
import pickle
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

print("=== SPX Option Trading Strategy Backtesting ===")

# Load data and models
print("\n1. Loading data and models...")
data = pd.read_csv('/home/<USER>/spx_engineered_data.csv')
data['date'] = pd.to_datetime(data['date'])

# Load trained model and scaler
with open('/home/<USER>/best_model.pkl', 'rb') as f:
    best_model = pickle.load(f)

with open('/home/<USER>/scaler.pkl', 'rb') as f:
    scaler = pickle.load(f)

with open('/home/<USER>/selected_features.json', 'r') as f:
    selected_features = json.load(f)

with open('/home/<USER>/model_results.json', 'r') as f:
    model_results = json.load(f)

print(f"Loaded data shape: {data.shape}")
print(f"Best model: {type(best_model).__name__}")
print(f"Selected features: {len(selected_features)}")

# 2. Prepare data for backtesting
print("\n2. Preparing data for backtesting...")

# Clean data similar to training
target_col = 'target_direction'
data_clean = data.dropna(subset=[target_col]).copy()

# Prepare features
X = data_clean[selected_features].copy()
X = X.replace([np.inf, -np.inf], np.nan)

for col in X.columns:
    if X[col].dtype in ['float64', 'int64']:
        X[col] = X[col].fillna(X[col].median())

# Scale features
X_scaled = scaler.transform(X)
X_scaled = np.nan_to_num(X_scaled, nan=0.0, posinf=1.0, neginf=-1.0)
X_scaled = pd.DataFrame(X_scaled, columns=selected_features, index=X.index)

# Get predictions and probabilities
predictions = best_model.predict(X_scaled)
probabilities = best_model.predict_proba(X_scaled)[:, 1]

# Add predictions to data
data_clean['prediction'] = predictions
data_clean['prediction_proba'] = probabilities
data_clean['actual_direction'] = data_clean[target_col]

print(f"Backtesting data shape: {data_clean.shape}")

# 3. Implement Trading Strategy
print("\n3. Implementing trading strategy...")

def calculate_trading_performance(data, confidence_threshold=0.6, stop_loss_pct=2.0, take_profit_pct=3.0):
    """
    Calculate trading performance with entry/exit rules
    
    Parameters:
    - confidence_threshold: Minimum prediction probability to enter trade
    - stop_loss_pct: Stop loss percentage
    - take_profit_pct: Take profit percentage
    """
    
    trades = []
    current_position = None
    
    for i in range(len(data) - 1):  # -1 because we need next day data
        row = data.iloc[i]
        next_row = data.iloc[i + 1]
        
        # Check if we should enter a trade
        if current_position is None:
            # Only trade if confidence is high enough
            if row['prediction_proba'] >= confidence_threshold:
                direction = 'long' if row['prediction'] == 1 else 'short'
                
                # Entry price (use next day's open)
                entry_price = next_row['spx_open']
                entry_date = next_row['date']
                
                current_position = {
                    'direction': direction,
                    'entry_price': entry_price,
                    'entry_date': entry_date,
                    'entry_index': i + 1,
                    'confidence': row['prediction_proba']
                }
        
        # Check if we should exit current position
        elif current_position is not None:
            entry_price = current_position['entry_price']
            direction = current_position['direction']
            
            # Calculate current P&L
            current_price = row['spx_close']
            
            if direction == 'long':
                pnl_pct = (current_price - entry_price) / entry_price * 100
            else:  # short
                pnl_pct = (entry_price - current_price) / entry_price * 100
            
            # Check exit conditions
            exit_trade = False
            exit_reason = None
            
            # Stop loss
            if pnl_pct <= -stop_loss_pct:
                exit_trade = True
                exit_reason = 'stop_loss'
            
            # Take profit
            elif pnl_pct >= take_profit_pct:
                exit_trade = True
                exit_reason = 'take_profit'
            
            # Time-based exit (max 3 days)
            elif i - current_position['entry_index'] >= 3:
                exit_trade = True
                exit_reason = 'time_exit'
            
            # Exit trade
            if exit_trade:
                trade_record = {
                    'entry_date': current_position['entry_date'],
                    'exit_date': row['date'],
                    'direction': direction,
                    'entry_price': entry_price,
                    'exit_price': current_price,
                    'pnl_pct': pnl_pct,
                    'pnl_points': current_price - entry_price if direction == 'long' else entry_price - current_price,
                    'days_held': i - current_position['entry_index'],
                    'exit_reason': exit_reason,
                    'confidence': current_position['confidence']
                }
                
                trades.append(trade_record)
                current_position = None
    
    return pd.DataFrame(trades)

# Run backtesting with different confidence thresholds
print("Running backtesting with different confidence thresholds...")

confidence_thresholds = [0.5, 0.55, 0.6, 0.65, 0.7]
backtest_results = {}

for threshold in confidence_thresholds:
    trades_df = calculate_trading_performance(
        data_clean, 
        confidence_threshold=threshold,
        stop_loss_pct=2.0,
        take_profit_pct=3.0
    )
    
    if len(trades_df) > 0:
        # Calculate performance metrics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl_pct'] > 0])
        losing_trades = len(trades_df[trades_df['pnl_pct'] <= 0])
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        avg_win = trades_df[trades_df['pnl_pct'] > 0]['pnl_pct'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl_pct'] <= 0]['pnl_pct'].mean() if losing_trades > 0 else 0
        
        total_return = trades_df['pnl_pct'].sum()
        avg_return_per_trade = trades_df['pnl_pct'].mean()
        
        # Risk metrics
        sharpe_ratio = trades_df['pnl_pct'].mean() / trades_df['pnl_pct'].std() if trades_df['pnl_pct'].std() > 0 else 0
        max_drawdown = trades_df['pnl_pct'].cumsum().expanding().max() - trades_df['pnl_pct'].cumsum()
        max_drawdown = max_drawdown.max() if len(max_drawdown) > 0 else 0
        
        backtest_results[threshold] = {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_return': total_return,
            'avg_return_per_trade': avg_return_per_trade,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'trades_df': trades_df
        }
    else:
        backtest_results[threshold] = {
            'total_trades': 0,
            'win_rate': 0,
            'total_return': 0,
            'avg_return_per_trade': 0,
            'avg_win': 0,
            'avg_loss': 0,
            'sharpe_ratio': 0,
            'max_drawdown': 0,
            'trades_df': pd.DataFrame()
        }

# 4. Performance Analysis
print("\n4. Performance Analysis:")
print("="*80)
print(f"{'Threshold':<12} {'Trades':<8} {'Win Rate':<10} {'Total Ret':<12} {'Avg Ret':<10} {'Sharpe':<8}")
print("="*80)

best_threshold = None
best_score = -np.inf

for threshold, results in backtest_results.items():
    # Combined score: win rate * total return (focusing on user's preferences)
    score = results['win_rate'] * results['total_return'] if results['total_trades'] > 0 else -np.inf
    
    if score > best_score:
        best_score = score
        best_threshold = threshold
    
    print(f"{threshold:<12.2f} {results['total_trades']:<8} {results['win_rate']:<10.3f} "
          f"{results['total_return']:<12.2f} {results['avg_return_per_trade']:<10.3f} "
          f"{results['sharpe_ratio']:<8.3f}")

print("="*80)
print(f"Best threshold: {best_threshold} (Score: {best_score:.3f})")

# 5. Detailed analysis of best strategy
if best_threshold and backtest_results[best_threshold]['total_trades'] > 0:
    print(f"\n5. Detailed Analysis - Best Strategy (Threshold: {best_threshold}):")
    
    best_results = backtest_results[best_threshold]
    best_trades = best_results['trades_df']
    
    print(f"Total Trades: {best_results['total_trades']}")
    print(f"Win Rate: {best_results['win_rate']:.3f} ({best_results['win_rate']*100:.1f}%)")
    print(f"Total Return: {best_results['total_return']:.2f}%")
    print(f"Average Return per Trade: {best_results['avg_return_per_trade']:.3f}%")
    print(f"Average Win: {best_results['avg_win']:.3f}%")
    print(f"Average Loss: {best_results['avg_loss']:.3f}%")
    print(f"Sharpe Ratio: {best_results['sharpe_ratio']:.3f}")
    print(f"Max Drawdown: {best_results['max_drawdown']:.2f}%")
    
    # Trade distribution
    print(f"\nTrade Distribution:")
    print(f"Long trades: {len(best_trades[best_trades['direction'] == 'long'])}")
    print(f"Short trades: {len(best_trades[best_trades['direction'] == 'short'])}")
    
    # Exit reasons
    print(f"\nExit Reasons:")
    exit_reasons = best_trades['exit_reason'].value_counts()
    for reason, count in exit_reasons.items():
        print(f"  {reason}: {count} ({count/len(best_trades)*100:.1f}%)")

# 6. Create visualizations
print("\n6. Creating performance visualizations...")

if best_threshold and len(backtest_results[best_threshold]['trades_df']) > 0:
    best_trades = backtest_results[best_threshold]['trades_df']
    
    # Create performance charts
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'Trading Strategy Performance (Threshold: {best_threshold})', fontsize=16, fontweight='bold')
    
    # Cumulative returns
    cumulative_returns = best_trades['pnl_pct'].cumsum()
    axes[0,0].plot(range(len(cumulative_returns)), cumulative_returns, linewidth=2, color='blue')
    axes[0,0].set_title('Cumulative Returns')
    axes[0,0].set_xlabel('Trade Number')
    axes[0,0].set_ylabel('Cumulative Return (%)')
    axes[0,0].grid(True, alpha=0.3)
    
    # Trade P&L distribution
    axes[0,1].hist(best_trades['pnl_pct'], bins=20, alpha=0.7, color='green', edgecolor='black')
    axes[0,1].axvline(x=0, color='red', linestyle='--', alpha=0.7)
    axes[0,1].set_title('Trade P&L Distribution')
    axes[0,1].set_xlabel('P&L (%)')
    axes[0,1].set_ylabel('Frequency')
    axes[0,1].grid(True, alpha=0.3)
    
    # Win rate by confidence
    confidence_bins = pd.cut(best_trades['confidence'], bins=5)
    win_rate_by_conf = best_trades.groupby(confidence_bins)['pnl_pct'].apply(lambda x: (x > 0).mean())
    axes[1,0].bar(range(len(win_rate_by_conf)), win_rate_by_conf.values, alpha=0.7, color='purple')
    axes[1,0].set_title('Win Rate by Confidence Level')
    axes[1,0].set_xlabel('Confidence Bin')
    axes[1,0].set_ylabel('Win Rate')
    axes[1,0].grid(True, alpha=0.3)
    
    # Days held distribution
    axes[1,1].hist(best_trades['days_held'], bins=range(0, 5), alpha=0.7, color='orange', edgecolor='black')
    axes[1,1].set_title('Days Held Distribution')
    axes[1,1].set_xlabel('Days Held')
    axes[1,1].set_ylabel('Frequency')
    axes[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('/home/<USER>/trading_performance.png', dpi=300, bbox_inches='tight')
    plt.close()

# 7. Save results
print("\n7. Saving results...")

# Save backtest results
backtest_summary = {}
for threshold, results in backtest_results.items():
    backtest_summary[str(threshold)] = {
        'total_trades': results['total_trades'],
        'win_rate': results['win_rate'],
        'total_return': results['total_return'],
        'avg_return_per_trade': results['avg_return_per_trade'],
        'sharpe_ratio': results['sharpe_ratio'],
        'max_drawdown': results['max_drawdown']
    }

with open('/home/<USER>/backtest_results.json', 'w') as f:
    json.dump(backtest_summary, f, indent=2)

# Save best trades
if best_threshold and len(backtest_results[best_threshold]['trades_df']) > 0:
    best_trades = backtest_results[best_threshold]['trades_df']
    best_trades.to_csv('/home/<USER>/best_trades.csv', index=False)

# Save strategy parameters
strategy_params = {
    'best_confidence_threshold': best_threshold,
    'stop_loss_pct': 2.0,
    'take_profit_pct': 3.0,
    'max_days_held': 3,
    'model_type': type(best_model).__name__,
    'selected_features': selected_features
}

with open('/home/<USER>/strategy_params.json', 'w') as f:
    json.dump(strategy_params, f, indent=2)

print("Results saved:")
print("  - backtest_results.json: Summary of all threshold tests")
print("  - best_trades.csv: Detailed trade records for best strategy")
print("  - strategy_params.json: Optimal strategy parameters")
print("  - trading_performance.png: Performance visualization")

print("\n=== Backtesting completed ===")

