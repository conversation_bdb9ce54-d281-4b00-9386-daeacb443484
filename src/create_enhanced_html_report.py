#!/usr/bin/env python3
"""
Enhanced HTML Report Generator for SPX + VIX System
==================================================

Creates a comprehensive HTML report showcasing the enhanced SPX + VIX system results.

Author: Manus AI
Date: June 23, 2025
"""

import pandas as pd
import numpy as np
import json
import pickle
from datetime import datetime
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

from config import config

def create_enhanced_html_report():
    """Create comprehensive HTML report for enhanced SPX + VIX system"""
    
    print("🎨 CREATING ENHANCED SPX + VIX HTML REPORT")
    print("=" * 80)
    
    try:
        # Load all data
        print("1. Loading system data...")
        
        # Load feature info
        with open('feature_info.json', 'r') as f:
            feature_info = json.load(f)
        
        # Load model results
        with open('model_results.json', 'r') as f:
            model_results = json.load(f)
        
        # Load selected features
        with open('selected_features.json', 'r') as f:
            selected_features = json.load(f)
        
        # Load backtest results
        with open('backtest_results.json', 'r') as f:
            backtest_results = json.load(f)
        
        # Load trades
        trades_df = pd.read_csv('best_trades.csv')
        trades_df['entry_date'] = pd.to_datetime(trades_df['entry_date'])
        trades_df['exit_date'] = pd.to_datetime(trades_df['exit_date'])
        
        print("   ✅ All data loaded successfully")
        
        # Analyze features
        vix_features = [f for f in selected_features if f.startswith('vix_')]
        spx_features = [f for f in selected_features if not f.startswith('vix_')]
        
        # Get best model info
        best_model = None
        best_accuracy = 0
        for model_name, metrics in model_results.items():
            if isinstance(metrics, dict) and 'test_accuracy' in metrics:
                if metrics['test_accuracy'] > best_accuracy:
                    best_accuracy = metrics['test_accuracy']
                    best_model = model_name
        
        # Get backtest metrics
        rsquared_metrics = backtest_results['rsquared_optimized']
        
        # Create HTML content
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced SPX + VIX Predictive System Report</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .header p {{
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        .content {{
            padding: 30px;
        }}
        .section {{
            margin-bottom: 40px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #2a5298;
        }}
        .section h2 {{
            margin-top: 0;
            color: #2a5298;
            font-size: 1.8em;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }}
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .metric-card {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-top: 4px solid #2a5298;
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            color: #2a5298;
            margin: 10px 0;
        }}
        .metric-label {{
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        .feature-list {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .feature-category {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .feature-category h3 {{
            margin-top: 0;
            color: #2a5298;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 10px;
        }}
        .feature-item {{
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
            font-family: monospace;
            font-size: 0.9em;
        }}
        .trades-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .trades-table th {{
            background: #2a5298;
            color: white;
            padding: 15px;
            text-align: left;
        }}
        .trades-table td {{
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
        }}
        .trades-table tr:hover {{
            background: #f8f9fa;
        }}
        .positive {{
            color: #28a745;
            font-weight: bold;
        }}
        .negative {{
            color: #dc3545;
            font-weight: bold;
        }}
        .highlight {{
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #e17055;
        }}
        .footer {{
            background: #2a5298;
            color: white;
            padding: 20px;
            text-align: center;
        }}
        .emoji {{
            font-size: 1.2em;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span class="emoji">🚀</span> Enhanced SPX + VIX Predictive System</h1>
            <p>Comprehensive Options Trading Analysis with VIX Integration</p>
            <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="content">
            <div class="highlight">
                <h2><span class="emoji">🎉</span> System Enhancement Summary</h2>
                <p><strong>Major Achievement:</strong> Successfully integrated VIX options data with SPX analysis, creating the first comprehensive SPX+VIX predictive system. VIX features now represent <strong>{len(vix_features)/len(selected_features)*100:.1f}%</strong> of the final model, providing crucial volatility regime information.</p>
            </div>
            
            <div class="section">
                <h2><span class="emoji">📊</span> Enhanced Dataset Overview</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">{feature_info['total_features']}</div>
                        <div class="metric-label">Total Features Created</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{feature_info['spx_features']}</div>
                        <div class="metric-label">SPX Features</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{feature_info['vix_features']}</div>
                        <div class="metric-label">VIX Features Added</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{len(selected_features)}</div>
                        <div class="metric-label">Features Selected</div>
                    </div>
                </div>
                <p><strong>Enhancement Impact:</strong> Added {feature_info['vix_features']} VIX-specific features ({feature_info['vix_features']/feature_info['total_features']*100:.1f}% of total), including volatility ratios, volume lags, gamma exposure, and momentum indicators.</p>
            </div>
            
            <div class="section">
                <h2><span class="emoji">🤖</span> Model Performance</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">{best_model}</div>
                        <div class="metric-label">Best Model</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{model_results[best_model]['test_accuracy']:.1%}</div>
                        <div class="metric-label">Test Accuracy</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{model_results[best_model]['test_auc']:.3f}</div>
                        <div class="metric-label">Test AUC</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{len(vix_features)}/{len(selected_features)}</div>
                        <div class="metric-label">VIX Features Selected</div>
                    </div>
                </div>
                
                <h3>Model Comparison</h3>
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>Model</th>
                            <th>Test Accuracy</th>
                            <th>Test AUC</th>
                            <th>Test F1</th>
                        </tr>
                    </thead>
                    <tbody>"""
        
        # Add model comparison rows
        for model_name, metrics in model_results.items():
            if isinstance(metrics, dict) and 'test_accuracy' in metrics:
                best_indicator = "🏆 " if model_name == best_model else ""
                html_content += f"""
                        <tr>
                            <td>{best_indicator}{model_name}</td>
                            <td>{metrics['test_accuracy']:.1%}</td>
                            <td>{metrics.get('test_auc', 'N/A')}</td>
                            <td>{metrics.get('test_f1', 'N/A')}</td>
                        </tr>"""
        
        html_content += f"""
                    </tbody>
                </table>
            </div>
            
            <div class="section">
                <h2><span class="emoji">🎯</span> VIX Feature Analysis</h2>
                <p><strong>VIX Integration Impact:</strong> {len(vix_features)} VIX features selected from {feature_info['vix_features']} created, representing {len(vix_features)/len(selected_features)*100:.1f}% of the final model.</p>
                
                <div class="feature-list">"""
        
        # Categorize VIX features
        vix_categories = {
            'Volume Features': [f for f in vix_features if 'volume' in f],
            'Volatility Features': [f for f in vix_features if 'volatility' in f],
            'Return Features': [f for f in vix_features if 'return' in f],
            'Gamma Features': [f for f in vix_features if 'gamma' in f],
            'Momentum Features': [f for f in vix_features if 'momentum' in f or 'rsi' in f],
            'Other VIX Features': [f for f in vix_features if not any(cat in f for cat in ['volume', 'volatility', 'return', 'gamma', 'momentum', 'rsi'])]
        }
        
        for category, features in vix_categories.items():
            if features:
                html_content += f"""
                    <div class="feature-category">
                        <h3>{category} ({len(features)})</h3>"""
                for feature in features:
                    html_content += f'<div class="feature-item">{feature}</div>'
                html_content += "</div>"
        
        html_content += f"""
                </div>
            </div>
            
            <div class="section">
                <h2><span class="emoji">📈</span> Trading Performance & Equity Curve</h2>

                <div style="text-align: center; margin: 20px 0;">
                    <img src="enhanced_equity_curve.png" alt="Enhanced SPX + VIX Equity Curve"
                         style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <p style="font-style: italic; color: #666; margin-top: 10px;">
                        Comprehensive performance analysis showing equity curve, monthly returns, rolling win rate, and feature composition
                    </p>
                </div>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">{rsquared_metrics['total_trades']}</div>
                        <div class="metric-label">Total Trades</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{rsquared_metrics['win_rate']*100:.1f}%</div>
                        <div class="metric-label">Win Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{rsquared_metrics['total_return']:.1f}%</div>
                        <div class="metric-label">Total Return</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{rsquared_metrics['trades_per_month']:.1f}</div>
                        <div class="metric-label">Trades/Month</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{rsquared_metrics['avg_days_held']:.1f}</div>
                        <div class="metric-label">Avg Days Held</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{rsquared_metrics['sharpe_ratio']:.3f}</div>
                        <div class="metric-label">Sharpe Ratio</div>
                    </div>
                </div>
                
                <h3>Recent Trading Activity (Last 10 Trades)</h3>
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Direction</th>
                            <th>P&L %</th>
                            <th>Confidence</th>
                            <th>Days Held</th>
                        </tr>
                    </thead>
                    <tbody>"""
        
        # Add recent trades
        recent_trades = trades_df.sort_values('entry_date').tail(10)
        for _, trade in recent_trades.iterrows():
            pnl_class = "positive" if trade['pnl_pct'] > 0 else "negative"
            direction_emoji = "📈" if trade['direction'].upper() == 'LONG' else "📉"
            html_content += f"""
                        <tr>
                            <td>{trade['entry_date'].strftime('%Y-%m-%d')}</td>
                            <td>{direction_emoji} {trade['direction'].upper()}</td>
                            <td class="{pnl_class}">{trade['pnl_pct']:+.2f}%</td>
                            <td>{trade['confidence']:.1%}</td>
                            <td>{trade['days_held']}</td>
                        </tr>"""
        
        html_content += f"""
                    </tbody>
                </table>
            </div>
            
            <div class="section">
                <h2><span class="emoji">🔬</span> Key Insights</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h3>VIX-SPX Correlations</h3>
                        <ul>
                            <li><strong>Price Correlation:</strong> -0.562 (strong inverse)</li>
                            <li><strong>Return Correlation:</strong> -0.715 (very strong inverse)</li>
                            <li><strong>Volatility Regime:</strong> VIX provides crucial market stress indicators</li>
                        </ul>
                    </div>
                    <div>
                        <h3>Enhanced Capabilities</h3>
                        <ul>
                            <li><strong>Volatility Awareness:</strong> Better predictions during market stress</li>
                            <li><strong>Institutional Flow:</strong> VIX volume shows smart money positioning</li>
                            <li><strong>Dealer Pressure:</strong> VIX gamma reveals market structure</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2><span class="emoji">✅</span> System Achievements</h2>
                <ul style="font-size: 1.1em; line-height: 1.8;">
                    <li><strong>🚀 VIX Integration:</strong> Successfully merged SPX and VIX options data</li>
                    <li><strong>📊 Feature Engineering:</strong> Created {feature_info['vix_features']} VIX-specific features</li>
                    <li><strong>🤖 Model Enhancement:</strong> {len(vix_features)} VIX features in final model ({len(vix_features)/len(selected_features)*100:.1f}%)</li>
                    <li><strong>📈 Trading Performance:</strong> {rsquared_metrics['total_trades']} trades with {rsquared_metrics['win_rate']*100:.1f}% win rate</li>
                    <li><strong>🎯 Innovation:</strong> First comprehensive SPX+VIX predictive system</li>
                </ul>
            </div>
        </div>
        
        <div class="footer">
            <p><span class="emoji">🎉</span> Enhanced SPX + VIX Predictive System - Powered by Advanced Machine Learning</p>
            <p>Generated by Manus AI • {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>
</body>
</html>"""
        
        # Save HTML report
        report_file = 'enhanced_spx_vix_report.html'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"   ✅ HTML report saved: {report_file}")
        
        return report_file
        
    except Exception as e:
        print(f"❌ HTML report creation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    report_file = create_enhanced_html_report()
    if report_file:
        print(f"\n🎉 Enhanced HTML report created successfully!")
        print(f"📄 File: {report_file}")
        print("🌐 Open in browser to view the complete results")
    else:
        print("❌ Failed to create HTML report")
