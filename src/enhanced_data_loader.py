#!/usr/bin/env python3
"""
Enhanced Data Loader for SPX + VIX Options Data
==============================================

Loads and merges SPX and VIX options data to enhance predictive capabilities.
VIX options provide additional volatility and sentiment information that can
improve SPX direction predictions.

Author: Enhanced by Manus AI
Date: June 23, 2025
"""

import pandas as pd
import numpy as np
from pathlib import Path
import glob
import warnings
import sys
import os
from typing import List, Optional, Dict, Any
from datetime import datetime

# Add cfg directory to path for config import
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'cfg'))
from config import config
from data_loader import DataLoader

class EnhancedDataLoader:
    """Enhanced data loader that combines SPX and VIX options data"""
    
    def __init__(self, data_directory: Optional[str] = None, include_vix: bool = True):
        """
        Initialize enhanced data loader
        
        Args:
            data_directory: Directory containing data files (optional)
            include_vix: Whether to include VIX options data for enhanced predictions
        """
        self.data_directory = Path(data_directory) if data_directory else config.get_data_source_config('data_directory')
        self.include_vix = include_vix
        self.date_column = 'date'
        
        print(f"🚀 Enhanced Data Loader Initialized")
        print(f"📁 Data Directory: {self.data_directory}")
        print(f"📊 Include VIX: {self.include_vix}")
    
    def find_ticker_files(self, ticker: str) -> List[Path]:
        """Find all files for a specific ticker"""
        pattern = f"**/{ticker}_option_daily_analysis_*.csv"
        files = glob.glob(str(self.data_directory / pattern), recursive=True)
        return [Path(f) for f in sorted(files)]
    
    def load_ticker_data(self, ticker: str) -> pd.DataFrame:
        """Load all data for a specific ticker"""
        print(f"\n📊 Loading {ticker.upper()} options data...")
        
        files = self.find_ticker_files(ticker.lower())
        
        if not files:
            print(f"❌ No {ticker.upper()} files found")
            return pd.DataFrame()
        
        print(f"📄 Found {len(files)} {ticker.upper()} files")
        
        dataframes = []
        for file_path in files:
            try:
                print(f"   📖 Loading: {file_path.name}")
                df = pd.read_csv(file_path)
                df['date'] = pd.to_datetime(df['date'])
                dataframes.append(df)
                print(f"      ✅ Shape: {df.shape}, Date range: {df['date'].min().date()} to {df['date'].max().date()}")
            except Exception as e:
                print(f"      ❌ Error: {str(e)}")
                continue
        
        if not dataframes:
            print(f"❌ No {ticker.upper()} data loaded successfully")
            return pd.DataFrame()
        
        # Combine all dataframes
        combined_df = pd.concat(dataframes, ignore_index=True)
        combined_df = combined_df.sort_values('date').reset_index(drop=True)
        
        print(f"   ✅ Combined {ticker.upper()} data: {combined_df.shape}")
        print(f"   📅 Date range: {combined_df['date'].min().date()} to {combined_df['date'].max().date()}")
        
        return combined_df
    
    def prepare_vix_features(self, vix_df: pd.DataFrame) -> pd.DataFrame:
        """Prepare VIX-specific features for merging with SPX data"""
        if vix_df.empty:
            return pd.DataFrame()
        
        print("🔧 Engineering VIX features...")
        
        # Rename VIX columns to avoid conflicts with SPX
        vix_features = vix_df.copy()
        
        # Core VIX price features
        vix_features['vix_return'] = vix_features['vix_close'].pct_change()
        vix_features['vix_volatility'] = vix_features['vix_return'].rolling(5).std()
        vix_features['vix_range_pct'] = (vix_features['vix_high'] - vix_features['vix_low']) / vix_features['vix_close']
        
        # VIX level indicators
        vix_features['vix_level'] = pd.cut(vix_features['vix_close'], 
                                          bins=[0, 15, 20, 25, 30, 100], 
                                          labels=['very_low', 'low', 'medium', 'high', 'very_high'])
        vix_features['vix_level_numeric'] = vix_features['vix_level'].cat.codes
        
        # VIX momentum features
        vix_features['vix_sma_5'] = vix_features['vix_close'].rolling(5).mean()
        vix_features['vix_sma_20'] = vix_features['vix_close'].rolling(20).mean()
        vix_features['vix_momentum'] = vix_features['vix_close'] / vix_features['vix_sma_20'] - 1
        
        # VIX options flow features (rename to avoid conflicts)
        vix_features['vix_put_wall'] = vix_features['put_wall_strike']
        vix_features['vix_call_wall'] = vix_features['call_wall_strike']
        vix_features['vix_put_wall_oi'] = vix_features['put_wall_oi']
        vix_features['vix_call_wall_oi'] = vix_features['call_wall_oi']
        
        # VIX gamma features
        vix_features['vix_gamma_put_exposure'] = vix_features['gamma_put_wall_exposure']
        vix_features['vix_gamma_call_exposure'] = vix_features['gamma_call_wall_exposure']
        vix_features['vix_total_gamma'] = vix_features['total_gamma']
        vix_features['vix_total_vega'] = vix_features['total_vega']
        
        # VIX notional features
        vix_features['vix_call_notional'] = vix_features['call_notional']
        vix_features['vix_put_notional'] = vix_features['put_notional']
        vix_features['vix_total_notional'] = vix_features['total_notional']
        vix_features['vix_put_call_notional_ratio'] = vix_features['vix_put_notional'] / vix_features['vix_call_notional']
        
        # VIX volume and OI features
        vix_features['vix_total_volume'] = vix_features['total_volume']
        vix_features['vix_total_oi'] = vix_features['total_open_interest']
        
        # VIX term structure proxies
        vix_features['vix_contango'] = vix_features['vix_close'] < vix_features['vix_close'].shift(1)
        vix_features['vix_backwardation'] = vix_features['vix_close'] > vix_features['vix_close'].shift(1)
        
        # Select only the new VIX features for merging
        vix_columns = [
            'date', 'vix_close', 'vix_return', 'vix_volatility', 'vix_range_pct',
            'vix_level_numeric', 'vix_momentum', 'vix_put_wall', 'vix_call_wall',
            'vix_put_wall_oi', 'vix_call_wall_oi', 'vix_gamma_put_exposure',
            'vix_gamma_call_exposure', 'vix_total_gamma', 'vix_total_vega',
            'vix_put_call_notional_ratio', 'vix_total_volume', 'vix_total_oi',
            'vix_contango', 'vix_backwardation'
        ]
        
        # Only keep columns that exist
        available_columns = [col for col in vix_columns if col in vix_features.columns]
        vix_final = vix_features[available_columns].copy()
        
        print(f"   ✅ Created {len(available_columns)-1} VIX features")
        
        return vix_final
    
    def merge_spx_vix_data(self, spx_df: pd.DataFrame, vix_df: pd.DataFrame) -> pd.DataFrame:
        """Merge SPX and VIX data on date"""
        if vix_df.empty:
            print("⚠️  No VIX data to merge, returning SPX data only")
            return spx_df
        
        print("🔗 Merging SPX and VIX data...")
        
        # Prepare VIX features
        vix_features = self.prepare_vix_features(vix_df)
        
        if vix_features.empty:
            print("⚠️  No VIX features created, returning SPX data only")
            return spx_df
        
        # Merge on date
        merged_df = pd.merge(spx_df, vix_features, on='date', how='left')
        
        # Fill missing VIX values with forward fill then backward fill
        vix_cols = [col for col in merged_df.columns if col.startswith('vix_')]
        for col in vix_cols:
            if col in merged_df.columns:
                merged_df[col] = merged_df[col].fillna(method='ffill').fillna(method='bfill')
        
        print(f"   ✅ Merged data shape: {merged_df.shape}")
        print(f"   📊 Added {len(vix_cols)} VIX features")
        
        # Show merge statistics
        spx_dates = set(spx_df['date'])
        vix_dates = set(vix_features['date'])
        common_dates = spx_dates.intersection(vix_dates)
        
        print(f"   📅 SPX dates: {len(spx_dates)}")
        print(f"   📅 VIX dates: {len(vix_dates)}")
        print(f"   📅 Common dates: {len(common_dates)} ({len(common_dates)/len(spx_dates)*100:.1f}%)")
        
        return merged_df
    
    def load_enhanced_data(self) -> pd.DataFrame:
        """Load and combine SPX and VIX data for enhanced predictions"""
        print("=== Enhanced SPX + VIX Options Data Loading ===")
        print(f"🕐 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Load SPX data
        spx_df = self.load_ticker_data('spx')
        
        if spx_df.empty:
            raise RuntimeError("Failed to load SPX data")
        
        # Load VIX data if requested
        if self.include_vix:
            vix_df = self.load_ticker_data('vix')
            
            if not vix_df.empty:
                # Merge SPX and VIX data
                enhanced_df = self.merge_spx_vix_data(spx_df, vix_df)
            else:
                print("⚠️  VIX data not available, using SPX only")
                enhanced_df = spx_df
        else:
            print("📊 VIX data not requested, using SPX only")
            enhanced_df = spx_df
        
        print(f"\n✅ Enhanced data loading completed!")
        print(f"   Final dataset shape: {enhanced_df.shape}")
        print(f"   Date range: {enhanced_df['date'].min().date()} to {enhanced_df['date'].max().date()}")
        
        # Show feature summary
        spx_features = len([col for col in enhanced_df.columns if not col.startswith('vix_') and col != 'date'])
        vix_features = len([col for col in enhanced_df.columns if col.startswith('vix_')])
        
        print(f"   📊 SPX features: {spx_features}")
        print(f"   📊 VIX features: {vix_features}")
        print(f"   📊 Total features: {spx_features + vix_features}")
        
        return enhanced_df

# Convenience functions
def load_enhanced_spx_data(data_directory: Optional[str] = None, include_vix: bool = True) -> pd.DataFrame:
    """
    Convenience function to load enhanced SPX + VIX options data
    
    Args:
        data_directory: Directory containing data files (optional)
        include_vix: Whether to include VIX options data
    
    Returns:
        Enhanced dataframe with SPX and VIX data
    """
    loader = EnhancedDataLoader(data_directory, include_vix)
    return loader.load_enhanced_data()

def compare_spx_vs_enhanced_features(data_directory: Optional[str] = None) -> Dict[str, Any]:
    """
    Compare feature counts between SPX-only and enhanced SPX+VIX data
    
    Returns:
        Dictionary with comparison statistics
    """
    # Load SPX only
    spx_loader = EnhancedDataLoader(data_directory, include_vix=False)
    spx_df = spx_loader.load_enhanced_data()
    
    # Load enhanced data
    enhanced_loader = EnhancedDataLoader(data_directory, include_vix=True)
    enhanced_df = enhanced_loader.load_enhanced_data()
    
    return {
        'spx_only': {
            'shape': spx_df.shape,
            'features': spx_df.shape[1] - 1,  # Exclude date column
            'columns': list(spx_df.columns)
        },
        'enhanced': {
            'shape': enhanced_df.shape,
            'features': enhanced_df.shape[1] - 1,  # Exclude date column
            'columns': list(enhanced_df.columns)
        },
        'vix_features_added': enhanced_df.shape[1] - spx_df.shape[1]
    }

if __name__ == "__main__":
    # Test the enhanced data loader
    try:
        loader = EnhancedDataLoader(include_vix=True)
        enhanced_data = loader.load_enhanced_data()
        print(f"\n🎉 Test successful! Enhanced data shape: {enhanced_data.shape}")
        
        # Show VIX features
        vix_features = [col for col in enhanced_data.columns if col.startswith('vix_')]
        print(f"📊 VIX features added: {len(vix_features)}")
        for feature in vix_features[:10]:  # Show first 10
            print(f"   - {feature}")
        if len(vix_features) > 10:
            print(f"   ... and {len(vix_features) - 10} more")
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
