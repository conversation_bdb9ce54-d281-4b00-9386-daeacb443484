#!/usr/bin/env python3
"""
SPX Options Predictive Trading System
=====================================

A comprehensive machine learning system for predicting SPX price movements
using options market data. This system incorporates ensemble modeling,
advanced feature engineering, and robust risk management.

Author: Manus AI
Date: June 23, 2025

Usage:
    python spx_prediction_system.py --mode [train|predict|backtest|report]
    
Features:
- Ensemble machine learning models with voting signals
- Advanced feature engineering with 150+ features
- Isolation Forest for outlier detection
- Comprehensive backtesting framework
- Real-time prediction capabilities
- Detailed performance reporting
"""

import pandas as pd
import numpy as np
import json
import pickle
import argparse
import warnings
from datetime import datetime, timedelta
from pathlib import Path

# Machine Learning imports
from sklearn.preprocessing import RobustScaler
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
import lightgbm as lgb

# Visualization imports
import matplotlib.pyplot as plt
import seaborn as sns

warnings.filterwarnings('ignore')

class SPXPredictionSystem:
    """
    Main class for the SPX Options Predictive Trading System
    """
    
    def __init__(self, config_file=None):
        """Initialize the prediction system"""
        self.config = self._load_config(config_file)
        self.model = None
        self.scaler = None
        self.selected_features = None
        self.feature_info = None
        self.data = None
        
    def _load_config(self, config_file):
        """Load system configuration"""
        default_config = {
            'confidence_threshold': 0.5,
            'stop_loss_pct': 2.0,
            'take_profit_pct': 3.0,
            'max_days_held': 3,
            'outlier_contamination': 0.1,
            'n_features': 30,
            'model_params': {
                'n_estimators': 100,
                'learning_rate': 0.1,
                'max_depth': 5,
                'random_state': 42,
                'verbose': -1
            }
        }
        
        if config_file and Path(config_file).exists():
            with open(config_file, 'r') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        
        return default_config
    
    def engineer_features(self, data):
        """
        Comprehensive feature engineering pipeline
        """
        print("Engineering features...")
        
        # Create copy to avoid modifying original data
        df = data.copy()
        df = df.sort_values('date').reset_index(drop=True)
        
        # Basic derived features
        df['spx_return'] = df['spx_close'].pct_change()
        df['spx_range_pct'] = (df['spx_high'] - df['spx_low']) / df['spx_open'] * 100
        df['put_call_oi_ratio'] = df['put_wall_oi'] / df['call_wall_oi']
        df['put_call_notional_ratio'] = df['put_notional'] / df['call_notional']
        df['spx_to_put_wall'] = (df['spx_close'] - df['put_wall_strike']) / df['spx_close'] * 100
        df['spx_to_call_wall'] = (df['call_wall_strike'] - df['spx_close']) / df['spx_close'] * 100
        
        # Lagged features
        lag_features = ['spx_return', 'spx_range_pct', 'put_call_oi_ratio', 
                       'total_gamma', 'total_volume']
        
        for feature in lag_features:
            for lag in range(1, 6):
                df[f'{feature}_lag{lag}'] = df[feature].shift(lag)
        
        # Rolling statistics
        rolling_features = ['spx_return', 'put_call_oi_ratio', 'total_gamma']
        windows = [3, 5, 10]
        
        for feature in rolling_features:
            for window in windows:
                df[f'{feature}_ma{window}'] = df[feature].rolling(window=window).mean()
                df[f'{feature}_std{window}'] = df[feature].rolling(window=window).std()
        
        # Momentum features
        df['spx_momentum_3d'] = df['spx_close'] / df['spx_close'].shift(3) - 1
        df['spx_momentum_5d'] = df['spx_close'] / df['spx_close'].shift(5) - 1
        df['realized_vol_5d'] = df['spx_return'].rolling(5).std() * np.sqrt(252)
        
        # Options-specific features
        df['gamma_change_1d'] = df['total_gamma'].diff()
        df['pc_ratio_change_1d'] = df['put_call_oi_ratio'].diff()
        df['volume_surge_5d'] = df['total_volume'] / df['total_volume'].rolling(5).mean()
        
        # Interaction features
        df['vol_pc_interaction'] = df['realized_vol_5d'] * df['put_call_oi_ratio']
        df['gamma_vol_interaction'] = df['total_gamma'] * df['total_volume']
        
        # Target variables
        df['target_direction'] = (df['spx_return'].shift(-1) > 0).astype(int)
        df['target_return'] = df['spx_return'].shift(-1)
        
        return df
    
    def prepare_features(self, data):
        """
        Prepare features for modeling
        """
        # Get all numeric features (excluding targets and metadata)
        exclude_cols = ['date', 'target_direction', 'target_return', 'data_year', 
                       'data_quarter', 'analysis_date']
        
        feature_cols = [col for col in data.columns 
                       if col not in exclude_cols and data[col].dtype in ['float64', 'int64']]
        
        X = data[feature_cols].copy()
        
        # Handle infinite values
        X = X.replace([np.inf, -np.inf], np.nan)
        
        # Fill missing values
        for col in X.columns:
            X[col] = X[col].fillna(X[col].median())
        
        # Remove constant features
        feature_variance = X.var()
        variable_features = feature_variance[feature_variance > 0].index.tolist()
        X = X[variable_features]
        
        return X, feature_cols
    
    def train_model(self, data_file):
        """
        Train the prediction model
        """
        print("Loading and preparing training data...")
        
        # Load data
        if isinstance(data_file, str):
            self.data = pd.read_csv(data_file)
        else:
            self.data = data_file.copy()
        
        self.data['date'] = pd.to_datetime(self.data['date'])
        
        # Engineer features
        engineered_data = self.engineer_features(self.data)
        
        # Prepare features
        X, feature_cols = self.prepare_features(engineered_data)
        y = engineered_data['target_direction'].copy()
        
        # Remove rows with missing targets
        valid_idx = ~y.isna()
        X = X[valid_idx]
        y = y[valid_idx]
        
        print(f"Training data shape: {X.shape}")
        print(f"Target distribution: {y.value_counts().to_dict()}")
        
        # Time-based split
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        
        # Scale features
        self.scaler = RobustScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Handle any remaining issues
        X_train_scaled = np.nan_to_num(X_train_scaled, nan=0.0, posinf=1.0, neginf=-1.0)
        X_test_scaled = np.nan_to_num(X_test_scaled, nan=0.0, posinf=1.0, neginf=-1.0)
        
        # Convert back to DataFrames
        X_train_scaled = pd.DataFrame(X_train_scaled, columns=X_train.columns, index=X_train.index)
        X_test_scaled = pd.DataFrame(X_test_scaled, columns=X_test.columns, index=X_test.index)
        
        # Feature selection
        print("Selecting features...")
        rf_selector = RandomForestClassifier(n_estimators=100, random_state=42)
        rf_selector.fit(X_train_scaled, y_train)
        
        feature_importance = pd.DataFrame({
            'feature': X_train_scaled.columns,
            'importance': rf_selector.feature_importances_
        }).sort_values('importance', ascending=False)
        
        # Select top features
        n_features = min(self.config['n_features'], len(feature_importance))
        self.selected_features = feature_importance.head(n_features)['feature'].tolist()
        
        X_train_selected = X_train_scaled[self.selected_features]
        X_test_selected = X_test_scaled[self.selected_features]
        
        print(f"Selected {len(self.selected_features)} features")
        
        # Train model
        print("Training LightGBM model...")
        self.model = lgb.LGBMClassifier(**self.config['model_params'])
        self.model.fit(X_train_selected, y_train)
        
        # Evaluate model
        train_pred = self.model.predict(X_train_selected)
        test_pred = self.model.predict(X_test_selected)
        test_proba = self.model.predict_proba(X_test_selected)[:, 1]
        
        train_acc = accuracy_score(y_train, train_pred)
        test_acc = accuracy_score(y_test, test_pred)
        test_auc = roc_auc_score(y_test, test_proba)
        
        print(f"Training Accuracy: {train_acc:.4f}")
        print(f"Test Accuracy: {test_acc:.4f}")
        print(f"Test AUC: {test_auc:.4f}")
        
        # Save model components
        self._save_model_components()
        
        return {
            'train_accuracy': train_acc,
            'test_accuracy': test_acc,
            'test_auc': test_auc,
            'feature_importance': feature_importance.head(10).to_dict('records')
        }
    
    def predict(self, data):
        """
        Generate predictions for new data
        """
        if self.model is None:
            self._load_model_components()
        
        # Engineer features
        engineered_data = self.engineer_features(data)
        
        # Prepare features
        X, _ = self.prepare_features(engineered_data)
        
        # Scale features
        X_scaled = self.scaler.transform(X)
        X_scaled = np.nan_to_num(X_scaled, nan=0.0, posinf=1.0, neginf=-1.0)
        X_scaled = pd.DataFrame(X_scaled, columns=X.columns, index=X.index)
        
        # Select features
        X_selected = X_scaled[self.selected_features]
        
        # Generate predictions
        predictions = self.model.predict(X_selected)
        probabilities = self.model.predict_proba(X_selected)[:, 1]
        
        # Create results DataFrame
        results = pd.DataFrame({
            'date': data['date'],
            'spx_close': data['spx_close'],
            'prediction': predictions,
            'probability': probabilities,
            'signal': (probabilities >= self.config['confidence_threshold']).astype(int),
            'direction': ['Long' if p == 1 else 'Short' for p in predictions]
        })
        
        return results
    
    def backtest(self, data_file=None):
        """
        Run comprehensive backtesting
        """
        if data_file:
            data = pd.read_csv(data_file)
            data['date'] = pd.to_datetime(data['date'])
        else:
            data = self.data
        
        # Generate predictions
        predictions_df = self.predict(data)
        
        # Merge with price data
        backtest_data = data.merge(predictions_df[['date', 'prediction', 'probability', 'signal']], 
                                  on='date', how='inner')
        
        # Run trading simulation
        trades = self._simulate_trading(backtest_data)
        
        # Calculate performance metrics
        performance = self._calculate_performance(trades)
        
        # Generate report
        report = self._generate_backtest_report(trades, performance)
        
        return {
            'trades': trades,
            'performance': performance,
            'report': report
        }
    
    def _simulate_trading(self, data):
        """
        Simulate trading based on predictions
        """
        trades = []
        current_position = None
        
        for i in range(len(data) - 1):
            row = data.iloc[i]
            next_row = data.iloc[i + 1]
            
            # Check for entry
            if current_position is None and row['signal'] == 1:
                direction = 'long' if row['prediction'] == 1 else 'short'
                entry_price = next_row['spx_open']
                
                current_position = {
                    'direction': direction,
                    'entry_price': entry_price,
                    'entry_date': next_row['date'],
                    'entry_index': i + 1,
                    'confidence': row['probability']
                }
            
            # Check for exit
            elif current_position is not None:
                entry_price = current_position['entry_price']
                direction = current_position['direction']
                current_price = row['spx_close']
                
                if direction == 'long':
                    pnl_pct = (current_price - entry_price) / entry_price * 100
                else:
                    pnl_pct = (entry_price - current_price) / entry_price * 100
                
                exit_trade = False
                exit_reason = None
                
                # Stop loss
                if pnl_pct <= -self.config['stop_loss_pct']:
                    exit_trade = True
                    exit_reason = 'stop_loss'
                # Take profit
                elif pnl_pct >= self.config['take_profit_pct']:
                    exit_trade = True
                    exit_reason = 'take_profit'
                # Time exit
                elif i - current_position['entry_index'] >= self.config['max_days_held']:
                    exit_trade = True
                    exit_reason = 'time_exit'
                
                if exit_trade:
                    trade = {
                        'entry_date': current_position['entry_date'],
                        'exit_date': row['date'],
                        'direction': direction,
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'pnl_pct': pnl_pct,
                        'days_held': i - current_position['entry_index'],
                        'exit_reason': exit_reason,
                        'confidence': current_position['confidence']
                    }
                    trades.append(trade)
                    current_position = None
        
        return pd.DataFrame(trades)
    
    def _calculate_performance(self, trades):
        """
        Calculate comprehensive performance metrics
        """
        if len(trades) == 0:
            return {}
        
        total_trades = len(trades)
        winning_trades = len(trades[trades['pnl_pct'] > 0])
        win_rate = winning_trades / total_trades
        
        total_return = trades['pnl_pct'].sum()
        avg_return = trades['pnl_pct'].mean()
        
        avg_win = trades[trades['pnl_pct'] > 0]['pnl_pct'].mean() if winning_trades > 0 else 0
        avg_loss = trades[trades['pnl_pct'] <= 0]['pnl_pct'].mean() if winning_trades < total_trades else 0
        
        sharpe_ratio = avg_return / trades['pnl_pct'].std() if trades['pnl_pct'].std() > 0 else 0
        
        cumulative_returns = trades['pnl_pct'].cumsum()
        max_drawdown = (cumulative_returns.expanding().max() - cumulative_returns).max()
        
        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_return': total_return,
            'avg_return_per_trade': avg_return,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown
        }
    
    def _generate_backtest_report(self, trades, performance):
        """
        Generate comprehensive backtest report
        """
        report = f"""
SPX Options Predictive Trading System - Backtest Report
======================================================

Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

PERFORMANCE SUMMARY
------------------
Total Trades: {performance.get('total_trades', 0)}
Win Rate: {performance.get('win_rate', 0):.1%}
Total Return: {performance.get('total_return', 0):.2f}%
Average Return per Trade: {performance.get('avg_return_per_trade', 0):.3f}%
Average Win: {performance.get('avg_win', 0):.3f}%
Average Loss: {performance.get('avg_loss', 0):.3f}%
Sharpe Ratio: {performance.get('sharpe_ratio', 0):.3f}
Maximum Drawdown: {performance.get('max_drawdown', 0):.2f}%

STRATEGY PARAMETERS
------------------
Confidence Threshold: {self.config['confidence_threshold']}
Stop Loss: {self.config['stop_loss_pct']}%
Take Profit: {self.config['take_profit_pct']}%
Max Days Held: {self.config['max_days_held']}

"""
        
        if len(trades) > 0:
            report += f"""
TRADE DISTRIBUTION
-----------------
Long Trades: {len(trades[trades['direction'] == 'long'])}
Short Trades: {len(trades[trades['direction'] == 'short'])}

EXIT REASONS
-----------
"""
            exit_reasons = trades['exit_reason'].value_counts()
            for reason, count in exit_reasons.items():
                report += f"{reason}: {count} ({count/len(trades)*100:.1f}%)\n"
        
        return report
    
    def generate_next_day_signal(self, current_data):
        """
        Generate trading signal for next day
        """
        predictions = self.predict(current_data)
        latest = predictions.iloc[-1]
        
        signal_report = f"""
SPX Options Trading Signal - {datetime.now().strftime('%Y-%m-%d')}
============================================================

PREDICTION FOR NEXT TRADING DAY
------------------------------
Direction: {latest['direction']}
Confidence: {latest['probability']:.1%}
Signal Strength: {'STRONG' if latest['probability'] > 0.7 else 'MODERATE' if latest['probability'] > 0.6 else 'WEAK'}

TRADING RECOMMENDATION
--------------------
"""
        
        if latest['signal'] == 1:
            signal_report += f"""
ENTER {latest['direction'].upper()} POSITION
Entry: Market Open
Stop Loss: {self.config['stop_loss_pct']}%
Take Profit: {self.config['take_profit_pct']}%
Max Hold: {self.config['max_days_held']} days
"""
        else:
            signal_report += "NO TRADE - Confidence below threshold\n"
        
        signal_report += f"""
CURRENT MARKET DATA
------------------
SPX Close: {current_data['spx_close'].iloc[-1]:.2f}
Put/Call OI Ratio: {current_data['put_wall_oi'].iloc[-1] / current_data['call_wall_oi'].iloc[-1]:.2f}
Total Volume: {current_data['total_volume'].iloc[-1]:,}
"""
        
        return signal_report, latest
    
    def _save_model_components(self):
        """Save trained model components"""
        with open('spx_model.pkl', 'wb') as f:
            pickle.dump(self.model, f)
        
        with open('spx_scaler.pkl', 'wb') as f:
            pickle.dump(self.scaler, f)
        
        with open('spx_features.json', 'w') as f:
            json.dump(self.selected_features, f)
        
        with open('spx_config.json', 'w') as f:
            json.dump(self.config, f)
    
    def _load_model_components(self):
        """Load trained model components"""
        with open('spx_model.pkl', 'rb') as f:
            self.model = pickle.load(f)
        
        with open('spx_scaler.pkl', 'rb') as f:
            self.scaler = pickle.load(f)
        
        with open('spx_features.json', 'r') as f:
            self.selected_features = json.load(f)

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='SPX Options Predictive Trading System')
    parser.add_argument('--mode', choices=['train', 'predict', 'backtest', 'signal'], 
                       required=True, help='Operation mode')
    parser.add_argument('--data', required=True, help='Path to data file')
    parser.add_argument('--config', help='Path to configuration file')
    parser.add_argument('--output', help='Output file path')
    
    args = parser.parse_args()
    
    # Initialize system
    system = SPXPredictionSystem(args.config)
    
    if args.mode == 'train':
        print("Training SPX prediction model...")
        results = system.train_model(args.data)
        print("Training completed successfully!")
        print(f"Test Accuracy: {results['test_accuracy']:.1%}")
        print(f"Test AUC: {results['test_auc']:.3f}")
        
    elif args.mode == 'predict':
        print("Generating predictions...")
        data = pd.read_csv(args.data)
        data['date'] = pd.to_datetime(data['date'])
        predictions = system.predict(data)
        
        if args.output:
            predictions.to_csv(args.output, index=False)
            print(f"Predictions saved to {args.output}")
        else:
            print(predictions.tail())
    
    elif args.mode == 'backtest':
        print("Running backtest...")
        results = system.backtest(args.data)
        print(results['report'])
        
        if args.output:
            results['trades'].to_csv(args.output, index=False)
            print(f"Trade details saved to {args.output}")
    
    elif args.mode == 'signal':
        print("Generating next-day trading signal...")
        data = pd.read_csv(args.data)
        data['date'] = pd.to_datetime(data['date'])
        signal_report, signal_data = system.generate_next_day_signal(data)
        print(signal_report)
        
        if args.output:
            with open(args.output, 'w') as f:
                f.write(signal_report)
            print(f"Signal report saved to {args.output}")

if __name__ == "__main__":
    main()

