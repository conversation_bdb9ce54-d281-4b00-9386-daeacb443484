"""
Generate System Status Summary
"""

import json
import pandas as pd
from datetime import datetime

def generate_status_summary():
    """Generate a comprehensive system status summary"""
    print("📊 ROBUST SPX SYSTEM - STATUS SUMMARY")
    print("=" * 60)
    
    # Load current prediction
    with open('current_prediction.json', 'r') as f:
        current_pred = json.load(f)
    
    # Load system results
    with open('robust_system_results.json', 'r') as f:
        system_results = json.load(f)
    
    # Load recent trades
    trades_df = pd.read_csv('robust_system_trades.csv')
    trades_df['date'] = pd.to_datetime(trades_df['date'])
    
    print(f"📅 Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📊 Data as of: {current_pred['date'][:10]}")
    
    print(f"\n🎯 CURRENT MARKET POSITION:")
    print(f"   📈 SPX: ${current_pred['spx_close']:,.2f}")
    print(f"   📊 VIX: {current_pred['vix_close']:.2f}")
    print(f"   🔮 Prediction: {current_pred['prediction']}")
    print(f"   📊 Confidence: {current_pred['confidence']:.1%}")
    print(f"   🚦 Signal: {current_pred['trade_signal']}")
    
    print(f"\n🏆 OVERALL SYSTEM PERFORMANCE:")
    print(f"   📊 Total Trades: {system_results['total_trades']}")
    print(f"   🎯 Win Rate: {system_results['win_rate']:.1%}")
    print(f"   💰 Total Return: {system_results['total_return']:.1f}%")
    print(f"   📅 Monthly Win Rate: {system_results['monthly_win_rate']:.1%}")
    print(f"   📈 Avg Return/Trade: {system_results['avg_return_per_trade']:.3f}%")
    
    # Recent performance
    last_10_trades = trades_df.tail(10)
    recent_win_rate = last_10_trades['win'].mean()
    recent_return = last_10_trades['return_pct'].sum()
    
    print(f"\n📈 RECENT PERFORMANCE (Last 10 Trades):")
    print(f"   🎯 Win Rate: {recent_win_rate:.1%}")
    print(f"   💰 Total Return: {recent_return:+.2f}%")
    print(f"   📊 Model Accuracy: {current_pred['model_accuracy_recent']:.1%}")
    
    # Last 5 trades detail
    last_5 = trades_df.tail(5)
    print(f"\n📋 LAST 5 TRADES:")
    for i, (_, trade) in enumerate(last_5.iterrows(), 1):
        status = "✅" if trade['win'] else "❌"
        direction = "📈" if trade['direction'] == 'long' else "📉"
        print(f"   {i}. {trade['date'].strftime('%m/%d')} {direction} {trade['return_pct']:+.2f}% {status}")
    
    # Trading recommendation
    print(f"\n🚨 TRADING RECOMMENDATION:")
    if current_pred['trade_signal'] != 'NO TRADE':
        print(f"   🎯 ACTION: {current_pred['trade_signal']}")
        print(f"   📊 Confidence: {current_pred['confidence']:.1%}")
        if current_pred['trade_signal'] == 'LONG':
            print(f"   📈 Strategy: Buy SPX calls or go long")
        else:
            print(f"   📉 Strategy: Buy SPX puts or go short")
    else:
        print(f"   ⏸️  WAIT: Confidence {current_pred['confidence']:.1%} below threshold")
        print(f"   📊 Need confidence > 55% or < 45% to trade")
    
    print(f"\n✅ System Status: OPERATIONAL")
    print(f"📊 Model Health: {'GOOD' if current_pred['model_accuracy_recent'] > 0.55 else 'ACCEPTABLE' if current_pred['model_accuracy_recent'] > 0.5 else 'NEEDS ATTENTION'}")

if __name__ == "__main__":
    generate_status_summary()
