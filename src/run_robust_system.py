"""
Complete Robust SPX System
Run the working baseline system end-to-end
"""

import pandas as pd
import numpy as np
import pickle
import json
from config import config

def run_complete_robust_system():
    """Run the complete robust system"""
    print("🚀 COMPLETE ROBUST SPX SYSTEM")
    print("=" * 60)
    
    # 1. Load data and model
    print("1. Loading robust baseline system...")
    
    # Load processed data
    data = pd.read_csv('spx_processed_data.csv')
    data['date'] = pd.to_datetime(data['date'])
    
    # Recreate baseline features
    data['spx_return_1d'] = data['spx_close'].pct_change(1)
    data['spx_return_5d'] = data['spx_close'].pct_change(5)
    data['spx_sma_5'] = data['spx_close'].rolling(5).mean()
    data['spx_sma_20'] = data['spx_close'].rolling(20).mean()
    data['spx_above_sma5'] = (data['spx_close'] > data['spx_sma_5']).astype(int)
    data['spx_above_sma20'] = (data['spx_close'] > data['spx_sma_20']).astype(int)
    data['vix_sma_5'] = data['vix_close'].rolling(5).mean()
    data['vix_sma_20'] = data['vix_close'].rolling(20).mean()
    data['vix_above_20'] = (data['vix_close'] > 20).astype(int)
    data['vix_above_30'] = (data['vix_close'] > 30).astype(int)
    data['volume_sma_5'] = data['total_volume'].rolling(5).mean()
    data['volume_sma_20'] = data['total_volume'].rolling(20).mean()
    data['volume_above_avg'] = (data['total_volume'] > data['volume_sma_20']).astype(int)
    data['gamma_normalized'] = data['total_gamma'] / data['total_gamma'].rolling(20).mean()
    data['put_call_ratio'] = data['put_notional'] / (data['call_notional'] + 1e-6)
    data['target'] = (data['next_day_return'] > 0).astype(int)
    
    # Load model
    with open(config.get_model_file('best_model'), 'rb') as f:
        model = pickle.load(f)
    
    with open(config.get_model_file('scaler'), 'rb') as f:
        scaler = pickle.load(f)
    
    with open(config.get_data_file('selected_features'), 'r') as f:
        selected_features = json.load(f)
    
    print(f"   ✅ Loaded robust model with {len(selected_features)} features")
    
    # 2. Prepare data
    print("2. Preparing data...")
    
    clean_data = data.dropna(subset=['target'] + selected_features).copy()
    X = clean_data[selected_features].copy()
    
    # Handle missing values
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    X = X.replace([np.inf, -np.inf], np.nan)
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    print(f"   📊 Clean data shape: {X.shape}")
    
    # 3. Generate predictions
    print("3. Generating predictions...")
    
    X_scaled = scaler.transform(X)
    predictions = model.predict(X_scaled)
    probabilities = model.predict_proba(X_scaled)[:, 1]
    
    # Add to data
    clean_data['prediction'] = predictions
    clean_data['probability'] = probabilities
    
    print(f"   ✅ Generated {len(predictions)} predictions")
    print(f"   📊 Prediction accuracy: {(predictions == clean_data['target']).mean():.1%}")
    
    # 4. Trading simulation
    print("4. Running trading simulation...")
    
    # Use last 600 days for trading
    trading_data = clean_data.tail(600).copy()
    
    # Strategy: Trade when probability > 0.55 or < 0.45
    confident_long = trading_data[trading_data['probability'] > 0.55].copy()
    confident_short = trading_data[trading_data['probability'] < 0.45].copy()
    
    trades = []
    
    # Long trades
    for _, row in confident_long.iterrows():
        trade_return = row['next_day_return'] * 100  # Convert to percentage
        trades.append({
            'date': row['date'],
            'direction': 'long',
            'entry_price': row['spx_close'],
            'confidence': row['probability'],
            'return_pct': trade_return,
            'win': trade_return > 0
        })
    
    # Short trades
    for _, row in confident_short.iterrows():
        trade_return = -row['next_day_return'] * 100  # Inverse for short
        trades.append({
            'date': row['date'],
            'direction': 'short',
            'entry_price': row['spx_close'],
            'confidence': 1 - row['probability'],
            'return_pct': trade_return,
            'win': trade_return > 0
        })
    
    if len(trades) == 0:
        print("   ❌ No confident trades found")
        return False
    
    # 5. Calculate performance
    print("5. Calculating performance...")
    
    trades_df = pd.DataFrame(trades)
    trades_df = trades_df.sort_values('date')
    
    # Basic metrics
    total_trades = len(trades_df)
    win_rate = trades_df['win'].mean()
    avg_return = trades_df['return_pct'].mean()
    total_return = trades_df['return_pct'].sum()
    
    # Monthly analysis
    trades_df['month'] = pd.to_datetime(trades_df['date']).dt.to_period('M')
    monthly_returns = trades_df.groupby('month')['return_pct'].sum()
    
    positive_months = (monthly_returns > 0).sum()
    total_months = len(monthly_returns)
    monthly_win_rate = positive_months / total_months if total_months > 0 else 0
    
    # Cumulative returns
    trades_df['cumulative_return'] = trades_df['return_pct'].cumsum()
    
    print(f"\\n📊 ROBUST SYSTEM PERFORMANCE:")
    print(f"   🎯 Total Trades: {total_trades}")
    print(f"   🏆 Win Rate: {win_rate:.1%}")
    print(f"   💰 Average Return per Trade: {avg_return:.3f}%")
    print(f"   📈 Total Return: {total_return:.2f}%")
    print(f"   📅 Monthly Win Rate: {monthly_win_rate:.1%} ({positive_months}/{total_months})")
    print(f"   📊 Best Month: {monthly_returns.max():.2f}%")
    print(f"   📉 Worst Month: {monthly_returns.min():.2f}%")
    print(f"   📈 Final Cumulative Return: {trades_df['cumulative_return'].iloc[-1]:.2f}%")
    
    # 6. Save results
    print("\\n6. Saving results...")
    
    # Save trades
    trades_df.to_csv('robust_system_trades.csv', index=False)
    
    # Save summary
    results = {
        'system': 'Robust Baseline SPX System',
        'total_trades': total_trades,
        'win_rate': win_rate,
        'avg_return_per_trade': avg_return,
        'total_return': total_return,
        'monthly_win_rate': monthly_win_rate,
        'best_month': monthly_returns.max(),
        'worst_month': monthly_returns.min(),
        'final_cumulative_return': trades_df['cumulative_return'].iloc[-1],
        'features_used': len(selected_features),
        'prediction_accuracy': (predictions == clean_data['target']).mean()
    }
    
    with open('robust_system_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"   ✅ Trades saved: robust_system_trades.csv")
    print(f"   ✅ Results saved: robust_system_results.json")
    
    # 7. Success criteria
    print("\\n7. Evaluating system success...")
    
    success_criteria = [
        (win_rate > 0.52, f"Win rate {win_rate:.1%} > 52%"),
        (total_return > 3.0, f"Total return {total_return:.1f}% > 3%"),
        (monthly_win_rate > 0.6, f"Monthly win rate {monthly_win_rate:.1%} > 60%"),
        (total_trades > 30, f"Total trades {total_trades} > 30"),
        (avg_return > 0, f"Average return {avg_return:.3f}% > 0%")
    ]
    
    passed_criteria = sum(1 for passed, _ in success_criteria if passed)
    
    print(f"\\n   📋 Success Criteria ({passed_criteria}/5 passed):")
    for passed, description in success_criteria:
        status = "✅" if passed else "❌"
        print(f"      {status} {description}")
    
    if passed_criteria >= 4:
        print("\\n🎉 ROBUST SYSTEM IS SUCCESSFUL!")
        print("   The system meets most success criteria and is ready for use.")
        return True
    else:
        print("\\n⚠️ System needs improvement")
        print("   Consider adjusting parameters or adding features.")
        return False

if __name__ == "__main__":
    success = run_complete_robust_system()
    
    if success:
        print("\\n🚀 Robust SPX system is operational and profitable!")
    else:
        print("\\n🔧 System requires further optimization")
