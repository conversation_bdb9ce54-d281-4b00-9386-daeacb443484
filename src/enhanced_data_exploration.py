#!/usr/bin/env python3
"""
Enhanced Data Exploration with SPX + VIX Options Data
====================================================

Loads and processes SPX options data enhanced with VIX options data
for improved predictive capabilities.

Author: Enhanced by Manus AI
Date: June 23, 2025
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Import configuration and enhanced data loader
from config import config
from enhanced_data_loader import EnhancedDataLoader

def main():
    print("=== Enhanced SPX + VIX Options Data Exploration ===")
    
    # Check if processed data already exists
    from pathlib import Path
    processed_file = Path(config.get_data_file('processed_data'))
    if processed_file.exists():
        print(f"✅ Enhanced processed data already exists: {processed_file}")
        print("   Delete the file to force reprocessing")
        return
    
    try:
        # Load enhanced data with VIX
        print("\n1. Loading enhanced SPX + VIX options data...")
        loader = EnhancedDataLoader(include_vix=True)
        df = loader.load_enhanced_data()
        
        if df.empty:
            raise RuntimeError("No data loaded")
        
        print(f"\n2. Enhanced data overview:")
        print(f"   📊 Shape: {df.shape}")
        print(f"   📅 Date range: {df['date'].min().date()} to {df['date'].max().date()}")
        
        # Show feature breakdown
        spx_features = [col for col in df.columns if not col.startswith('vix_') and col != 'date']
        vix_features = [col for col in df.columns if col.startswith('vix_')]
        
        print(f"   📈 SPX features: {len(spx_features)}")
        print(f"   📊 VIX features: {len(vix_features)}")
        print(f"   🎯 Total features: {len(spx_features) + len(vix_features)}")
        
        # Basic data quality checks
        print(f"\n3. Enhanced data quality:")
        print(f"   📊 Missing values: {df.isnull().sum().sum():,}")
        print(f"   🔄 Duplicate rows: {df.duplicated().sum():,}")
        print(f"   💾 Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.1f} MB")
        
        # Check VIX data coverage
        if vix_features:
            vix_coverage = df[vix_features[0]].notna().sum() / len(df)
            print(f"   📊 VIX data coverage: {vix_coverage:.1%}")
        
        # Basic statistics for key columns
        print(f"\n4. Key statistics:")
        
        # SPX statistics
        if 'spx_close' in df.columns:
            print(f"   📈 SPX Close: ${df['spx_close'].min():.0f} - ${df['spx_close'].max():.0f}")
            print(f"   📊 SPX Avg: ${df['spx_close'].mean():.0f}")
        
        # VIX statistics
        if 'vix_close' in df.columns:
            vix_data = df['vix_close'].dropna()
            if len(vix_data) > 0:
                print(f"   📊 VIX Close: {vix_data.min():.1f} - {vix_data.max():.1f}")
                print(f"   📊 VIX Avg: {vix_data.mean():.1f}")
        
        # Volume statistics
        if 'total_volume' in df.columns:
            print(f"   📊 SPX Volume: {df['total_volume'].min():,.0f} - {df['total_volume'].max():,.0f}")
        
        if 'vix_total_volume' in df.columns:
            vix_vol = df['vix_total_volume'].dropna()
            if len(vix_vol) > 0:
                print(f"   📊 VIX Volume: {vix_vol.min():,.0f} - {vix_vol.max():,.0f}")
        
        # Data preprocessing
        print(f"\n5. Preprocessing enhanced data...")
        
        # Sort by date
        df = df.sort_values('date').reset_index(drop=True)
        
        # Handle missing values
        print(f"   🔧 Handling missing values...")
        
        # Forward fill VIX data (common for VIX to have gaps)
        for col in vix_features:
            if col in df.columns:
                df[col] = df[col].fillna(method='ffill').fillna(method='bfill')
        
        # Fill remaining missing values with median for numeric columns
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if col != 'date':
                df[col] = df[col].fillna(df[col].median())
        
        # Remove any remaining rows with missing critical data
        critical_columns = ['spx_close', 'date']
        df = df.dropna(subset=critical_columns)
        
        print(f"   ✅ After preprocessing: {df.shape}")
        
        # Create target variable (next day direction)
        print(f"   🎯 Creating target variable...")
        df['spx_return'] = df['spx_close'].pct_change()
        df['next_day_return'] = df['spx_return'].shift(-1)
        df['target_direction'] = (df['next_day_return'] > 0).astype(int)
        
        # Remove last row (no next day data)
        df = df[:-1].copy()
        
        print(f"   ✅ Final enhanced dataset: {df.shape}")
        
        # Show target distribution
        target_dist = df['target_direction'].value_counts()
        print(f"   📊 Target distribution:")
        print(f"      Up days (1): {target_dist.get(1, 0)} ({target_dist.get(1, 0)/len(df)*100:.1f}%)")
        print(f"      Down days (0): {target_dist.get(0, 0)} ({target_dist.get(0, 0)/len(df)*100:.1f}%)")
        
        # Save enhanced processed data
        print(f"\n6. Saving enhanced processed data...")
        df.to_csv(processed_file, index=False)
        print(f"   ✅ Saved to: {processed_file}")
        
        # Show sample of VIX features
        if vix_features:
            print(f"\n7. Sample VIX features added:")
            sample_vix = vix_features[:8]  # Show first 8 VIX features
            for feature in sample_vix:
                if feature in df.columns:
                    sample_val = df[feature].dropna()
                    if len(sample_val) > 0:
                        print(f"   📊 {feature}: {sample_val.mean():.3f} (avg)")
            
            if len(vix_features) > 8:
                print(f"   ... and {len(vix_features) - 8} more VIX features")
        
        # Correlation analysis between VIX and SPX
        if 'vix_close' in df.columns and 'spx_close' in df.columns:
            vix_spx_corr = df['vix_close'].corr(df['spx_close'])
            vix_return_corr = df.get('vix_return', pd.Series()).corr(df['spx_return'])
            
            print(f"\n8. VIX-SPX Relationship:")
            print(f"   📊 VIX-SPX Price Correlation: {vix_spx_corr:.3f}")
            if not pd.isna(vix_return_corr):
                print(f"   📊 VIX-SPX Return Correlation: {vix_return_corr:.3f}")
        
        print(f"\n✅ Enhanced data exploration completed successfully!")
        print(f"   📊 Enhanced dataset ready with {len(spx_features)} SPX + {len(vix_features)} VIX features")
        
    except Exception as e:
        print(f"❌ Enhanced data exploration failed: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    main()
