import pandas as pd
import numpy as np
import json
from sklearn.model_selection import train_test_split, TimeSeriesSplit, cross_val_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.ensemble import IsolationForest
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.metrics import classification_report, confusion_matrix
import xgboost as xgb
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

print("=== SPX Option Predictive Model Development ===")

# Import configuration
from config import config

# Load engineered data and feature info
data = pd.read_csv(config.get_data_file('engineered_data'))
with open(config.get_data_file('feature_info'), 'r') as f:
    feature_info = json.load(f)

print(f"Loaded data shape: {data.shape}")

# Prepare data for modeling
print("\n1. Preparing data for modeling...")

# Get features and targets
all_features = feature_info['all_features']
target_col = 'target_direction'

# Remove rows with missing targets
data_clean = data.dropna(subset=[target_col]).copy()
print(f"Data after removing missing targets: {data_clean.shape}")

# Handle missing values in features
X = data_clean[all_features].copy()
y = data_clean[target_col].copy()

# Fill missing values with median for numerical features
for col in X.columns:
    if X[col].dtype in ['float64', 'int64']:
        X[col] = X[col].fillna(X[col].median())

# Remove features with too many missing values (>50%)
missing_pct = X.isnull().sum() / len(X)
features_to_keep = missing_pct[missing_pct <= 0.5].index.tolist()
X = X[features_to_keep]

print(f"Features after removing high-missing: {X.shape[1]}")
print(f"Target distribution: {y.value_counts().to_dict()}")

# 2. Create time-based train/test split
print("\n2. Creating time-based train/test split...")
# Use first 80% for training, last 20% for testing
split_idx = int(len(X) * 0.8)
X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]

print(f"Training set: {X_train.shape}")
print(f"Test set: {X_test.shape}")

# 3. Feature scaling
print("\n3. Scaling features...")
scaler = RobustScaler()  # More robust to outliers than StandardScaler
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Convert back to DataFrames for easier handling
X_train_scaled = pd.DataFrame(X_train_scaled, columns=X_train.columns, index=X_train.index)
X_test_scaled = pd.DataFrame(X_test_scaled, columns=X_test.columns, index=X_test.index)

# 4. Feature selection using importance
print("\n4. Feature selection...")
# Use Random Forest for initial feature importance
rf_selector = RandomForestClassifier(n_estimators=100, random_state=42)
rf_selector.fit(X_train_scaled, y_train)

# Get feature importance
feature_importance = pd.DataFrame({
    'feature': X_train_scaled.columns,
    'importance': rf_selector.feature_importances_
}).sort_values('importance', ascending=False)

# Select top features
top_features = feature_importance.head(50)['feature'].tolist()
X_train_selected = X_train_scaled[top_features]
X_test_selected = X_test_scaled[top_features]

print(f"Selected top {len(top_features)} features")
print("Top 10 most important features:")
for i, (feat, imp) in enumerate(zip(feature_importance['feature'][:10], feature_importance['importance'][:10])):
    print(f"  {i+1}. {feat}: {imp:.4f}")

# 5. Model Development
print("\n5. Developing multiple models...")

models = {}

# Logistic Regression
models['logistic'] = LogisticRegression(random_state=42, max_iter=1000)

# Random Forest
models['random_forest'] = RandomForestClassifier(
    n_estimators=200, 
    max_depth=10, 
    min_samples_split=5,
    random_state=42
)

# Gradient Boosting
models['gradient_boost'] = GradientBoostingClassifier(
    n_estimators=200,
    learning_rate=0.1,
    max_depth=6,
    random_state=42
)

# XGBoost
models['xgboost'] = xgb.XGBClassifier(
    n_estimators=200,
    learning_rate=0.1,
    max_depth=6,
    random_state=42,
    eval_metric='logloss'
)

# LightGBM
models['lightgbm'] = lgb.LGBMClassifier(
    n_estimators=200,
    learning_rate=0.1,
    max_depth=6,
    random_state=42,
    verbose=-1
)

# SVM (with probability for ensemble)
models['svm'] = SVC(probability=True, random_state=42)

# 6. Train individual models
print("\n6. Training individual models...")
model_results = {}

for name, model in models.items():
    print(f"Training {name}...")
    
    # Train model
    model.fit(X_train_selected, y_train)
    
    # Predictions
    train_pred = model.predict(X_train_selected)
    test_pred = model.predict(X_test_selected)
    
    # Probabilities for ensemble
    train_proba = model.predict_proba(X_train_selected)[:, 1]
    test_proba = model.predict_proba(X_test_selected)[:, 1]
    
    # Metrics
    train_acc = accuracy_score(y_train, train_pred)
    test_acc = accuracy_score(y_test, test_pred)
    test_auc = roc_auc_score(y_test, test_proba)
    
    model_results[name] = {
        'model': model,
        'train_accuracy': train_acc,
        'test_accuracy': test_acc,
        'test_auc': test_auc,
        'train_predictions': train_pred,
        'test_predictions': test_pred,
        'train_probabilities': train_proba,
        'test_probabilities': test_proba
    }
    
    print(f"  Train Accuracy: {train_acc:.4f}")
    print(f"  Test Accuracy: {test_acc:.4f}")
    print(f"  Test AUC: {test_auc:.4f}")

# 7. Create Ensemble Models
print("\n7. Creating ensemble models...")

# Voting Classifier (Hard voting)
voting_hard = VotingClassifier(
    estimators=[
        ('rf', models['random_forest']),
        ('gb', models['gradient_boost']),
        ('xgb', models['xgboost']),
        ('lgb', models['lightgbm'])
    ],
    voting='hard'
)

# Voting Classifier (Soft voting)
voting_soft = VotingClassifier(
    estimators=[
        ('rf', models['random_forest']),
        ('gb', models['gradient_boost']),
        ('xgb', models['xgboost']),
        ('lgb', models['lightgbm'])
    ],
    voting='soft'
)

# Train ensemble models
print("Training hard voting ensemble...")
voting_hard.fit(X_train_selected, y_train)
hard_train_pred = voting_hard.predict(X_train_selected)
hard_test_pred = voting_hard.predict(X_test_selected)

print("Training soft voting ensemble...")
voting_soft.fit(X_train_selected, y_train)
soft_train_pred = voting_soft.predict(X_train_selected)
soft_test_pred = voting_soft.predict(X_test_selected)
soft_test_proba = voting_soft.predict_proba(X_test_selected)[:, 1]

# Add ensemble results
model_results['voting_hard'] = {
    'model': voting_hard,
    'train_accuracy': accuracy_score(y_train, hard_train_pred),
    'test_accuracy': accuracy_score(y_test, hard_test_pred),
    'test_auc': None,  # Hard voting doesn't provide probabilities
    'train_predictions': hard_train_pred,
    'test_predictions': hard_test_pred
}

model_results['voting_soft'] = {
    'model': voting_soft,
    'train_accuracy': accuracy_score(y_train, soft_train_pred),
    'test_accuracy': accuracy_score(y_test, soft_test_pred),
    'test_auc': roc_auc_score(y_test, soft_test_proba),
    'train_predictions': soft_train_pred,
    'test_predictions': soft_test_pred,
    'test_probabilities': soft_test_proba
}

print(f"Hard Voting - Train Acc: {model_results['voting_hard']['train_accuracy']:.4f}, Test Acc: {model_results['voting_hard']['test_accuracy']:.4f}")
print(f"Soft Voting - Train Acc: {model_results['voting_soft']['train_accuracy']:.4f}, Test Acc: {model_results['voting_soft']['test_accuracy']:.4f}, AUC: {model_results['voting_soft']['test_auc']:.4f}")

# 8. Outlier-aware predictions using Isolation Forest
print("\n8. Creating outlier-aware predictions...")

# Fit Isolation Forest on training data
iso_forest = IsolationForest(contamination=0.1, random_state=42)
iso_forest.fit(X_train_selected)

# Detect outliers in test set
test_outliers = iso_forest.predict(X_test_selected)
outlier_mask = test_outliers == -1

print(f"Detected {sum(outlier_mask)} outliers in test set ({sum(outlier_mask)/len(outlier_mask)*100:.1f}%)")

# Create outlier-adjusted predictions (use ensemble for normal days, conservative for outliers)
best_model_name = max(model_results.keys(), key=lambda x: model_results[x]['test_accuracy'] if model_results[x]['test_accuracy'] is not None else 0)
best_predictions = model_results[best_model_name]['test_predictions'].copy()

# For outlier days, use more conservative predictions (bias towards no trade)
outlier_adjusted_predictions = best_predictions.copy()
# You could implement specific logic here for outlier days

model_results['outlier_adjusted'] = {
    'test_predictions': outlier_adjusted_predictions,
    'test_accuracy': accuracy_score(y_test, outlier_adjusted_predictions),
    'outlier_mask': outlier_mask
}

# 9. Model Summary
print("\n9. Model Performance Summary:")
print("="*60)
print(f"{'Model':<20} {'Train Acc':<12} {'Test Acc':<12} {'Test AUC':<12}")
print("="*60)

for name, results in model_results.items():
    if name == 'outlier_adjusted':
        continue
    train_acc = results.get('train_accuracy', 0)
    test_acc = results.get('test_accuracy', 0)
    test_auc = results.get('test_auc', 0)
    auc_str = f"{test_auc:.4f}" if test_auc else "N/A"
    print(f"{name:<20} {train_acc:<12.4f} {test_acc:<12.4f} {auc_str:<12}")

print("="*60)

# 10. Save models and results
print("\n10. Saving models and results...")

# Save the best performing model
best_model = model_results[best_model_name]['model']
import pickle

with open(config.get_model_file('best_model'), 'wb') as f:
    pickle.dump(best_model, f)

with open(config.get_model_file('scaler'), 'wb') as f:
    pickle.dump(scaler, f)

# Save feature list
with open(config.get_data_file('selected_features'), 'w') as f:
    json.dump(top_features, f)

# Save model results summary
results_summary = {}
for name, results in model_results.items():
    if name == 'outlier_adjusted':
        continue
    results_summary[name] = {
        'train_accuracy': results.get('train_accuracy', 0),
        'test_accuracy': results.get('test_accuracy', 0),
        'test_auc': results.get('test_auc', 0)
    }

with open(config.get_data_file('model_results'), 'w') as f:
    json.dump(results_summary, f, indent=2)

print(f"Best model ({best_model_name}) saved to: {config.get_model_file('best_model')}")
print(f"Scaler saved to: {config.get_model_file('scaler')}")
print(f"Selected features saved to: {config.get_data_file('selected_features')}")
print(f"Model results saved to: {config.get_data_file('model_results')}")

print("\n=== Model development completed ===")

