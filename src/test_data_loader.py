#!/usr/bin/env python3
"""
Test script for the new data loader functionality
"""

from data_loader import DataLoader, list_available_tickers, get_date_range_for_ticker, load_ticker_data
from config import config
import pandas as pd

def test_data_discovery():
    """Test data discovery functionality"""
    print("=== Testing Data Discovery ===")
    
    # List available tickers
    print("\n1. Available tickers:")
    tickers = list_available_tickers()
    if tickers:
        for ticker in tickers:
            print(f"   📈 {ticker}")
    else:
        print("   No tickers found")
    
    # Get date range for SPX
    if 'SPX' in tickers:
        print("\n2. SPX date range:")
        date_info = get_date_range_for_ticker('spx')
        if 'date_range' in date_info and date_info['date_range']:
            dr = date_info['date_range']
            print(f"   📅 Start: {dr['start'].date()}")
            print(f"   📅 End: {dr['end'].date()}")
            print(f"   📊 Days: {dr['days']}")
            print(f"   📄 Files: {date_info['files']}")
        else:
            print(f"   ❌ Error: {date_info.get('error', 'No data found')}")

def test_data_loading():
    """Test loading a small sample of data"""
    print("\n=== Testing Data Loading ===")
    
    try:
        # Create loader with SPX filter
        loader = DataLoader(ticker_filter='spx')
        files = loader.find_data_files()
        
        if not files:
            print("❌ No SPX files found")
            return
        
        # Load just the first file as a test
        print(f"\n3. Testing load of first file: {files[0].name}")
        df = loader.load_single_file(files[0])
        
        print(f"   ✅ Shape: {df.shape}")
        print(f"   📊 Columns: {list(df.columns)}")
        
        if 'date' in df.columns:
            print(f"   📅 Date range: {df['date'].min()} to {df['date'].max()}")
        
        # Show sample data
        print(f"\n   📋 Sample data (first 3 rows):")
        print(df.head(3).to_string())
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")

def test_full_loading():
    """Test loading all SPX data"""
    print("\n=== Testing Full Data Loading ===")
    
    try:
        print("\n4. Loading all SPX data...")
        loader = DataLoader(ticker_filter='spx')
        
        # Load all data
        data = loader.load_all_data()
        
        # Get summary
        summary = loader.get_data_summary(data)
        
        print(f"\n📊 Full Dataset Summary:")
        print(f"   Records: {summary['total_records']:,}")
        print(f"   Columns: {summary['total_columns']}")
        print(f"   Memory: {summary['memory_usage_mb']:.1f} MB")
        print(f"   Missing values: {summary['missing_values']:,}")
        
        if summary['date_range']:
            dr = summary['date_range']
            print(f"   Date range: {dr['start'].date()} to {dr['end'].date()}")
            print(f"   Total days: {dr['days']}")
        
        if 'files_distribution' in summary:
            print(f"   Source files: {summary['source_files']}")
            print(f"   Records per file (sample):")
            for file, count in list(summary['files_distribution'].items())[:5]:
                print(f"      {file}: {count}")
            if len(summary['files_distribution']) > 5:
                print(f"      ... and {len(summary['files_distribution']) - 5} more files")
        
        return data
        
    except Exception as e:
        print(f"❌ Error loading full dataset: {e}")
        return None

def main():
    """Main test function"""
    print("🚀 SPX Data Loader Test")
    print("=" * 50)
    
    # Show current configuration
    print(f"📁 Data directory: {config.get_data_source_config('data_directory')}")
    print(f"🔍 File pattern: {config.get_data_source_config('file_pattern')}")
    print(f"🎯 Ticker filter: {config.get_data_source_config('ticker_filter')}")
    
    # Run tests
    test_data_discovery()
    test_data_loading()
    
    # Ask user if they want to load all data
    print("\n" + "=" * 50)
    response = input("Load all SPX data? This may take a moment (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        data = test_full_loading()
        if data is not None:
            print(f"\n✅ Successfully loaded {len(data):,} records!")
            print("   Data is ready for analysis and modeling.")
    else:
        print("⏭️  Skipping full data load.")
    
    print("\n🎉 Data loader test completed!")

if __name__ == "__main__":
    main()
