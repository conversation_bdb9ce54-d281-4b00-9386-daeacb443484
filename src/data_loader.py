"""
SPX Predictive System - Data Loader
===================================

Flexible data loading utilities for handling multiple data files.

Author: Manus AI
Date: June 23, 2025
"""

import pandas as pd
import numpy as np
from pathlib import Path
import glob
import warnings
from typing import List, Optional, Dict, Any
from datetime import datetime

from config import config

class DataLoader:
    """Flexible data loader for SPX options data"""
    
    def __init__(self, data_directory: Optional[str] = None, file_pattern: Optional[str] = None, ticker_filter: Optional[str] = None):
        """
        Initialize data loader

        Args:
            data_directory: Directory containing data files (uses config default if None)
            file_pattern: Pattern to match files (uses config default if None)
            ticker_filter: Filter for specific ticker (uses config default if None)
        """
        self.data_directory = Path(data_directory) if data_directory else config.get_data_source_config('data_directory')
        self.file_pattern = file_pattern or config.get_data_source_config('file_pattern')
        self.recursive_search = config.get_data_source_config('recursive_search')
        self.ticker_filter = ticker_filter if ticker_filter is not None else config.get_data_source_config('ticker_filter')
        self.date_column = config.get_data_source_config('date_column')
        self.sort_by_date = config.get_data_source_config('sort_by_date')
        self.validate_data = config.get_data_source_config('validate_data')
        self.file_encoding = config.get_data_source_config('file_encoding')

        # Check if data directory exists
        if not self.data_directory.exists():
            print(f"⚠️  Data directory does not exist: {self.data_directory}")
        else:
            print(f"📁 Data directory found: {self.data_directory}")
    
    def find_data_files(self) -> List[Path]:
        """Find all data files matching the pattern"""
        print(f"📁 Data directory: {self.data_directory}")
        print(f"🔍 File pattern: {self.file_pattern}")
        print(f"🔄 Recursive search: {self.recursive_search}")
        print(f"🎯 Ticker filter: {self.ticker_filter}")

        file_paths = []

        if self.recursive_search:
            # Search recursively through all subdirectories
            pattern = f"**/{self.file_pattern}"
            files = glob.glob(str(self.data_directory / pattern), recursive=True)
        else:
            # Search only in the specified directory
            pattern_path = self.data_directory / self.file_pattern
            files = glob.glob(str(pattern_path))

        # Convert to Path objects and sort
        all_file_paths = [Path(f) for f in sorted(files)]

        # Apply ticker filter if specified
        if self.ticker_filter:
            for file_path in all_file_paths:
                # Check if ticker is in the filename
                if self.ticker_filter.lower() in file_path.name.lower():
                    file_paths.append(file_path)
        else:
            file_paths = all_file_paths

        print(f"📄 Found {len(file_paths)} matching files:")

        if not file_paths:
            print("   No files found!")
            print(f"   Searched in: {self.data_directory}")
            print(f"   Pattern: {self.file_pattern}")
            if self.ticker_filter:
                print(f"   Ticker filter: {self.ticker_filter}")
        else:
            for i, file_path in enumerate(file_paths, 1):
                try:
                    size_mb = file_path.stat().st_size / (1024 * 1024)
                    rel_path = file_path.relative_to(self.data_directory)
                    print(f"  {i}. {rel_path} ({size_mb:.1f} MB)")
                except Exception as e:
                    print(f"  {i}. {file_path.name} (size unknown: {e})")

        return file_paths
    
    def load_single_file(self, file_path: Path) -> pd.DataFrame:
        """Load a single data file"""
        try:
            print(f"📖 Loading: {file_path.name}")
            
            # Load the file
            df = pd.read_csv(file_path, encoding=self.file_encoding)
            
            # Convert date column if it exists
            if self.date_column in df.columns:
                df[self.date_column] = pd.to_datetime(df[self.date_column])
            
            print(f"   ✅ Shape: {df.shape}")
            if self.date_column in df.columns:
                print(f"   📅 Date range: {df[self.date_column].min().date()} to {df[self.date_column].max().date()}")
            
            return df
            
        except Exception as e:
            print(f"   ❌ Error loading {file_path.name}: {str(e)}")
            raise
    
    def validate_dataframe(self, df: pd.DataFrame, file_name: str = "") -> bool:
        """Validate loaded dataframe"""
        issues = []
        
        # Check for required columns
        required_columns = ['date', 'spx_close']
        missing_cols = [col for col in required_columns if col not in df.columns]
        if missing_cols:
            issues.append(f"Missing required columns: {missing_cols}")
        
        # Check for empty dataframe
        if df.empty:
            issues.append("Dataframe is empty")
        
        # Check for duplicate dates
        if self.date_column in df.columns:
            duplicates = df[self.date_column].duplicated().sum()
            if duplicates > 0:
                issues.append(f"Found {duplicates} duplicate dates")
        
        # Check for excessive missing values
        missing_pct = df.isnull().sum() / len(df) * 100
        high_missing = missing_pct[missing_pct > 50]
        if len(high_missing) > 0:
            issues.append(f"Columns with >50% missing values: {high_missing.index.tolist()}")
        
        if issues:
            print(f"   ⚠️  Validation issues in {file_name}:")
            for issue in issues:
                print(f"      - {issue}")
            return False
        else:
            print(f"   ✅ Validation passed for {file_name}")
            return True
    
    def combine_dataframes(self, dataframes: List[pd.DataFrame], file_names: List[str]) -> pd.DataFrame:
        """Combine multiple dataframes"""
        print(f"\n🔗 Combining {len(dataframes)} dataframes...")
        
        # Add source file information
        for i, (df, file_name) in enumerate(zip(dataframes, file_names)):
            df = df.copy()
            df['source_file'] = file_name
            dataframes[i] = df
        
        # Combine all dataframes
        combined_df = pd.concat(dataframes, ignore_index=True)
        
        # Sort by date if requested
        if self.sort_by_date and self.date_column in combined_df.columns:
            combined_df = combined_df.sort_values(self.date_column).reset_index(drop=True)
            print(f"   📅 Sorted by {self.date_column}")
        
        print(f"   ✅ Combined shape: {combined_df.shape}")
        if self.date_column in combined_df.columns:
            print(f"   📅 Date range: {combined_df[self.date_column].min().date()} to {combined_df[self.date_column].max().date()}")
        
        # Show source file distribution
        if 'source_file' in combined_df.columns:
            print(f"   📊 Records per file:")
            file_counts = combined_df['source_file'].value_counts().sort_index()
            for file_name, count in file_counts.items():
                print(f"      {file_name}: {count} records")
        
        return combined_df
    
    def load_all_data(self) -> pd.DataFrame:
        """Load and combine all data files"""
        print("=== Loading SPX Options Data ===")
        print(f"🕐 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Find all data files
        file_paths = self.find_data_files()
        
        if not file_paths:
            raise FileNotFoundError(f"No files found matching pattern: {self.file_pattern} in {self.data_directory}")
        
        # Load all files
        print(f"\n📚 Loading {len(file_paths)} files...")
        dataframes = []
        file_names = []
        
        for file_path in file_paths:
            try:
                df = self.load_single_file(file_path)
                
                # Validate if requested
                if self.validate_data:
                    self.validate_dataframe(df, file_path.name)
                
                dataframes.append(df)
                file_names.append(file_path.name)
                
            except Exception as e:
                print(f"❌ Failed to load {file_path.name}: {str(e)}")
                print("   Continuing with other files...")
                continue
        
        if not dataframes:
            raise RuntimeError("No files were successfully loaded")
        
        # Combine all dataframes
        combined_df = self.combine_dataframes(dataframes, file_names)
        
        print(f"\n✅ Data loading completed successfully!")
        print(f"   Final dataset shape: {combined_df.shape}")
        
        return combined_df
    
    def get_data_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Get summary statistics for the loaded data"""
        summary = {
            'total_records': len(df),
            'total_columns': len(df.columns),
            'date_range': None,
            'missing_values': df.isnull().sum().sum(),
            'duplicate_rows': df.duplicated().sum(),
            'memory_usage_mb': df.memory_usage(deep=True).sum() / (1024 * 1024),
        }
        
        if self.date_column in df.columns:
            summary['date_range'] = {
                'start': df[self.date_column].min(),
                'end': df[self.date_column].max(),
                'days': (df[self.date_column].max() - df[self.date_column].min()).days
            }
        
        if 'source_file' in df.columns:
            summary['source_files'] = df['source_file'].nunique()
            summary['files_distribution'] = df['source_file'].value_counts().to_dict()
        
        return summary

# Convenience functions
def load_spx_data(data_directory: Optional[str] = None, file_pattern: Optional[str] = None, ticker_filter: Optional[str] = None) -> pd.DataFrame:
    """
    Convenience function to load SPX options data

    Args:
        data_directory: Directory containing data files (optional)
        file_pattern: Pattern to match files (optional)
        ticker_filter: Filter for specific ticker (optional)

    Returns:
        Combined dataframe with all data
    """
    loader = DataLoader(data_directory, file_pattern, ticker_filter)
    return loader.load_all_data()

def load_ticker_data(ticker: str, data_directory: Optional[str] = None) -> pd.DataFrame:
    """
    Convenience function to load data for a specific ticker

    Args:
        ticker: Ticker symbol (e.g., 'spx', 'spy')
        data_directory: Directory containing data files (optional)

    Returns:
        Combined dataframe with ticker data
    """
    loader = DataLoader(data_directory=data_directory, ticker_filter=ticker)
    return loader.load_all_data()

def get_default_data_directory() -> Path:
    """Get the default data directory from config"""
    return config.get_data_source_config('data_directory')

def set_data_directory(directory: str):
    """Set the default data directory in config"""
    config.set_data_source_config('data_directory', Path(directory))

def set_file_pattern(pattern: str):
    """Set the default file pattern in config"""
    config.set_data_source_config('file_pattern', pattern)

def list_available_tickers(data_directory: Optional[str] = None) -> List[str]:
    """
    List all available tickers in the data directory

    Args:
        data_directory: Directory to search (optional)

    Returns:
        List of ticker symbols found
    """
    data_dir = Path(data_directory) if data_directory else config.get_data_source_config('data_directory')

    if not data_dir.exists():
        print(f"❌ Directory not found: {data_dir}")
        return []

    # Search for all option analysis files
    pattern = "**/*_option_daily_analysis_*.csv"
    files = glob.glob(str(data_dir / pattern), recursive=True)

    tickers = set()
    for file_path in files:
        file_name = Path(file_path).name
        # Extract ticker from filename pattern: {ticker}_option_daily_analysis_{year}_{quarter}.csv
        parts = file_name.split('_option_daily_analysis_')
        if len(parts) == 2:
            ticker = parts[0]
            tickers.add(ticker.upper())

    return sorted(list(tickers))

def get_date_range_for_ticker(ticker: str, data_directory: Optional[str] = None) -> Dict[str, Any]:
    """
    Get the date range available for a specific ticker

    Args:
        ticker: Ticker symbol
        data_directory: Directory to search (optional)

    Returns:
        Dictionary with date range information
    """
    try:
        loader = DataLoader(data_directory=data_directory, ticker_filter=ticker)
        files = loader.find_data_files()

        if not files:
            return {'ticker': ticker, 'files': 0, 'date_range': None}

        # Load just the first and last files to get date range
        first_file = files[0]
        last_file = files[-1]

        first_df = pd.read_csv(first_file, usecols=['date'], nrows=1)
        last_df = pd.read_csv(last_file, usecols=['date'])

        first_date = pd.to_datetime(first_df['date'].iloc[0])
        last_date = pd.to_datetime(last_df['date'].iloc[-1])

        return {
            'ticker': ticker.upper(),
            'files': len(files),
            'date_range': {
                'start': first_date,
                'end': last_date,
                'days': (last_date - first_date).days
            }
        }

    except Exception as e:
        return {'ticker': ticker, 'error': str(e)}
