#!/usr/bin/env python3
"""
Fix Scaler for Backtesting
==========================

Creates a new scaler that only works with the selected features for backtesting.

Author: Manus AI
Date: June 23, 2025
"""

import pandas as pd
import numpy as np
import pickle
import json
from sklearn.preprocessing import StandardScaler
from config import config

def main():
    print("🔧 FIXING SCALER FOR ENHANCED VIX BACKTESTING")
    print("=" * 80)
    
    try:
        # 1. Load the engineered data
        print("1. Loading engineered data...")
        data_file = config.get_data_file('engineered_data')
        df = pd.read_csv(data_file)
        print(f"   📊 Loaded data shape: {df.shape}")
        
        # 2. Load selected features
        print("2. Loading selected features...")
        with open('selected_features.json', 'r') as f:
            selected_features = json.load(f)
        print(f"   🎯 Selected features: {len(selected_features)}")
        
        # 3. Prepare data with only selected features
        print("3. Preparing data with selected features...")
        
        # Remove target and date columns
        feature_columns = [col for col in df.columns if col not in ['date', 'target_direction', 'spx_return', 'next_day_return']]
        
        # Get intersection of selected features and available features
        available_selected = [f for f in selected_features if f in feature_columns]
        print(f"   ✅ Available selected features: {len(available_selected)}")
        
        if len(available_selected) < len(selected_features):
            missing = [f for f in selected_features if f not in feature_columns]
            print(f"   ⚠️  Missing features: {len(missing)}")
            for feat in missing[:5]:
                print(f"      - {feat}")
        
        # 4. Create new scaler with only selected features
        print("4. Creating new scaler for selected features...")
        
        X_selected = df[available_selected].copy()
        
        # Clean the data
        X_selected = X_selected.replace([np.inf, -np.inf], np.nan)
        
        # Fill missing values
        for col in X_selected.columns:
            if X_selected[col].dtype in ['float64', 'int64']:
                median_val = X_selected[col].median()
                if pd.isna(median_val):
                    median_val = 0.0
                X_selected[col] = X_selected[col].fillna(median_val)
        
        # Create and fit new scaler
        new_scaler = StandardScaler()
        new_scaler.fit(X_selected)
        
        print(f"   ✅ New scaler fitted on {X_selected.shape[1]} features")
        
        # 5. Save the new scaler
        print("5. Saving new scaler...")
        
        # Backup original scaler
        import shutil
        shutil.copy('scaler.pkl', 'scaler_original.pkl')
        print("   📁 Original scaler backed up as scaler_original.pkl")
        
        # Save new scaler
        with open('scaler.pkl', 'wb') as f:
            pickle.dump(new_scaler, f)
        print("   ✅ New scaler saved as scaler.pkl")
        
        # 6. Update selected features to match available ones
        print("6. Updating selected features...")
        
        # Backup original selected features
        shutil.copy('selected_features.json', 'selected_features_original.json')
        print("   📁 Original features backed up as selected_features_original.json")
        
        # Save updated selected features
        with open('selected_features.json', 'w') as f:
            json.dump(available_selected, f, indent=2)
        print(f"   ✅ Updated selected features: {len(available_selected)}")
        
        # 7. Test the new scaler
        print("7. Testing new scaler...")
        
        # Load and test
        with open('scaler.pkl', 'rb') as f:
            test_scaler = pickle.load(f)
        
        # Test transform
        X_test = X_selected.tail(10)
        X_scaled = test_scaler.transform(X_test)
        print(f"   ✅ Test successful: {X_test.shape} -> {X_scaled.shape}")
        
        print("\n✅ SCALER FIX COMPLETED!")
        print("   🔧 New scaler works with selected features only")
        print("   📁 Original files backed up")
        print("   🚀 Ready for backtesting")
        
    except Exception as e:
        print(f"❌ Scaler fix failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
