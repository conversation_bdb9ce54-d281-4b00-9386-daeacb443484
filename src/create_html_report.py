#!/usr/bin/env python3
"""
SPX Predictive System - HTML Report Generator
=============================================

Creates a comprehensive HTML report with historical performance,
interactive graphs, backtesting results, and current trading signals.

Author: Manus AI
Date: June 23, 2025
"""

import pandas as pd
import numpy as np
import json
import pickle
import base64
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from io import BytesIO
import warnings
warnings.filterwarnings('ignore')

from config import config

def encode_image_to_base64(fig):
    """Convert matplotlib figure to base64 string"""
    buffer = BytesIO()
    fig.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.getvalue()).decode()
    buffer.close()
    plt.close(fig)
    return image_base64

def create_performance_chart():
    """Create performance chart"""
    # Load backtest results
    best_trades = pd.read_csv(config.get_output_file('best_trades'))
    best_trades['entry_date'] = pd.to_datetime(best_trades['entry_date'])
    best_trades['exit_date'] = pd.to_datetime(best_trades['exit_date'])
    
    # Calculate cumulative returns
    best_trades = best_trades.sort_values('entry_date')
    best_trades['cumulative_return'] = (1 + best_trades['pnl_pct'] / 100).cumprod()
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # Cumulative returns
    ax1.plot(best_trades['entry_date'], best_trades['cumulative_return'], 
             linewidth=2, color='#2E86AB', label='Strategy Returns')
    ax1.axhline(y=1, color='gray', linestyle='--', alpha=0.7)
    ax1.set_title('SPX Options Trading Strategy - Cumulative Returns', fontsize=16, fontweight='bold')
    ax1.set_ylabel('Cumulative Return', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # Monthly returns distribution
    monthly_returns = best_trades.set_index('entry_date')['pnl_pct'].resample('M').sum()
    colors = ['green' if x > 0 else 'red' for x in monthly_returns]
    ax2.bar(monthly_returns.index, monthly_returns.values, color=colors, alpha=0.7)
    ax2.set_title('Monthly Returns Distribution', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Monthly Return (%)', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.8)
    
    plt.tight_layout()
    return encode_image_to_base64(fig)

def create_spx_price_chart():
    """Create SPX price chart with signals"""
    # Load data
    data = pd.read_csv(config.get_data_file('engineered_data'))
    data['date'] = pd.to_datetime(data['date'])
    
    # Load trades for signal overlay
    best_trades = pd.read_csv(config.get_output_file('best_trades'))
    best_trades['entry_date'] = pd.to_datetime(best_trades['entry_date'])
    
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Plot SPX price
    ax.plot(data['date'], data['spx_close'], linewidth=1.5, color='#1f77b4', label='SPX Close')
    
    # Add trade signals
    for _, trade in best_trades.iterrows():
        entry_date = trade['entry_date']
        entry_price = trade['entry_price']
        color = 'green' if trade['pnl_pct'] > 0 else 'red'
        ax.scatter(entry_date, entry_price, color=color, s=50, alpha=0.7, zorder=5)
    
    ax.set_title('SPX Price with Trading Signals (2020-2025)', fontsize=16, fontweight='bold')
    ax.set_ylabel('SPX Price', fontsize=12)
    ax.set_xlabel('Date', fontsize=12)
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # Add annotations
    ax.text(0.02, 0.98, f'Green dots: Profitable trades\nRed dots: Losing trades', 
            transform=ax.transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    return encode_image_to_base64(fig)

def create_options_flow_chart():
    """Create options flow analysis chart"""
    data = pd.read_csv(config.get_data_file('engineered_data'))
    data['date'] = pd.to_datetime(data['date'])
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Put/Call ratio over time
    ax1.plot(data['date'], data['put_call_oi_ratio'], linewidth=1, alpha=0.7, color='purple')
    ax1.set_title('Put/Call Open Interest Ratio', fontweight='bold')
    ax1.set_ylabel('Ratio')
    ax1.grid(True, alpha=0.3)
    
    # Total volume over time
    ax2.plot(data['date'], data['total_volume'], linewidth=1, alpha=0.7, color='orange')
    ax2.set_title('Total Options Volume', fontweight='bold')
    ax2.set_ylabel('Volume')
    ax2.grid(True, alpha=0.3)
    
    # Gamma exposure
    ax3.plot(data['date'], data['total_gamma'], linewidth=1, alpha=0.7, color='red')
    ax3.set_title('Total Gamma Exposure', fontweight='bold')
    ax3.set_ylabel('Gamma')
    ax3.grid(True, alpha=0.3)
    
    # Vega exposure
    ax4.plot(data['date'], data['total_vega'], linewidth=1, alpha=0.7, color='blue')
    ax4.set_title('Total Vega Exposure', fontweight='bold')
    ax4.set_ylabel('Vega')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return encode_image_to_base64(fig)

def get_current_signal():
    """Generate current trading signal"""
    try:
        # Load latest data
        data = pd.read_csv(config.get_data_file('engineered_data'))
        data['date'] = pd.to_datetime(data['date'])

        # Get latest data point
        latest_data = data.iloc[-1].copy()

        # Load backtest results to get best threshold
        with open(config.get_data_file('backtest_results'), 'r') as f:
            backtest_results = json.load(f)

        # Find best performing threshold
        best_threshold = None
        best_score = -999
        for threshold, results in backtest_results.items():
            score = results.get('total_return', 0) * results.get('win_rate', 0)
            if score > best_score:
                best_score = score
                best_threshold = float(threshold)

        # Simple signal based on recent performance and momentum
        recent_return = latest_data.get('spx_return', 0)
        recent_volume = latest_data.get('total_volume', 0)
        put_call_ratio = latest_data.get('put_call_oi_ratio', 1.5)

        # Generate signal based on simple rules
        bullish_signals = 0
        bearish_signals = 0

        # Price momentum
        if recent_return > 0.005:  # > 0.5% return
            bullish_signals += 1
        elif recent_return < -0.005:  # < -0.5% return
            bearish_signals += 1

        # Put/Call ratio (contrarian indicator)
        if put_call_ratio > 2.5:  # High put/call ratio = bullish
            bullish_signals += 1
        elif put_call_ratio < 1.5:  # Low put/call ratio = bearish
            bearish_signals += 1

        # Volume analysis
        avg_volume = data['total_volume'].rolling(20).mean().iloc[-1]
        if recent_volume > avg_volume * 1.2:  # High volume
            if recent_return > 0:
                bullish_signals += 1
            else:
                bearish_signals += 1

        # Determine prediction
        if bullish_signals > bearish_signals:
            prediction = 'BULLISH'
            confidence = min(0.9, 0.5 + (bullish_signals - bearish_signals) * 0.1)
        elif bearish_signals > bullish_signals:
            prediction = 'BEARISH'
            confidence = min(0.9, 0.5 + (bearish_signals - bullish_signals) * 0.1)
        else:
            prediction = 'NEUTRAL'
            confidence = 0.5

        signal_data = {
            'date': latest_data['date'].strftime('%Y-%m-%d'),
            'spx_close': f"{latest_data['spx_close']:.2f}",
            'prediction': prediction,
            'confidence': confidence,
            'probability_up': confidence if prediction == 'BULLISH' else 1 - confidence,
            'probability_down': confidence if prediction == 'BEARISH' else 1 - confidence,
            'recent_return': f"{recent_return:.3f}",
            'put_call_ratio': f"{put_call_ratio:.2f}",
            'volume_vs_avg': f"{recent_volume / avg_volume:.2f}x" if avg_volume > 0 else "N/A"
        }

        return signal_data

    except Exception as e:
        # Fallback signal
        return {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'spx_close': 'N/A',
            'prediction': 'NEUTRAL',
            'confidence': 0.5,
            'probability_up': 0.5,
            'probability_down': 0.5,
            'error': str(e)
        }

def load_performance_metrics():
    """Load performance metrics from backtest results"""
    try:
        with open(config.get_data_file('backtest_results'), 'r') as f:
            backtest_results = json.load(f)

        # Check if we have R-squared optimized results (newest format)
        if 'rsquared_optimized' in backtest_results:
            rsquared_results = backtest_results['rsquared_optimized']
            return {
                'rsquared': rsquared_results.get('rsquared', 0),
                'correlation': rsquared_results.get('correlation', 0),
                'total_return': rsquared_results.get('total_return', 0),
                'win_rate': rsquared_results.get('win_rate', 0),
                'total_trades': rsquared_results.get('total_trades', 0),
                'avg_return_per_trade': rsquared_results.get('avg_return_per_trade', 0),
                'sharpe_ratio': rsquared_results.get('sharpe_ratio', 0),
                'avg_days_held': rsquared_results.get('avg_days_held', 0),
                'trades_per_month': rsquared_results.get('trades_per_month', 0),
                'monthly_consistency': rsquared_results.get('monthly_consistency', 0),
                'max_drawdown': 0,  # Not calculated in R-squared version
                'profit_factor': 0   # Not calculated in R-squared version
            }

        # Check if we have enhanced results (previous format)
        elif 'enhanced' in backtest_results:
            enhanced_results = backtest_results['enhanced']
            # Convert to percentage for display
            return {
                'total_return': enhanced_results.get('total_return', 0) * 100,  # Convert to %
                'win_rate': enhanced_results.get('win_rate', 0),
                'total_trades': enhanced_results.get('total_trades', 0),
                'avg_return_per_trade': enhanced_results.get('avg_return_per_trade', 0) * 100,  # Convert to %
                'sharpe_ratio': enhanced_results.get('sharpe_ratio', 0),
                'annualized_sharpe': enhanced_results.get('annualized_sharpe', 0),
                'sortino_ratio': enhanced_results.get('sortino_ratio', 0),
                'calmar_ratio': enhanced_results.get('calmar_ratio', 0),
                'max_drawdown': enhanced_results.get('max_drawdown', 0) * 100,  # Convert to %
                'profit_factor': enhanced_results.get('profit_factor', 0)
            }

        # Fallback to old threshold-based format
        best_threshold = None
        best_score = -999

        for threshold, results in backtest_results.items():
            if threshold == 'enhanced':
                continue
            score = results.get('total_return', 0) * results.get('win_rate', 0)
            if score > best_score:
                best_score = score
                best_threshold = threshold

        if best_threshold:
            return backtest_results[best_threshold]
        else:
            return {}

    except Exception as e:
        return {'error': str(e)}

def create_html_report():
    """Create comprehensive HTML report"""
    print("🔄 Generating HTML report...")

    # Generate charts
    print("📊 Creating performance chart...")
    performance_chart = create_performance_chart()

    print("📈 Creating SPX price chart...")
    spx_chart = create_spx_price_chart()

    print("📊 Creating options flow chart...")
    options_chart = create_options_flow_chart()

    # Get current signal
    print("🎯 Generating current signal...")
    current_signal = get_current_signal()

    # Load performance metrics
    print("📋 Loading performance metrics...")
    performance_metrics = load_performance_metrics()

    # Load model info
    with open(config.get_data_file('model_results'), 'r') as f:
        model_results = json.load(f)

    # Get data summary
    data = pd.read_csv(config.get_data_file('engineered_data'))
    data_summary = {
        'total_records': len(data),
        'date_range_start': data['date'].min(),
        'date_range_end': data['date'].max(),
        'total_features': len(data.columns) - 1  # Exclude date column
    }

    # Create HTML content
    html_content = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>SPX Predictive System - Performance Report</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
                color: #333;
            }}
            .container {{
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 40px;
                border-bottom: 3px solid #2E86AB;
                padding-bottom: 20px;
            }}
            .header h1 {{
                color: #2E86AB;
                margin: 0;
                font-size: 2.5em;
            }}
            .header p {{
                color: #666;
                margin: 10px 0 0 0;
                font-size: 1.1em;
            }}
            .signal-box {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 25px;
                border-radius: 10px;
                margin: 20px 0;
                text-align: center;
            }}
            .signal-prediction {{
                font-size: 2em;
                font-weight: bold;
                margin: 10px 0;
            }}
            .signal-confidence {{
                font-size: 1.2em;
                opacity: 0.9;
            }}
            .metrics-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin: 30px 0;
            }}
            .metric-card {{
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                border-left: 4px solid #2E86AB;
            }}
            .metric-value {{
                font-size: 2em;
                font-weight: bold;
                color: #2E86AB;
                margin: 10px 0;
            }}
            .metric-label {{
                color: #666;
                font-size: 0.9em;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
            .chart-container {{
                margin: 30px 0;
                text-align: center;
            }}
            .chart-container img {{
                max-width: 100%;
                height: auto;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
            .section-title {{
                font-size: 1.8em;
                color: #2E86AB;
                margin: 40px 0 20px 0;
                border-bottom: 2px solid #eee;
                padding-bottom: 10px;
            }}
            .data-table {{
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }}
            .data-table th, .data-table td {{
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }}
            .data-table th {{
                background-color: #f8f9fa;
                font-weight: bold;
                color: #2E86AB;
            }}
            .footer {{
                text-align: center;
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                color: #666;
            }}
            .bullish {{ color: #28a745; }}
            .bearish {{ color: #dc3545; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>SPX Predictive System</h1>
                <p>Performance Report & Current Signal</p>
                <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>

            <!-- Current Signal Section -->
            <div class="signal-box">
                <h2>Current Trading Signal</h2>
                <div class="signal-prediction {'bullish' if current_signal.get('prediction') == 'BULLISH' else 'bearish'}">
                    {current_signal.get('prediction', 'N/A')}
                </div>
                <div class="signal-confidence">
                    Confidence: {current_signal.get('confidence', 0):.1%}<br>
                    SPX Close: {current_signal.get('spx_close', 'N/A')}<br>
                    Date: {current_signal.get('date', 'N/A')}
                </div>
                <p style="margin-top: 15px; opacity: 0.8;">
                    Probability Up: {current_signal.get('probability_up', 0):.1%} |
                    Probability Down: {current_signal.get('probability_down', 0):.1%}
                </p>
            </div>

            <!-- Performance Metrics -->
            <h2 class="section-title">R-Squared Optimized Strategy Performance</h2>

            <!-- R-Squared Highlight -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 10px; margin: 20px 0; text-align: center;">
                <h3 style="margin: 0 0 15px 0;">🎯 Predictive Consistency Metrics</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <div>
                        <div style="font-size: 2.5em; font-weight: bold; color: #FFD700;">{performance_metrics.get('rsquared', 0):.4f}</div>
                        <div style="font-size: 1.2em; opacity: 0.9;">R-Squared</div>
                        <div style="font-size: 0.9em; opacity: 0.7;">Predictive Accuracy</div>
                    </div>
                    <div>
                        <div style="font-size: 2.5em; font-weight: bold; color: #98FB98;">{performance_metrics.get('correlation', 0):.4f}</div>
                        <div style="font-size: 1.2em; opacity: 0.9;">Correlation</div>
                        <div style="font-size: 0.9em; opacity: 0.7;">Predicted vs Actual</div>
                    </div>
                    <div>
                        <div style="font-size: 2.5em; font-weight: bold; color: #87CEEB;">{performance_metrics.get('trades_per_month', 0):.1f}</div>
                        <div style="font-size: 1.2em; opacity: 0.9;">Trades/Month</div>
                        <div style="font-size: 0.9em; opacity: 0.7;">High Frequency</div>
                    </div>
                </div>
            </div>

            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">{performance_metrics.get('total_return', 0):.2f}%</div>
                    <div class="metric-label">Total Return</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{performance_metrics.get('win_rate', 0):.1%}</div>
                    <div class="metric-label">Win Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" style="color: #28a745; font-weight: bold;">{performance_metrics.get('total_trades', 0)}</div>
                    <div class="metric-label">Total Trades</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{performance_metrics.get('avg_return_per_trade', 0):.3f}%</div>
                    <div class="metric-label">Avg Return/Trade</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{performance_metrics.get('sharpe_ratio', 0):.3f}</div>
                    <div class="metric-label">Sharpe Ratio</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{performance_metrics.get('avg_days_held', 0):.1f}</div>
                    <div class="metric-label">Avg Days Held</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{performance_metrics.get('monthly_consistency', 0):.1%}</div>
                    <div class="metric-label">Monthly Consistency</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{performance_metrics.get('trades_per_month', 0):.1f}</div>
                    <div class="metric-label">Trades per Month</div>
                </div>
            </div>


            <!-- Performance Chart -->
            <h2 class="section-title">Historical Performance</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{performance_chart}" alt="Performance Chart">
            </div>

            <!-- SPX Price Chart -->
            <h2 class="section-title">SPX Price with Trading Signals</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{spx_chart}" alt="SPX Price Chart">
            </div>

            <!-- Options Flow Chart -->
            <h2 class="section-title">Options Flow Analysis</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{options_chart}" alt="Options Flow Chart">
            </div>

            <!-- Model Performance -->
            <h2 class="section-title">Model Performance</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Model</th>
                        <th>Train Accuracy</th>
                        <th>Test Accuracy</th>
                        <th>Test AUC</th>
                    </tr>
                </thead>
                <tbody>"""

    # Add model results to table
    for model_name, results in model_results.items():
        test_auc = results.get('test_auc', 0)
        test_auc_str = f"{test_auc:.3f}" if test_auc is not None else "N/A"
        html_content += f"""
                    <tr>
                        <td>{model_name.replace('_', ' ').title()}</td>
                        <td>{results.get('train_accuracy', 0):.3f}</td>
                        <td>{results.get('test_accuracy', 0):.3f}</td>
                        <td>{test_auc_str}</td>
                    </tr>"""

    html_content += f"""
                </tbody>
            </table>

            <!-- Last 3 Trades -->
            <h2 class="section-title">Recent Trading Activity</h2>"""

    # Add last 3 trades
    try:
        best_trades = pd.read_csv(config.get_output_file('best_trades'))
        best_trades['entry_date'] = pd.to_datetime(best_trades['entry_date'])
        best_trades['exit_date'] = pd.to_datetime(best_trades['exit_date'])
        last_3_trades = best_trades.sort_values('entry_date').tail(3)

        html_content += f"""
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #2E86AB; margin-top: 0;">Last 3 Trades from Backtesting</h3>
                <div style="display: grid; gap: 15px;">"""

        for i, (_, trade) in enumerate(last_3_trades.iterrows(), 1):
            profit_color = "#28a745" if trade['pnl_pct'] > 0 else "#dc3545"
            profit_icon = "✅" if trade['pnl_pct'] > 0 else "❌"
            trade_num = len(best_trades) - 3 + i

            html_content += f"""
                    <div style="background: white; padding: 15px; border-radius: 6px; border-left: 4px solid {profit_color};">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <strong style="color: #2E86AB;">Trade #{trade_num}</strong>
                            <span style="color: {profit_color}; font-weight: bold;">{profit_icon} {trade['pnl_pct']:+.2f}%</span>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; font-size: 0.9em;">
                            <div><strong>Direction:</strong> <span style="color: {'#28a745' if trade['direction'].upper() == 'LONG' else '#dc3545'}; font-weight: bold;">{trade['direction'].upper()}</span></div>
                            <div><strong>Entry:</strong> {trade['entry_date'].strftime('%Y-%m-%d')}</div>
                            <div><strong>Exit:</strong> {trade['exit_date'].strftime('%Y-%m-%d')}</div>
                            <div><strong>Entry Price:</strong> ${trade['entry_price']:.2f}</div>
                            <div><strong>Exit Price:</strong> ${trade['exit_price']:.2f}</div>
                            <div><strong>Days Held:</strong> {trade['days_held']}</div>
                            <div><strong>Confidence:</strong> {trade['confidence']:.1%}</div>
                        </div>
                    </div>"""

        # Summary of last 3 trades
        total_pnl = last_3_trades['pnl_pct'].sum()
        wins = (last_3_trades['pnl_pct'] > 0).sum()
        avg_pnl = last_3_trades['pnl_pct'].mean()
        long_trades = (last_3_trades['direction'].str.upper() == 'LONG').sum()
        short_trades = (last_3_trades['direction'].str.upper() == 'SHORT').sum()

        html_content += f"""
                </div>
                <div style="margin-top: 15px; padding: 10px; background: #e9ecef; border-radius: 4px;">
                    <strong>Last 3 Trades Summary:</strong>
                    Total P&L: <span style="color: {'#28a745' if total_pnl > 0 else '#dc3545'};">{total_pnl:+.2f}%</span> |
                    Win Rate: {wins}/3 ({wins/3:.1%}) |
                    Avg P&L: {avg_pnl:+.2f}% |
                    Direction: {long_trades} LONG, {short_trades} SHORT
                </div>
            </div>

            <!-- Data Summary -->
            <h2 class="section-title">Dataset Summary</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">{data_summary['total_records']:,}</div>
                    <div class="metric-label">Total Records</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{data_summary['total_features']}</div>
                    <div class="metric-label">Features</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">5.4</div>
                    <div class="metric-label">Years of Data</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">22</div>
                    <div class="metric-label">Source Files</div>
                </div>
            </div>"""
    except Exception as e:
        html_content += f"""
            <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <strong>Recent Trades:</strong> Unable to load recent trades data ({str(e)})
            </div>

            <!-- Data Summary -->
            <h2 class="section-title">Dataset Summary</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">{data_summary['total_records']:,}</div>
                    <div class="metric-label">Total Records</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{data_summary['total_features']}</div>
                    <div class="metric-label">Features</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">5.4</div>
                    <div class="metric-label">Years of Data</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">22</div>
                    <div class="metric-label">Source Files</div>
                </div>
            </div>

            <div class="footer">
                <p>SPX Predictive System | Generated by Manus AI | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>Data Range: {data_summary['date_range_start']} to {data_summary['date_range_end']}</p>
            </div>
        </div>
    </body>
    </html>
    """

    return html_content

def main():
    """Main function to create HTML report"""
    html_content = create_html_report()
    
    # Save HTML file
    output_file = config.get_output_file('html_report').replace('.png', '.html')
    with open(output_file, 'w') as f:
        f.write(html_content)
    
    print(f"✅ HTML report created: {output_file}")
    return output_file

if __name__ == "__main__":
    main()
