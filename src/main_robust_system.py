"""
Main Robust SPX System - Complete Pipeline
Runs entire system and produces comprehensive report with current signal and last 5 trades
"""

import pandas as pd
import numpy as np
import pickle
import json
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import accuracy_score
from config import config
from enhanced_data_loader import EnhancedDataLoader

class RobustSPXSystem:
    def __init__(self):
        self.data = None
        self.model = None
        self.scaler = None
        self.features = None
        self.trades = []
        self.current_prediction = None
        self.system_results = {}
        
    def run_complete_system(self):
        """Run the complete robust SPX system pipeline"""
        print("🚀 ROBUST SPX SYSTEM - COMPLETE PIPELINE")
        print("=" * 60)
        
        # Step 1: Load and process data
        self.load_and_process_data()
        
        # Step 2: Create robust baseline model
        self.create_robust_model()
        
        # Step 3: Run trading simulation
        self.run_trading_simulation()
        
        # Step 4: Generate current prediction
        self.generate_current_prediction()
        
        # Step 5: Create comprehensive report
        self.create_comprehensive_report()
        
        print("\n✅ COMPLETE ROBUST SPX SYSTEM EXECUTED SUCCESSFULLY!")
        return True
    
    def load_and_process_data(self):
        """Load and process SPX + VIX options data"""
        print("\n1. Loading and processing data...")
        
        # Load enhanced data
        loader = EnhancedDataLoader()
        self.data = loader.load_enhanced_data()
        
        # Create baseline features
        self.data['spx_return_1d'] = self.data['spx_close'].pct_change(1)
        self.data['spx_return_5d'] = self.data['spx_close'].pct_change(5)
        self.data['spx_sma_5'] = self.data['spx_close'].rolling(5).mean()
        self.data['spx_sma_20'] = self.data['spx_close'].rolling(20).mean()
        self.data['spx_above_sma5'] = (self.data['spx_close'] > self.data['spx_sma_5']).astype(int)
        self.data['spx_above_sma20'] = (self.data['spx_close'] > self.data['spx_sma_20']).astype(int)
        self.data['vix_sma_5'] = self.data['vix_close'].rolling(5).mean()
        self.data['vix_sma_20'] = self.data['vix_close'].rolling(20).mean()
        self.data['vix_above_20'] = (self.data['vix_close'] > 20).astype(int)
        self.data['vix_above_30'] = (self.data['vix_close'] > 30).astype(int)
        self.data['volume_sma_5'] = self.data['total_volume'].rolling(5).mean()
        self.data['volume_sma_20'] = self.data['total_volume'].rolling(20).mean()
        self.data['volume_above_avg'] = (self.data['total_volume'] > self.data['volume_sma_20']).astype(int)
        self.data['gamma_normalized'] = self.data['total_gamma'] / self.data['total_gamma'].rolling(20).mean()
        self.data['put_call_ratio'] = self.data['put_notional'] / (self.data['call_notional'] + 1e-6)
        # Create next day return and target
        self.data['next_day_return'] = self.data['spx_close'].pct_change(1).shift(-1)
        self.data['target'] = (self.data['next_day_return'] > 0).astype(int)
        
        # Define robust features
        self.features = [
            'spx_return_1d', 'spx_return_5d', 'spx_above_sma5', 'spx_above_sma20',
            'vix_close', 'vix_return', 'vix_above_20', 'vix_above_30',
            'total_volume', 'volume_above_avg',
            'total_gamma', 'gamma_normalized', 'put_call_ratio',
            'total_open_interest', 'unique_strikes'
        ]
        
        print(f"   ✅ Loaded {len(self.data)} observations with {len(self.features)} features")
    
    def create_robust_model(self):
        """Create and train the robust baseline model"""
        print("\n2. Creating robust baseline model...")
        
        # Clean data
        clean_data = self.data.dropna(subset=['target'] + self.features).copy()
        X = clean_data[self.features].copy()
        y = clean_data['target'].copy()
        
        # Handle missing values
        for col in X.columns:
            if X[col].dtype in ['float64', 'int64']:
                X[col] = X[col].fillna(X[col].median())
        
        X = X.replace([np.inf, -np.inf], np.nan)
        for col in X.columns:
            if X[col].dtype in ['float64', 'int64']:
                X[col] = X[col].fillna(X[col].median())
        
        # Time series cross-validation
        tscv = TimeSeriesSplit(n_splits=5)
        cv_scores = []
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
            X_train_fold = X.iloc[train_idx]
            X_val_fold = X.iloc[val_idx]
            y_train_fold = y.iloc[train_idx]
            y_val_fold = y.iloc[val_idx]
            
            scaler_fold = StandardScaler()
            X_train_scaled = scaler_fold.fit_transform(X_train_fold)
            X_val_scaled = scaler_fold.transform(X_val_fold)
            
            model_fold = RandomForestClassifier(
                n_estimators=50, max_depth=6, min_samples_split=100,
                min_samples_leaf=50, random_state=42, class_weight='balanced',
                max_features='sqrt'
            )
            
            model_fold.fit(X_train_scaled, y_train_fold)
            val_pred = model_fold.predict(X_val_scaled)
            val_acc = accuracy_score(y_val_fold, val_pred)
            cv_scores.append(val_acc)
        
        avg_cv_score = np.mean(cv_scores)
        
        # Train final model on all data
        self.scaler = StandardScaler()
        X_scaled = self.scaler.fit_transform(X)
        
        self.model = RandomForestClassifier(
            n_estimators=50, max_depth=6, min_samples_split=100,
            min_samples_leaf=50, random_state=42, class_weight='balanced',
            max_features='sqrt'
        )
        
        self.model.fit(X_scaled, y)
        
        # Test on full dataset
        full_pred = self.model.predict(X_scaled)
        full_acc = accuracy_score(y, full_pred)
        
        # Store clean data for later use
        self.clean_data = clean_data.copy()
        self.clean_data['prediction'] = full_pred
        self.clean_data['probability'] = self.model.predict_proba(X_scaled)[:, 1]
        
        print(f"   ✅ Model trained - CV: {avg_cv_score:.1%}, Full: {full_acc:.1%}")
        
        # Save model components
        with open('best_model.pkl', 'wb') as f:
            pickle.dump(self.model, f)
        with open('scaler.pkl', 'wb') as f:
            pickle.dump(self.scaler, f)
        with open('selected_features.json', 'w') as f:
            json.dump(self.features, f)
    
    def run_trading_simulation(self):
        """Run trading simulation and generate trades"""
        print("\n3. Running trading simulation...")
        
        # Use last 600 days for trading
        trading_data = self.clean_data.tail(600).copy()
        
        # Strategy: Trade when probability > 0.52 or < 0.48 (more sensitive)
        confident_long = trading_data[trading_data['probability'] > 0.52].copy()
        confident_short = trading_data[trading_data['probability'] < 0.48].copy()
        
        trades = []
        
        # Long trades
        for _, row in confident_long.iterrows():
            trade_return = row['next_day_return'] * 100
            trades.append({
                'date': row['date'],
                'direction': 'long',
                'entry_price': row['spx_close'],
                'confidence': row['probability'],
                'return_pct': trade_return,
                'win': trade_return > 0
            })
        
        # Short trades
        for _, row in confident_short.iterrows():
            trade_return = -row['next_day_return'] * 100
            trades.append({
                'date': row['date'],
                'direction': 'short',
                'entry_price': row['spx_close'],
                'confidence': 1 - row['probability'],
                'return_pct': trade_return,
                'win': trade_return > 0
            })
        
        # Store trades
        self.trades_df = pd.DataFrame(trades).sort_values('date')
        self.trades = trades
        
        # Calculate performance metrics
        total_trades = len(self.trades_df)
        win_rate = self.trades_df['win'].mean()
        avg_return = self.trades_df['return_pct'].mean()
        total_return = self.trades_df['return_pct'].sum()
        
        # Monthly analysis
        self.trades_df['month'] = pd.to_datetime(self.trades_df['date']).dt.to_period('M')
        monthly_returns = self.trades_df.groupby('month')['return_pct'].sum()
        positive_months = (monthly_returns > 0).sum()
        total_months = len(monthly_returns)
        monthly_win_rate = positive_months / total_months if total_months > 0 else 0
        
        # Store results
        self.system_results = {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_return_per_trade': avg_return,
            'total_return': total_return,
            'monthly_win_rate': monthly_win_rate,
            'best_month': monthly_returns.max(),
            'worst_month': monthly_returns.min(),
            'prediction_accuracy': (self.clean_data['prediction'] == self.clean_data['target']).mean()
        }
        
        print(f"   ✅ Simulation complete - {total_trades} trades, {win_rate:.1%} win rate, {total_return:.1f}% return")
    
    def generate_current_prediction(self):
        """Generate current market prediction"""
        print("\n4. Generating current prediction...")
        
        current_data = self.clean_data.iloc[-1]
        
        self.current_prediction = {
            'date': current_data['date'],
            'spx_close': current_data['spx_close'],
            'vix_close': current_data['vix_close'],
            'prediction': 'UP' if current_data['prediction'] == 1 else 'DOWN',
            'confidence': current_data['probability'] if current_data['prediction'] == 1 else (1 - current_data['probability']),
            'trade_signal': 'LONG' if current_data['probability'] > 0.52 else 'SHORT' if current_data['probability'] < 0.48 else 'NO TRADE'
        }
        
        print(f"   ✅ Current prediction: {self.current_prediction['prediction']} ({self.current_prediction['confidence']:.1%})")
    
    def create_comprehensive_report(self):
        """Create comprehensive HTML report with all results"""
        print("\n5. Creating comprehensive report...")
        
        # Create equity curve
        self.trades_df['cumulative_return'] = self.trades_df['return_pct'].cumsum()
        
        plt.figure(figsize=(12, 6))
        plt.plot(pd.to_datetime(self.trades_df['date']), self.trades_df['cumulative_return'], 
                linewidth=2, color='#2E8B57')
        plt.title('Robust SPX System - Equity Curve', fontsize=16, fontweight='bold')
        plt.xlabel('Date')
        plt.ylabel('Cumulative Return (%)')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('equity_curve.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # Get last 5 trades
        last_5_trades = self.trades_df.tail(5)
        
        # Create HTML report
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Robust SPX System - Complete Report</title>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; margin: 0; padding: 20px; background-color: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
                .header {{ text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; }}
                .current-signal {{ background: #e8f5e8; border: 2px solid #28a745; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center; }}
                .signal-value {{ font-size: 2em; font-weight: bold; color: #28a745; }}
                .metrics-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }}
                .metric-card {{ background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }}
                .metric-value {{ font-size: 1.5em; font-weight: bold; color: #007bff; }}
                .trades-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                .trades-table th, .trades-table td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
                .trades-table th {{ background-color: #f8f9fa; font-weight: bold; }}
                .win {{ color: #28a745; font-weight: bold; }}
                .loss {{ color: #dc3545; font-weight: bold; }}
                .chart-container {{ text-align: center; margin: 30px 0; }}
                .chart-container img {{ max-width: 100%; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚀 Robust SPX Trading System</h1>
                    <h2>Complete Performance Report</h2>
                    <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                
                <div class="current-signal">
                    <h3>🚨 CURRENT MARKET SIGNAL</h3>
                    <div class="signal-value">{self.current_prediction['trade_signal']}</div>
                    <p><strong>Date:</strong> {self.current_prediction['date'].strftime('%Y-%m-%d')}</p>
                    <p><strong>SPX:</strong> ${self.current_prediction['spx_close']:,.2f} | <strong>VIX:</strong> {self.current_prediction['vix_close']:.2f}</p>
                    <p><strong>Prediction:</strong> {self.current_prediction['prediction']} | <strong>Confidence:</strong> {self.current_prediction['confidence']:.1%}</p>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">{self.system_results['win_rate']:.1%}</div>
                        <div>Win Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{self.system_results['total_return']:.1f}%</div>
                        <div>Total Return</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{self.system_results['total_trades']}</div>
                        <div>Total Trades</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{self.system_results['monthly_win_rate']:.1%}</div>
                        <div>Monthly Win Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{self.system_results['avg_return_per_trade']:.3f}%</div>
                        <div>Avg Return/Trade</div>
                    </div>
                </div>
                
                <h3>📊 Last 5 Trades</h3>
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Direction</th>
                            <th>Entry Price</th>
                            <th>Return</th>
                            <th>Result</th>
                            <th>Confidence</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        
        for _, trade in last_5_trades.iterrows():
            result_class = "win" if trade['win'] else "loss"
            result_text = "WIN ✅" if trade['win'] else "LOSS ❌"
            direction_emoji = "📈" if trade['direction'] == 'long' else "📉"
            
            html_content += f"""
                        <tr>
                            <td>{trade['date'].strftime('%Y-%m-%d')}</td>
                            <td>{direction_emoji} {trade['direction'].upper()}</td>
                            <td>${trade['entry_price']:,.2f}</td>
                            <td class="{result_class}">{trade['return_pct']:+.3f}%</td>
                            <td class="{result_class}">{result_text}</td>
                            <td>{trade['confidence']:.1%}</td>
                        </tr>
            """
        
        html_content += f"""
                    </tbody>
                </table>
                
                <div class="chart-container">
                    <h3>📈 Equity Curve</h3>
                    <img src="equity_curve.png" alt="Equity Curve">
                </div>
                
                <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;">
                    <h3>✅ System Status: OPERATIONAL</h3>
                    <p><strong>Last 5 Trades:</strong> {last_5_trades['win'].sum()}/5 wins ({last_5_trades['win'].mean():.1%})</p>
                    <p><strong>Last 5 Return:</strong> {last_5_trades['return_pct'].sum():+.2f}%</p>
                    <p><strong>Model Health:</strong> {self.system_results['prediction_accuracy']:.1%} accuracy</p>
                </div>
                
                <div style="text-align: center; margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <p><strong>Robust SPX Trading System</strong> - Powered by 15 carefully selected features</p>
                    <p>Conservative, profitable approach with {self.system_results['win_rate']:.1%} win rate</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Save HTML report
        with open('robust_system_complete_report.html', 'w') as f:
            f.write(html_content)
        
        # Save data files
        self.trades_df.to_csv('system_trades.csv', index=False)
        
        with open('system_results.json', 'w') as f:
            json.dump({
                **self.system_results,
                'current_prediction': {
                    'date': self.current_prediction['date'].isoformat(),
                    'spx_close': float(self.current_prediction['spx_close']),
                    'vix_close': float(self.current_prediction['vix_close']),
                    'prediction': self.current_prediction['prediction'],
                    'confidence': float(self.current_prediction['confidence']),
                    'trade_signal': self.current_prediction['trade_signal']
                }
            }, f, indent=2)
        
        print(f"   ✅ Report created: robust_system_complete_report.html")
        print(f"   ✅ Data saved: system_trades.csv, system_results.json")

def main():
    """Main function to run the complete robust SPX system"""
    system = RobustSPXSystem()
    success = system.run_complete_system()
    
    if success:
        print(f"\n🎉 SYSTEM EXECUTION COMPLETE!")
        print(f"📊 Performance: {system.system_results['win_rate']:.1%} win rate, {system.system_results['total_return']:.1f}% return")
        print(f"🚨 Current Signal: {system.current_prediction['trade_signal']}")
        print(f"📈 Report: robust_system_complete_report.html")
        
        return system
    else:
        print(f"\n❌ System execution failed")
        return None

if __name__ == "__main__":
    system = main()
