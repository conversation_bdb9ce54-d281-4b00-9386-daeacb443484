"""
Market Regime Detection using Hidden Markov Models and Advanced Techniques
"""

import numpy as np
import pandas as pd
from sklearn.mixture import GaussianMixture
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

class MarketRegimeDetector:
    def __init__(self, n_regimes=3):
        """
        Initialize Market Regime Detector
        
        Args:
            n_regimes: Number of market regimes to detect (default: 3 for Bull/Bear/Sideways)
        """
        self.n_regimes = n_regimes
        self.hmm_model = None
        self.scaler = StandardScaler()
        self.regime_features = None
        self.regime_labels = None
        
    def create_regime_features(self, data):
        """Create features for regime detection"""
        features = pd.DataFrame(index=data.index)
        
        # Price-based features
        features['returns'] = data['spx_close'].pct_change()
        features['volatility'] = features['returns'].rolling(20).std()
        features['momentum'] = data['spx_close'].pct_change(20)
        
        # VIX-based features
        if 'vix_close' in data.columns:
            features['vix_level'] = data['vix_close']
            features['vix_change'] = data['vix_close'].pct_change()
            features['vix_momentum'] = data['vix_close'].pct_change(10)
            features['vix_term_structure'] = data['vix_close'] / data['vix_close'].rolling(5).mean()
        
        # Volume-based features
        if 'total_volume' in data.columns:
            features['volume_trend'] = data['total_volume'].pct_change(5)
            features['volume_volatility'] = data['total_volume'].rolling(10).std()
        
        # Options-based features
        if 'net_gamma_exposure' in data.columns:
            features['gamma_regime'] = data['net_gamma_exposure']
            features['gamma_change'] = data['net_gamma_exposure'].pct_change(3)
        
        # Market microstructure
        features['price_acceleration'] = features['returns'].diff()
        features['volatility_trend'] = features['volatility'].pct_change(5)
        
        # Cross-asset relationships
        if 'vix_close' in data.columns:
            features['vix_spx_correlation'] = data['vix_close'].rolling(20).corr(data['spx_close'])
            features['stress_indicator'] = (data['vix_close'] > data['vix_close'].rolling(60).quantile(0.8)).astype(int)
        
        return features.dropna()
    
    def detect_regimes_gmm(self, data):
        """Detect market regimes using Gaussian Mixture Models"""
        print("Detecting market regimes using Gaussian Mixture Models...")
        
        # Create regime features
        regime_features = self.create_regime_features(data)
        
        # Scale features
        features_scaled = self.scaler.fit_transform(regime_features)
        
        # Fit Gaussian Mixture Model
        gmm = GaussianMixture(
            n_components=self.n_regimes,
            covariance_type='full',
            random_state=42,
            max_iter=200
        )
        
        regime_labels = gmm.fit_predict(features_scaled)
        regime_probabilities = gmm.predict_proba(features_scaled)
        
        # Create regime DataFrame
        regime_df = pd.DataFrame({
            'regime': regime_labels,
            'regime_prob_0': regime_probabilities[:, 0],
            'regime_prob_1': regime_probabilities[:, 1],
            'regime_prob_2': regime_probabilities[:, 2] if self.n_regimes > 2 else 0
        }, index=regime_features.index)
        
        # Analyze regime characteristics
        self._analyze_regime_characteristics(regime_features, regime_labels)
        
        self.regime_features = regime_features
        self.regime_labels = regime_labels
        self.hmm_model = gmm
        
        return regime_df
    
    def _analyze_regime_characteristics(self, features, labels):
        """Analyze characteristics of each detected regime"""
        print("\nRegime Characteristics:")
        print("=" * 50)
        
        for regime in range(self.n_regimes):
            regime_data = features[labels == regime]
            
            if len(regime_data) > 0:
                print(f"\nRegime {regime} ({len(regime_data)} observations):")
                print(f"  Avg Return: {regime_data['returns'].mean():.4f}")
                print(f"  Avg Volatility: {regime_data['volatility'].mean():.4f}")
                print(f"  Avg Momentum: {regime_data['momentum'].mean():.4f}")
                
                if 'vix_level' in regime_data.columns:
                    print(f"  Avg VIX Level: {regime_data['vix_level'].mean():.2f}")
                    print(f"  Avg VIX Change: {regime_data['vix_change'].mean():.4f}")
                
                # Classify regime type
                avg_return = regime_data['returns'].mean()
                avg_volatility = regime_data['volatility'].mean()
                
                if avg_return > 0.001 and avg_volatility < 0.02:
                    regime_type = "Bull Market"
                elif avg_return < -0.001 and avg_volatility > 0.025:
                    regime_type = "Bear Market"
                else:
                    regime_type = "Sideways/Transition"
                
                print(f"  Classification: {regime_type}")
    
    def add_regime_features_to_data(self, data, regime_df):
        """Add regime-based features to the main dataset"""
        print("Adding regime-based features to dataset...")
        
        # Merge regime information
        data_with_regimes = data.merge(regime_df, left_index=True, right_index=True, how='left')
        
        # Create regime-based features
        data_with_regimes['regime_change'] = data_with_regimes['regime'].diff()
        data_with_regimes['regime_stability'] = (data_with_regimes['regime'] == data_with_regimes['regime'].shift(1)).astype(int)
        data_with_regimes['regime_duration'] = data_with_regimes.groupby('regime').cumcount() + 1
        
        # Regime transition indicators
        data_with_regimes['entering_bull'] = ((data_with_regimes['regime'] != data_with_regimes['regime'].shift(1)) & 
                                            (data_with_regimes['regime'] == 0)).astype(int)  # Assuming regime 0 is bull
        data_with_regimes['entering_bear'] = ((data_with_regimes['regime'] != data_with_regimes['regime'].shift(1)) & 
                                            (data_with_regimes['regime'] == 1)).astype(int)  # Assuming regime 1 is bear
        
        # Regime-specific momentum
        for regime in range(self.n_regimes):
            regime_mask = data_with_regimes['regime'] == regime
            data_with_regimes[f'regime_{regime}_momentum'] = np.where(
                regime_mask, 
                data_with_regimes['spx_close'].pct_change(5), 
                0
            )
        
        # Regime confidence features
        data_with_regimes['regime_confidence'] = data_with_regimes[['regime_prob_0', 'regime_prob_1', 'regime_prob_2']].max(axis=1)
        data_with_regimes['regime_uncertainty'] = 1 - data_with_regimes['regime_confidence']
        
        print(f"Added {len([col for col in data_with_regimes.columns if 'regime' in col])} regime-based features")
        
        return data_with_regimes
    
    def create_regime_aware_strategy_signals(self, data_with_regimes):
        """Create regime-aware trading signals"""
        print("Creating regime-aware strategy signals...")
        
        signals = pd.DataFrame(index=data_with_regimes.index)
        
        # Regime-specific confidence adjustments
        signals['regime_confidence_multiplier'] = 1.0
        
        # Bull market: Increase long confidence, decrease short confidence
        bull_mask = data_with_regimes['regime'] == 0  # Assuming regime 0 is bull
        signals.loc[bull_mask, 'regime_confidence_multiplier'] = 1.2
        
        # Bear market: Increase short confidence, decrease long confidence  
        bear_mask = data_with_regimes['regime'] == 1  # Assuming regime 1 is bear
        signals.loc[bear_mask, 'regime_confidence_multiplier'] = 1.1
        
        # Sideways market: Reduce overall confidence
        sideways_mask = data_with_regimes['regime'] == 2  # Assuming regime 2 is sideways
        signals.loc[sideways_mask, 'regime_confidence_multiplier'] = 0.8
        
        # Regime transition periods: Reduce confidence
        transition_mask = data_with_regimes['regime_change'] != 0
        signals.loc[transition_mask, 'regime_confidence_multiplier'] *= 0.7
        
        # High uncertainty periods: Reduce confidence
        high_uncertainty_mask = data_with_regimes['regime_uncertainty'] > 0.4
        signals.loc[high_uncertainty_mask, 'regime_confidence_multiplier'] *= 0.6
        
        # Regime-specific position sizing adjustments
        signals['regime_position_multiplier'] = 1.0
        
        # Bull market: Slightly larger positions
        signals.loc[bull_mask, 'regime_position_multiplier'] = 1.1
        
        # Bear market: Smaller positions due to higher volatility
        signals.loc[bear_mask, 'regime_position_multiplier'] = 0.9
        
        # High volatility regimes: Reduce position size
        if 'volatility' in data_with_regimes.columns:
            high_vol_mask = data_with_regimes['volatility'] > data_with_regimes['volatility'].rolling(60).quantile(0.8)
            signals.loc[high_vol_mask, 'regime_position_multiplier'] *= 0.8
        
        return signals

def integrate_regime_detection(data):
    """Main function to integrate regime detection into the trading system"""
    print("\n🔍 INTEGRATING MARKET REGIME DETECTION")
    print("=" * 60)
    
    # Initialize regime detector
    detector = MarketRegimeDetector(n_regimes=3)
    
    # Detect regimes
    regime_df = detector.detect_regimes_gmm(data)
    
    # Add regime features to data
    enhanced_data = detector.add_regime_features_to_data(data, regime_df)
    
    # Create regime-aware signals
    regime_signals = detector.create_regime_aware_strategy_signals(enhanced_data)
    
    # Merge signals with data
    final_data = enhanced_data.merge(regime_signals, left_index=True, right_index=True, how='left')
    
    print(f"\n✅ Regime detection completed!")
    print(f"   📊 Detected {detector.n_regimes} market regimes")
    print(f"   🎯 Added {len([col for col in final_data.columns if 'regime' in col])} regime features")
    print(f"   📈 Enhanced dataset shape: {final_data.shape}")
    
    return final_data, detector

if __name__ == "__main__":
    # Test regime detection
    import sys
    sys.path.append('.')
    from config import config
    
    # Load data
    data = pd.read_csv(config.get_data_file('engineered_data'))
    data['date'] = pd.to_datetime(data['date'])
    data.set_index('date', inplace=True)
    
    # Run regime detection
    enhanced_data, detector = integrate_regime_detection(data)
    
    # Save enhanced data
    enhanced_data.to_csv('data_with_regimes.csv')
    print("\n✅ Enhanced data with regimes saved to 'data_with_regimes.csv'")
