"""
SPX Predictive System - Pipeline Logger
======================================

Logging utilities for the pipeline execution with progress tracking.

Author: Manus AI
Date: June 23, 2025
"""

import logging
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Optional

class PipelineLogger:
    """Enhanced logger for pipeline execution with progress tracking"""
    
    def __init__(self, log_level: str = 'INFO', log_file: Optional[str] = None):
        """
        Initialize pipeline logger
        
        Args:
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
            log_file: Optional log file path
        """
        self.log_level = getattr(logging, log_level.upper())
        self.start_time = time.time()
        self.stage_start_time = None
        self.current_stage = None
        self.total_stages = 0
        self.completed_stages = 0
        
        # Setup logger
        self.logger = logging.getLogger('SPXPipeline')
        self.logger.setLevel(self.log_level)
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Console handler with custom formatting
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        
        # Custom formatter
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # File handler if specified
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.DEBUG)  # Always debug level for file
            file_formatter = logging.Formatter(
                '%(asctime)s | %(levelname)-8s | %(funcName)s:%(lineno)d | %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
    
    def set_total_stages(self, total: int):
        """Set total number of pipeline stages"""
        self.total_stages = total
        self.completed_stages = 0
    
    def start_stage(self, stage_name: str, stage_num: int):
        """Start a new pipeline stage"""
        self.current_stage = stage_name
        self.stage_start_time = time.time()
        
        # Calculate progress
        progress = (stage_num - 1) / self.total_stages * 100 if self.total_stages > 0 else 0
        
        self.info("=" * 80)
        self.info(f"🚀 STAGE {stage_num}/{self.total_stages}: {stage_name.upper()}")
        self.info(f"📊 Progress: {progress:.1f}%")
        self.info(f"⏰ Started: {datetime.now().strftime('%H:%M:%S')}")
        self.info("=" * 80)
    
    def complete_stage(self, success: bool = True):
        """Complete current pipeline stage"""
        if self.stage_start_time is None:
            return
        
        stage_duration = time.time() - self.stage_start_time
        status = "✅ COMPLETED" if success else "❌ FAILED"
        
        self.completed_stages += 1
        progress = self.completed_stages / self.total_stages * 100 if self.total_stages > 0 else 0
        
        self.info(f"{status}: {self.current_stage}")
        self.info(f"⏱️  Stage Duration: {stage_duration:.1f}s")
        self.info(f"📊 Overall Progress: {progress:.1f}%")
        
        if not success:
            self.error(f"Stage '{self.current_stage}' failed!")
        
        self.stage_start_time = None
        self.current_stage = None
    
    def progress_bar(self, current: int, total: int, description: str = ""):
        """Display a simple progress bar"""
        if total == 0:
            return
        
        percent = current / total * 100
        filled = int(percent / 2)  # 50 chars max
        bar = "█" * filled + "░" * (50 - filled)
        
        self.info(f"📈 {description} [{bar}] {percent:.1f}% ({current}/{total})")
    
    def file_status(self, file_path: str, description: str):
        """Log file existence status"""
        exists = Path(file_path).exists()
        status = "✅" if exists else "❌"
        self.info(f"  {status} {description}: {file_path}")
        return exists
    
    def execution_summary(self):
        """Print execution summary"""
        total_duration = time.time() - self.start_time
        
        self.info("=" * 80)
        self.info("📋 EXECUTION SUMMARY")
        self.info("=" * 80)
        self.info(f"⏱️  Total Duration: {total_duration:.1f} seconds")
        self.info(f"📊 Completed Stages: {self.completed_stages}/{self.total_stages}")
        self.info(f"🏁 Finished: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.info("=" * 80)
    
    def debug(self, message: str):
        """Log debug message"""
        self.logger.debug(message)
    
    def info(self, message: str):
        """Log info message"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """Log warning message"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """Log error message"""
        self.logger.error(message)
    
    def critical(self, message: str):
        """Log critical message"""
        self.logger.critical(message)

class ProgressTracker:
    """Simple progress tracker for long-running operations"""
    
    def __init__(self, total_items: int, description: str = "", logger: Optional[PipelineLogger] = None):
        """
        Initialize progress tracker
        
        Args:
            total_items: Total number of items to process
            description: Description of the operation
            logger: Optional logger instance
        """
        self.total_items = total_items
        self.description = description
        self.logger = logger
        self.current_item = 0
        self.start_time = time.time()
        self.last_update = 0
    
    def update(self, increment: int = 1):
        """Update progress"""
        self.current_item += increment
        
        # Only update every 10% or every 10 seconds
        percent = self.current_item / self.total_items * 100
        current_time = time.time()
        
        if (percent - self.last_update >= 10) or (current_time - self.start_time >= 10):
            if self.logger:
                self.logger.progress_bar(self.current_item, self.total_items, self.description)
            else:
                print(f"Progress: {percent:.1f}% ({self.current_item}/{self.total_items})")
            
            self.last_update = percent
    
    def complete(self):
        """Mark as complete"""
        duration = time.time() - self.start_time
        if self.logger:
            self.logger.info(f"✅ {self.description} completed in {duration:.1f}s")
        else:
            print(f"✅ {self.description} completed in {duration:.1f}s")

# Convenience function to create logger
def create_pipeline_logger(log_level: str = 'INFO', log_file: Optional[str] = None) -> PipelineLogger:
    """Create a pipeline logger instance"""
    return PipelineLogger(log_level, log_file)
