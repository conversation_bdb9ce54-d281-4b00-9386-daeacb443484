"""
Gradual Enhancement System
Add proven enhancements one at a time while monitoring accuracy
"""

import pandas as pd
import numpy as np
import pickle
import json
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import accuracy_score, roc_auc_score
from config import config

class GradualEnhancer:
    def __init__(self):
        self.baseline_features = None
        self.baseline_accuracy = None
        self.enhancement_results = []
        
    def load_baseline(self):
        """Load the working baseline system"""
        print("📊 Loading baseline system...")
        
        # Load baseline features
        with open(config.get_data_file('selected_features'), 'r') as f:
            self.baseline_features = json.load(f)
        
        print(f"   ✅ Baseline: {len(self.baseline_features)} features")
        return True
    
    def prepare_data(self, additional_features=None):
        """Prepare data with baseline + additional features"""
        # Load processed data
        data = pd.read_csv('spx_processed_data.csv')
        data['date'] = pd.to_datetime(data['date'])
        
        # Recreate baseline features
        data['spx_return_1d'] = data['spx_close'].pct_change(1)
        data['spx_return_5d'] = data['spx_close'].pct_change(5)
        data['spx_sma_5'] = data['spx_close'].rolling(5).mean()
        data['spx_sma_20'] = data['spx_close'].rolling(20).mean()
        data['spx_above_sma5'] = (data['spx_close'] > data['spx_sma_5']).astype(int)
        data['spx_above_sma20'] = (data['spx_close'] > data['spx_sma_20']).astype(int)
        data['vix_sma_5'] = data['vix_close'].rolling(5).mean()
        data['vix_sma_20'] = data['vix_close'].rolling(20).mean()
        data['vix_above_20'] = (data['vix_close'] > 20).astype(int)
        data['vix_above_30'] = (data['vix_close'] > 30).astype(int)
        data['volume_sma_5'] = data['total_volume'].rolling(5).mean()
        data['volume_sma_20'] = data['total_volume'].rolling(20).mean()
        data['volume_above_avg'] = (data['total_volume'] > data['volume_sma_20']).astype(int)
        data['gamma_normalized'] = data['total_gamma'] / data['total_gamma'].rolling(20).mean()
        data['put_call_ratio'] = data['put_notional'] / (data['call_notional'] + 1e-6)
        data['target'] = (data['next_day_return'] > 0).astype(int)
        
        # Add additional features if specified
        if additional_features:
            for feature_name, feature_func in additional_features.items():
                try:
                    data[feature_name] = feature_func(data)
                    print(f"   ✅ Added feature: {feature_name}")
                except Exception as e:
                    print(f"   ❌ Failed to add {feature_name}: {str(e)}")
        
        return data
    
    def test_enhancement(self, enhancement_name, additional_features, description):
        """Test a specific enhancement"""
        print(f"\n🧪 TESTING ENHANCEMENT: {enhancement_name}")
        print(f"   📝 {description}")
        print("=" * 60)
        
        # Prepare data with enhancement
        data = self.prepare_data(additional_features)
        
        # Feature list
        feature_list = self.baseline_features.copy()
        if additional_features:
            feature_list.extend(list(additional_features.keys()))
        
        # Filter to available features
        available_features = [f for f in feature_list if f in data.columns]
        
        # Clean data
        clean_data = data.dropna(subset=['target'] + available_features).copy()
        X = clean_data[available_features].copy()
        y = clean_data['target'].copy()
        
        # Handle missing values
        for col in X.columns:
            if X[col].dtype in ['float64', 'int64']:
                X[col] = X[col].fillna(X[col].median())
        
        X = X.replace([np.inf, -np.inf], np.nan)
        for col in X.columns:
            if X[col].dtype in ['float64', 'int64']:
                X[col] = X[col].fillna(X[col].median())
        
        print(f"   📊 Data shape: {X.shape}")
        print(f"   🎯 Features: {len(available_features)} ({len(available_features) - len(self.baseline_features)} new)")
        
        # Time series cross-validation
        tscv = TimeSeriesSplit(n_splits=3)
        cv_scores = []
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
            X_train_fold = X.iloc[train_idx]
            X_val_fold = X.iloc[val_idx]
            y_train_fold = y.iloc[train_idx]
            y_val_fold = y.iloc[val_idx]
            
            # Scale features
            scaler_fold = StandardScaler()
            X_train_scaled = scaler_fold.fit_transform(X_train_fold)
            X_val_scaled = scaler_fold.transform(X_val_fold)
            
            # Train model
            model_fold = RandomForestClassifier(
                n_estimators=100,
                max_depth=8,
                min_samples_split=50,
                min_samples_leaf=20,
                random_state=42,
                class_weight='balanced'
            )
            
            model_fold.fit(X_train_scaled, y_train_fold)
            
            # Validate
            val_pred = model_fold.predict(X_val_scaled)
            val_acc = accuracy_score(y_val_fold, val_pred)
            cv_scores.append(val_acc)
        
        avg_cv_score = np.mean(cv_scores)
        cv_std = np.std(cv_scores)
        
        print(f"   📊 CV Accuracy: {avg_cv_score:.1%} ± {cv_std:.1%}")
        
        # Compare to baseline
        if self.baseline_accuracy is None:
            self.baseline_accuracy = 0.493  # From baseline test
        
        improvement = avg_cv_score - self.baseline_accuracy
        
        if improvement > 0.01:  # At least 1% improvement
            status = "✅ ACCEPT"
            print(f"   🎉 Improvement: +{improvement:.1%} - ACCEPTING ENHANCEMENT")
        elif improvement > -0.005:  # Less than 0.5% degradation
            status = "⚠️ NEUTRAL"
            print(f"   ⚖️ Change: {improvement:+.1%} - NEUTRAL (consider keeping)")
        else:
            status = "❌ REJECT"
            print(f"   ❌ Degradation: {improvement:+.1%} - REJECTING ENHANCEMENT")
        
        # Store results
        result = {
            'name': enhancement_name,
            'description': description,
            'cv_accuracy': avg_cv_score,
            'cv_std': cv_std,
            'improvement': improvement,
            'status': status,
            'features': available_features.copy(),
            'n_features': len(available_features)
        }
        
        self.enhancement_results.append(result)
        
        return status == "✅ ACCEPT", available_features, avg_cv_score

def run_gradual_enhancement():
    """Run gradual enhancement process"""
    print("🚀 GRADUAL ENHANCEMENT PROCESS")
    print("=" * 60)
    
    enhancer = GradualEnhancer()
    enhancer.load_baseline()
    
    # Track best system
    best_features = enhancer.baseline_features.copy()
    best_accuracy = 0.493  # Baseline accuracy
    
    # Enhancement 1: VIX Momentum Features
    print("\n" + "="*60)
    enhancement_1 = {
        'vix_momentum_3d': lambda data: data['vix_close'].pct_change(3),
        'vix_momentum_10d': lambda data: data['vix_close'].pct_change(10),
        'vix_acceleration': lambda data: data['vix_close'].pct_change(1).diff(),
    }
    
    accept_1, features_1, acc_1 = enhancer.test_enhancement(
        "VIX Momentum", 
        enhancement_1,
        "Add VIX momentum and acceleration features"
    )
    
    if accept_1:
        best_features = features_1
        best_accuracy = acc_1
        print("   ✅ VIX Momentum features ACCEPTED")
    
    # Enhancement 2: Volume Momentum
    print("\n" + "="*60)
    enhancement_2 = {
        'volume_momentum_3d': lambda data: data['total_volume'].pct_change(3),
        'volume_momentum_5d': lambda data: data['total_volume'].pct_change(5),
        'volume_acceleration': lambda data: data['total_volume'].pct_change(1).diff(),
    }
    
    # Use current best features as baseline
    enhancer.baseline_features = best_features
    enhancer.baseline_accuracy = best_accuracy
    
    accept_2, features_2, acc_2 = enhancer.test_enhancement(
        "Volume Momentum",
        enhancement_2,
        "Add volume momentum and acceleration features"
    )
    
    if accept_2:
        best_features = features_2
        best_accuracy = acc_2
        print("   ✅ Volume Momentum features ACCEPTED")
    
    # Enhancement 3: Options Flow Ratios
    print("\n" + "="*60)
    enhancement_3 = {
        'gamma_to_volume_ratio': lambda data: data['total_gamma'] / (data['total_volume'] + 1e-6),
        'oi_to_volume_ratio': lambda data: data['total_open_interest'] / (data['total_volume'] + 1e-6),
        'notional_concentration': lambda data: (data['call_notional'] + data['put_notional']) / (data['unique_strikes'] + 1),
    }
    
    enhancer.baseline_features = best_features
    enhancer.baseline_accuracy = best_accuracy
    
    accept_3, features_3, acc_3 = enhancer.test_enhancement(
        "Options Flow Ratios",
        enhancement_3,
        "Add options flow concentration and efficiency ratios"
    )
    
    if accept_3:
        best_features = features_3
        best_accuracy = acc_3
        print("   ✅ Options Flow Ratios ACCEPTED")
    
    # Enhancement 4: Price Action Features
    print("\n" + "="*60)
    enhancement_4 = {
        'spx_volatility_5d': lambda data: data['spx_close'].pct_change(1).rolling(5).std(),
        'spx_volatility_20d': lambda data: data['spx_close'].pct_change(1).rolling(20).std(),
        'price_momentum_ratio': lambda data: data['spx_return_5d'] / (data['spx_close'].pct_change(1).rolling(5).std() + 1e-6),
    }
    
    enhancer.baseline_features = best_features
    enhancer.baseline_accuracy = best_accuracy
    
    accept_4, features_4, acc_4 = enhancer.test_enhancement(
        "Price Action",
        enhancement_4,
        "Add price volatility and risk-adjusted momentum"
    )
    
    if accept_4:
        best_features = features_4
        best_accuracy = acc_4
        print("   ✅ Price Action features ACCEPTED")
    
    # Enhancement 5: VIX-SPX Relationship
    print("\n" + "="*60)
    enhancement_5 = {
        'vix_spx_correlation_5d': lambda data: data['vix_close'].rolling(5).corr(data['spx_close']),
        'vix_spx_correlation_20d': lambda data: data['vix_close'].rolling(20).corr(data['spx_close']),
        'vix_spx_divergence': lambda data: (data['vix_close'].pct_change(1) + data['spx_close'].pct_change(1)),
    }
    
    enhancer.baseline_features = best_features
    enhancer.baseline_accuracy = best_accuracy
    
    accept_5, features_5, acc_5 = enhancer.test_enhancement(
        "VIX-SPX Relationship",
        enhancement_5,
        "Add VIX-SPX correlation and divergence features"
    )
    
    if accept_5:
        best_features = features_5
        best_accuracy = acc_5
        print("   ✅ VIX-SPX Relationship features ACCEPTED")
    
    # Final Results
    print("\n" + "="*80)
    print("🏆 GRADUAL ENHANCEMENT RESULTS")
    print("="*80)
    
    print(f"📊 Baseline Accuracy: 49.3%")
    print(f"🎯 Final Accuracy: {best_accuracy:.1%}")
    print(f"📈 Total Improvement: {best_accuracy - 0.493:+.1%}")
    print(f"🔧 Final Features: {len(best_features)} (vs {len(enhancer.baseline_features)} baseline)")
    
    print(f"\n📋 Enhancement Summary:")
    for i, result in enumerate(enhancer.enhancement_results, 1):
        print(f"   {i}. {result['name']}: {result['status']} ({result['improvement']:+.1%})")
    
    # Save best system
    if best_accuracy > 0.493:
        print(f"\n💾 Saving enhanced system...")
        
        # Save features
        with open(config.get_data_file('selected_features'), 'w') as f:
            json.dump(best_features, f, indent=2)
        
        # Save enhancement results
        with open('enhancement_results.json', 'w') as f:
            json.dump(enhancer.enhancement_results, f, indent=2)
        
        print(f"   ✅ Enhanced features saved: {len(best_features)} features")
        print(f"   ✅ Enhancement results saved")
        
        return True, best_features, best_accuracy
    else:
        print(f"\n⚠️ No improvements found - keeping baseline system")
        return False, enhancer.baseline_features, 0.493

if __name__ == "__main__":
    success, final_features, final_accuracy = run_gradual_enhancement()
    
    if success:
        print(f"\n🎉 Enhanced system ready!")
        print(f"   🎯 Accuracy: {final_accuracy:.1%}")
        print(f"   🔧 Features: {len(final_features)}")
    else:
        print(f"\n🔧 Staying with baseline system")
