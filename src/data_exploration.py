import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Import configuration and data loader
from config import config
from data_loader import DataLoader

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")

print("=== SPX Option Data Analysis ===")
print(f"Analysis started at: {datetime.now()}")

# Load the data using the flexible data loader
print("\n1. Loading data...")
try:
    # Use the data loader to automatically find and load all matching files
    data_loader = DataLoader()
    data = data_loader.load_all_data()

    # Get data summary
    summary = data_loader.get_data_summary(data)
    print(f"\n📊 Data Summary:")
    print(f"   Total records: {summary['total_records']:,}")
    print(f"   Total columns: {summary['total_columns']}")
    print(f"   Memory usage: {summary['memory_usage_mb']:.1f} MB")
    print(f"   Missing values: {summary['missing_values']:,}")
    print(f"   Duplicate rows: {summary['duplicate_rows']:,}")

    if summary['date_range']:
        print(f"   Date range: {summary['date_range']['start'].date()} to {summary['date_range']['end'].date()}")
        print(f"   Total days: {summary['date_range']['days']}")

    if 'files_distribution' in summary:
        print(f"   Source files: {summary['source_files']}")

except FileNotFoundError as e:
    print(f"❌ No data files found: {e}")
    print(f"📁 Expected directory: {config.get_data_source_config('data_directory')}")
    print(f"🔍 Expected pattern: {config.get_data_source_config('file_pattern')}")
    print("\n💡 To fix this:")
    print("   1. Create the data directory and add your CSV files")
    print("   2. Or update the configuration:")
    print("      from config import config")
    print("      config.set_data_source_config('data_directory', '/path/to/your/data')")
    print("      config.set_data_source_config('file_pattern', 'your_pattern_*.csv')")
    exit(1)

except Exception as e:
    print(f"❌ Error loading data: {e}")
    exit(1)

# Basic data info
print("\n2. Data Structure:")
print(data.info())

# Remove source_file column if it exists (added by data loader)
if 'source_file' in data.columns:
    print(f"\n📁 Source files in dataset: {data['source_file'].nunique()}")
    data = data.drop('source_file', axis=1)

print("\n3. Column descriptions:")
columns_desc = {
    'date': 'Trading date',
    'spx_open': 'SPX opening price',
    'spx_high': 'SPX high price',
    'spx_low': 'SPX low price', 
    'spx_close': 'SPX closing price',
    'put_wall_strike': 'Strike price with highest put open interest',
    'put_wall_oi': 'Open interest at put wall strike',
    'call_wall_strike': 'Strike price with highest call open interest',
    'call_wall_oi': 'Open interest at call wall strike',
    'gamma_put_wall_strike': 'Strike with highest put gamma exposure',
    'gamma_put_wall_exposure': 'Put gamma exposure value',
    'gamma_call_wall_strike': 'Strike with highest call gamma exposure',
    'gamma_call_wall_exposure': 'Call gamma exposure value',
    'total_gamma': 'Total gamma across all options',
    'total_vega': 'Total vega across all options',
    'total_open_interest': 'Total open interest',
    'total_volume': 'Total options volume',
    'unique_strikes': 'Number of unique strike prices',
    'total_options': 'Total number of options contracts',
    'call_notional': 'Total notional value of calls',
    'put_notional': 'Total notional value of puts',
    'total_notional': 'Total notional value'
}

for col, desc in columns_desc.items():
    if col in data.columns:
        print(f"  {col}: {desc}")

# Check for missing values
print("\n4. Missing values:")
missing_values = data.isnull().sum()
print(missing_values[missing_values > 0])

# Basic statistics
print("\n5. Basic Statistics for key variables:")
key_vars = ['spx_close', 'total_gamma', 'total_vega', 'total_open_interest', 
           'total_volume', 'call_notional', 'put_notional']
print(data[key_vars].describe())

# Calculate daily returns and other derived features
print("\n6. Creating derived features...")
data['spx_return'] = data['spx_close'].pct_change()
data['spx_return_next'] = data['spx_return'].shift(-1)  # Next day return (target)
data['spx_direction_next'] = (data['spx_return_next'] > 0).astype(int)  # 1 for up, 0 for down

# Intraday range
data['spx_range'] = data['spx_high'] - data['spx_low']
data['spx_range_pct'] = data['spx_range'] / data['spx_open'] * 100

# Options metrics
data['put_call_oi_ratio'] = data['put_wall_oi'] / data['call_wall_oi']
data['put_call_notional_ratio'] = data['put_notional'] / data['call_notional']
data['gamma_exposure_ratio'] = data['gamma_call_wall_exposure'] / (data['gamma_put_wall_exposure'] + 1e-10)

# Distance from walls
data['spx_to_put_wall'] = (data['spx_close'] - data['put_wall_strike']) / data['spx_close'] * 100
data['spx_to_call_wall'] = (data['call_wall_strike'] - data['spx_close']) / data['spx_close'] * 100

print("Derived features created:")
derived_features = ['spx_return', 'spx_return_next', 'spx_direction_next', 'spx_range_pct',
                   'put_call_oi_ratio', 'put_call_notional_ratio', 'gamma_exposure_ratio',
                   'spx_to_put_wall', 'spx_to_call_wall']
for feature in derived_features:
    print(f"  {feature}: {data[feature].describe().loc[['mean', 'std']]}")

# Import configuration
from config import config

# Save processed data
data.to_csv(config.get_data_file('processed_data'), index=False)
print(f"\nProcessed data saved to: {config.get_data_file('processed_data')}")

print("\n=== Data exploration completed ===")

