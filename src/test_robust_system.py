"""
Test Robust SPX System - Show Last 5 Trades and Current Prediction
"""

import pandas as pd
import numpy as np
import pickle
import json
from datetime import datetime
from config import config

def test_robust_system():
    """Test the robust system and show recent trades + current prediction"""
    print("🧪 TESTING ROBUST SPX SYSTEM")
    print("=" * 60)
    
    # 1. Load system components
    print("1. Loading robust system...")
    
    # Load processed data
    data = pd.read_csv('spx_processed_data.csv')
    data['date'] = pd.to_datetime(data['date'])
    
    # Recreate baseline features
    data['spx_return_1d'] = data['spx_close'].pct_change(1)
    data['spx_return_5d'] = data['spx_close'].pct_change(5)
    data['spx_sma_5'] = data['spx_close'].rolling(5).mean()
    data['spx_sma_20'] = data['spx_close'].rolling(20).mean()
    data['spx_above_sma5'] = (data['spx_close'] > data['spx_sma_5']).astype(int)
    data['spx_above_sma20'] = (data['spx_close'] > data['spx_sma_20']).astype(int)
    data['vix_sma_5'] = data['vix_close'].rolling(5).mean()
    data['vix_sma_20'] = data['vix_close'].rolling(20).mean()
    data['vix_above_20'] = (data['vix_close'] > 20).astype(int)
    data['vix_above_30'] = (data['vix_close'] > 30).astype(int)
    data['volume_sma_5'] = data['total_volume'].rolling(5).mean()
    data['volume_sma_20'] = data['total_volume'].rolling(20).mean()
    data['volume_above_avg'] = (data['total_volume'] > data['volume_sma_20']).astype(int)
    data['gamma_normalized'] = data['total_gamma'] / data['total_gamma'].rolling(20).mean()
    data['put_call_ratio'] = data['put_notional'] / (data['call_notional'] + 1e-6)
    data['target'] = (data['next_day_return'] > 0).astype(int)
    
    # Load model components
    with open(config.get_model_file('best_model'), 'rb') as f:
        model = pickle.load(f)
    
    with open(config.get_model_file('scaler'), 'rb') as f:
        scaler = pickle.load(f)
    
    with open(config.get_data_file('selected_features'), 'r') as f:
        selected_features = json.load(f)
    
    print(f"   ✅ Loaded model with {len(selected_features)} features")
    
    # 2. Prepare data
    print("2. Preparing data...")
    
    clean_data = data.dropna(subset=['target'] + selected_features).copy()
    X = clean_data[selected_features].copy()
    
    # Handle missing values
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    X = X.replace([np.inf, -np.inf], np.nan)
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    print(f"   📊 Clean data shape: {X.shape}")
    
    # 3. Generate all predictions
    print("3. Generating predictions...")
    
    X_scaled = scaler.transform(X)
    predictions = model.predict(X_scaled)
    probabilities = model.predict_proba(X_scaled)[:, 1]
    
    # Add to data
    clean_data['prediction'] = predictions
    clean_data['probability'] = probabilities
    
    print(f"   ✅ Generated {len(predictions)} predictions")
    
    # 4. Get current prediction (most recent data point)
    print("4. Current prediction...")
    
    current_data = clean_data.iloc[-1]
    current_prediction = {
        'date': current_data['date'],
        'spx_close': current_data['spx_close'],
        'vix_close': current_data['vix_close'],
        'prediction': 'UP' if current_data['prediction'] == 1 else 'DOWN',
        'confidence': current_data['probability'] if current_data['prediction'] == 1 else (1 - current_data['probability']),
        'trade_signal': 'LONG' if current_data['probability'] > 0.55 else 'SHORT' if current_data['probability'] < 0.45 else 'NO TRADE'
    }
    
    print(f"\\n📅 CURRENT PREDICTION ({current_prediction['date'].strftime('%Y-%m-%d')}):")
    print(f"   📈 SPX Close: ${current_prediction['spx_close']:.2f}")
    print(f"   📊 VIX Close: {current_prediction['vix_close']:.2f}")
    print(f"   🎯 Prediction: {current_prediction['prediction']}")
    print(f"   📊 Confidence: {current_prediction['confidence']:.1%}")
    print(f"   🚦 Trade Signal: {current_prediction['trade_signal']}")
    
    # 5. Get last 5 completed trades
    print("\\n5. Last 5 completed trades...")
    
    # Load existing trades
    try:
        trades_df = pd.read_csv('robust_system_trades.csv')
        trades_df['date'] = pd.to_datetime(trades_df['date'])
        
        # Get last 5 trades
        last_5_trades = trades_df.tail(5).copy()
        
        print(f"\\n📊 LAST 5 TRADES:")
        print("=" * 80)
        
        for i, (_, trade) in enumerate(last_5_trades.iterrows(), 1):
            status = "✅ WIN" if trade['win'] else "❌ LOSS"
            direction_emoji = "📈" if trade['direction'] == 'long' else "📉"
            
            print(f"{i}. {trade['date'].strftime('%Y-%m-%d')} | {direction_emoji} {trade['direction'].upper()}")
            print(f"   Entry: ${trade['entry_price']:.2f} | Return: {trade['return_pct']:+.3f}% | {status}")
            print(f"   Confidence: {trade['confidence']:.1%}")
            print()
        
        # Summary of last 5 trades
        win_rate_5 = last_5_trades['win'].mean()
        total_return_5 = last_5_trades['return_pct'].sum()
        
        print(f"📊 LAST 5 TRADES SUMMARY:")
        print(f"   🏆 Win Rate: {win_rate_5:.1%} ({last_5_trades['win'].sum()}/5)")
        print(f"   💰 Total Return: {total_return_5:+.2f}%")
        print(f"   📈 Average Return: {last_5_trades['return_pct'].mean():+.3f}%")
        
    except FileNotFoundError:
        print("   ⚠️  No previous trades found. Run the full system first.")
    
    # 6. System status check
    print("\\n6. System status check...")
    
    # Check if we should trade today
    should_trade = current_prediction['trade_signal'] != 'NO TRADE'
    
    if should_trade:
        print(f"   🚦 TRADE SIGNAL: {current_prediction['trade_signal']}")
        print(f"   📊 Confidence: {current_prediction['confidence']:.1%}")
        
        if current_prediction['trade_signal'] == 'LONG':
            print(f"   📈 Recommendation: BUY SPX calls or long SPX")
        else:
            print(f"   📉 Recommendation: BUY SPX puts or short SPX")
    else:
        print(f"   ⏸️  NO TRADE: Confidence {current_prediction['confidence']:.1%} below threshold")
        print(f"   📊 Wait for confidence > 55% or < 45%")
    
    # 7. Model health check
    print("\\n7. Model health check...")
    
    # Check recent prediction accuracy
    recent_data = clean_data.tail(50)  # Last 50 days
    recent_accuracy = (recent_data['prediction'] == recent_data['target']).mean()
    
    print(f"   📊 Recent Accuracy (50 days): {recent_accuracy:.1%}")
    
    if recent_accuracy > 0.6:
        print(f"   ✅ Model performing well")
    elif recent_accuracy > 0.5:
        print(f"   ⚠️  Model performance acceptable")
    else:
        print(f"   ❌ Model performance degraded - needs attention")
    
    # 8. Feature importance check
    print("\\n8. Key features for current prediction...")
    
    # Get feature values for current prediction
    current_features = X.iloc[-1]
    
    print(f"   📊 Key Feature Values:")
    key_features = ['spx_return_1d', 'spx_return_5d', 'vix_close', 'total_volume', 'unique_strikes']
    
    for feature in key_features:
        if feature in current_features.index:
            value = current_features[feature]
            print(f"      {feature}: {value:.4f}")
    
    # 9. Save current prediction
    print("\\n9. Saving current prediction...")
    
    current_prediction_record = {
        'timestamp': datetime.now().isoformat(),
        'date': current_prediction['date'].isoformat(),
        'spx_close': float(current_prediction['spx_close']),
        'vix_close': float(current_prediction['vix_close']),
        'prediction': current_prediction['prediction'],
        'confidence': float(current_prediction['confidence']),
        'trade_signal': current_prediction['trade_signal'],
        'model_accuracy_recent': float(recent_accuracy)
    }
    
    with open('current_prediction.json', 'w') as f:
        json.dump(current_prediction_record, f, indent=2)
    
    print(f"   ✅ Current prediction saved to current_prediction.json")
    
    print("\\n✅ ROBUST SYSTEM TEST COMPLETED!")
    
    return current_prediction, should_trade

if __name__ == "__main__":
    current_pred, trade_signal = test_robust_system()
    
    if trade_signal:
        print(f"\\n🚨 TRADE ALERT: {current_pred['trade_signal']} with {current_pred['confidence']:.1%} confidence")
    else:
        print(f"\\n💤 NO TRADE: Wait for stronger signal")
