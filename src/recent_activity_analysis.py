"""
Recent Activity Analysis - Show Recent Market Activity and Potential Trades
"""

import pandas as pd
import numpy as np
import pickle
import json
from datetime import datetime, timedelta
from config import config

def analyze_recent_activity():
    """Analyze recent market activity and show potential trades"""
    print("📊 RECENT MARKET ACTIVITY ANALYSIS")
    print("=" * 60)
    
    # Load data and model
    data = pd.read_csv('spx_processed_data.csv')
    data['date'] = pd.to_datetime(data['date'])
    
    # Recreate features
    data['spx_return_1d'] = data['spx_close'].pct_change(1)
    data['spx_return_5d'] = data['spx_close'].pct_change(5)
    data['spx_sma_5'] = data['spx_close'].rolling(5).mean()
    data['spx_sma_20'] = data['spx_close'].rolling(20).mean()
    data['spx_above_sma5'] = (data['spx_close'] > data['spx_sma_5']).astype(int)
    data['spx_above_sma20'] = (data['spx_close'] > data['spx_sma_20']).astype(int)
    data['vix_sma_5'] = data['vix_close'].rolling(5).mean()
    data['vix_sma_20'] = data['vix_close'].rolling(20).mean()
    data['vix_above_20'] = (data['vix_close'] > 20).astype(int)
    data['vix_above_30'] = (data['vix_close'] > 30).astype(int)
    data['volume_sma_5'] = data['total_volume'].rolling(5).mean()
    data['volume_sma_20'] = data['total_volume'].rolling(20).mean()
    data['volume_above_avg'] = (data['total_volume'] > data['volume_sma_20']).astype(int)
    data['gamma_normalized'] = data['total_gamma'] / data['total_gamma'].rolling(20).mean()
    data['put_call_ratio'] = data['put_notional'] / (data['call_notional'] + 1e-6)
    data['next_day_return'] = data['spx_close'].pct_change(1).shift(-1)
    data['target'] = (data['next_day_return'] > 0).astype(int)
    
    # Load model
    with open('best_model.pkl', 'rb') as f:
        model = pickle.load(f)
    with open('scaler.pkl', 'rb') as f:
        scaler = pickle.load(f)
    with open('selected_features.json', 'r') as f:
        features = json.load(f)
    
    # Clean data and generate predictions
    clean_data = data.dropna(subset=['target'] + features).copy()
    X = clean_data[features].copy()
    
    # Handle missing values
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    X = X.replace([np.inf, -np.inf], np.nan)
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    # Generate predictions
    X_scaled = scaler.transform(X)
    predictions = model.predict(X_scaled)
    probabilities = model.predict_proba(X_scaled)[:, 1]
    
    clean_data['prediction'] = predictions
    clean_data['probability'] = probabilities
    
    # Get recent data (last 60 days)
    recent_data = clean_data.tail(60).copy()
    
    print(f"📅 Analyzing last 60 trading days")
    print(f"📊 Date range: {recent_data['date'].min().strftime('%Y-%m-%d')} to {recent_data['date'].max().strftime('%Y-%m-%d')}")
    
    # Show recent predictions with different confidence levels
    confidence_levels = [0.48, 0.50, 0.52, 0.55]
    
    print(f"\n📊 RECENT TRADING OPPORTUNITIES:")
    print("=" * 80)
    
    for conf_level in confidence_levels:
        long_signals = recent_data[recent_data['probability'] > conf_level]
        short_signals = recent_data[recent_data['probability'] < (1 - conf_level)]
        
        total_signals = len(long_signals) + len(short_signals)
        
        print(f"\n🎯 Confidence Level: {conf_level:.0%} / {(1-conf_level):.0%}")
        print(f"   📈 Long signals: {len(long_signals)}")
        print(f"   📉 Short signals: {len(short_signals)}")
        print(f"   📊 Total signals: {total_signals}")
        
        if total_signals > 0:
            # Show most recent signals
            all_signals = pd.concat([
                long_signals.assign(signal_type='LONG'),
                short_signals.assign(signal_type='SHORT')
            ]).sort_values('date').tail(5)
            
            print(f"   📋 Most Recent Signals:")
            for _, signal in all_signals.iterrows():
                direction = "📈" if signal['signal_type'] == 'LONG' else "📉"
                conf = signal['probability'] if signal['signal_type'] == 'LONG' else (1 - signal['probability'])
                print(f"      {signal['date'].strftime('%m/%d')} {direction} {signal['signal_type']} "
                      f"(${signal['spx_close']:.0f}, {conf:.1%})")
    
    # Current market status
    current = recent_data.iloc[-1]
    
    print(f"\n📅 CURRENT MARKET STATUS ({current['date'].strftime('%Y-%m-%d')}):")
    print(f"   📈 SPX: ${current['spx_close']:,.2f}")
    print(f"   📊 VIX: {current['vix_close']:.2f}")
    print(f"   🎯 Prediction: {'UP' if current['prediction'] == 1 else 'DOWN'}")
    print(f"   📊 Confidence: {current['probability'] if current['prediction'] == 1 else (1-current['probability']):.1%}")
    
    # Determine current signal with different thresholds
    print(f"\n🚦 TRADING SIGNALS BY CONFIDENCE LEVEL:")
    for conf_level in confidence_levels:
        if current['probability'] > conf_level:
            signal = "LONG"
        elif current['probability'] < (1 - conf_level):
            signal = "SHORT"
        else:
            signal = "NO TRADE"
        
        print(f"   {conf_level:.0%} threshold: {signal}")
    
    # Recent market volatility
    recent_returns = recent_data['spx_return_1d'].dropna()
    recent_volatility = recent_returns.std() * np.sqrt(252) * 100
    
    print(f"\n📊 RECENT MARKET CHARACTERISTICS:")
    print(f"   📈 Avg Daily Return: {recent_returns.mean()*100:+.3f}%")
    print(f"   📊 Volatility (annualized): {recent_volatility:.1f}%")
    print(f"   📊 VIX Average: {recent_data['vix_close'].mean():.1f}")
    print(f"   📊 Up Days: {(recent_returns > 0).mean():.1%}")
    
    # Model performance on recent data
    recent_accuracy = (recent_data['prediction'] == recent_data['target']).mean()
    print(f"   🎯 Recent Model Accuracy: {recent_accuracy:.1%}")
    
    # Recommendation
    print(f"\n💡 RECOMMENDATION:")
    if recent_accuracy > 0.55:
        print(f"   ✅ Model performing well on recent data")
        print(f"   📊 Consider using 52%/48% confidence thresholds")
    elif recent_accuracy > 0.5:
        print(f"   ⚠️  Model performance acceptable")
        print(f"   📊 Consider using 55%/45% confidence thresholds")
    else:
        print(f"   ❌ Model performance degraded on recent data")
        print(f"   📊 Consider model retraining or higher thresholds")
    
    # Save recent analysis
    recent_analysis = {
        'analysis_date': datetime.now().isoformat(),
        'recent_period_days': 60,
        'recent_accuracy': recent_accuracy,
        'recent_volatility': recent_volatility,
        'recent_up_days': (recent_returns > 0).mean(),
        'current_spx': float(current['spx_close']),
        'current_vix': float(current['vix_close']),
        'current_prediction': 'UP' if current['prediction'] == 1 else 'DOWN',
        'current_confidence': float(current['probability'] if current['prediction'] == 1 else (1-current['probability'])),
        'signals_by_threshold': {
            f"{conf:.0%}": "LONG" if current['probability'] > conf else "SHORT" if current['probability'] < (1-conf) else "NO TRADE"
            for conf in confidence_levels
        }
    }
    
    with open('recent_activity_analysis.json', 'w') as f:
        json.dump(recent_analysis, f, indent=2)
    
    print(f"\n✅ Recent activity analysis saved to: recent_activity_analysis.json")

if __name__ == "__main__":
    analyze_recent_activity()
