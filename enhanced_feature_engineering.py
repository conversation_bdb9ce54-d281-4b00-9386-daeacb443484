#!/usr/bin/env python3
"""
Enhanced Feature Engineering for SPX + VIX Options Data
======================================================

Creates advanced features from the enhanced SPX + VIX dataset for improved
predictive capabilities.

Author: Enhanced by Manus AI
Date: June 23, 2025
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Import configuration
from config import config

def main():
    print("=== Enhanced SPX + VIX Feature Engineering ===")
    
    # Check if engineered data already exists
    from pathlib import Path
    engineered_file = Path(config.get_data_file('engineered_data'))
    if engineered_file.exists():
        print(f"✅ Enhanced engineered data already exists: {engineered_file}")
        print("   Delete the file to force re-engineering")
        return
    
    try:
        # Load enhanced processed data
        print("\n1. Loading enhanced processed data...")
        processed_file = config.get_data_file('processed_data')
        data = pd.read_csv(processed_file)
        data['date'] = pd.to_datetime(data['date'])
        
        print(f"   📊 Starting with {data.shape[0]} observations")
        print(f"   📅 Date range: {data['date'].min().date()} to {data['date'].max().date()}")
        
        # Identify feature groups
        spx_base_features = [col for col in data.columns if not col.startswith('vix_') and 
                           col not in ['date', 'target_direction', 'spx_return', 'next_day_return']]
        vix_features = [col for col in data.columns if col.startswith('vix_')]
        
        print(f"   📈 SPX base features: {len(spx_base_features)}")
        print(f"   📊 VIX features: {len(vix_features)}")
        
        # 1. Create basic derived features
        print("\n2. Creating basic derived features...")
        
        # SPX price features
        data['spx_range_pct'] = (data['spx_high'] - data['spx_low']) / data['spx_close']
        data['spx_gap_pct'] = (data['spx_open'] - data['spx_close'].shift(1)) / data['spx_close'].shift(1)
        data['spx_body_pct'] = abs(data['spx_close'] - data['spx_open']) / data['spx_close']
        
        # Options flow ratios
        data['put_call_oi_ratio'] = data['put_wall_oi'] / (data['call_wall_oi'] + 1)
        data['put_call_notional_ratio'] = data['put_notional'] / (data['call_notional'] + 1)
        data['gamma_ratio'] = data['gamma_put_wall_exposure'] / (data['gamma_call_wall_exposure'] + 1)
        
        # Wall distances
        data['spx_to_put_wall'] = (data['spx_close'] - data['put_wall_strike']) / data['spx_close']
        data['spx_to_call_wall'] = (data['call_wall_strike'] - data['spx_close']) / data['spx_close']
        
        print(f"   ✅ Created basic derived features")
        
        # 2. Create lagged features
        print("\n3. Creating lagged features...")
        
        # Key features to lag
        lag_features = [
            'spx_close', 'spx_return', 'spx_range_pct', 'total_volume', 'total_gamma',
            'put_call_oi_ratio', 'put_call_notional_ratio', 'spx_to_put_wall', 'spx_to_call_wall'
        ]
        
        # Add VIX features to lag
        vix_lag_features = [
            'vix_close', 'vix_return', 'vix_volatility', 'vix_momentum', 
            'vix_put_call_notional_ratio', 'vix_total_volume'
        ]
        
        lag_features.extend([f for f in vix_lag_features if f in data.columns])
        
        # Create 1-3 day lags
        for feature in lag_features:
            if feature in data.columns:
                for lag in [1, 2, 3]:
                    data[f'{feature}_lag{lag}'] = data[feature].shift(lag)
        
        print(f"   ✅ Created lagged features for {len(lag_features)} variables")
        
        # 3. Create rolling window features
        print("\n4. Creating rolling window features...")
        
        # Rolling windows
        windows = [5, 10, 20]
        
        # SPX rolling features
        spx_rolling_features = ['spx_close', 'spx_return', 'spx_range_pct', 'total_volume']
        
        for feature in spx_rolling_features:
            if feature in data.columns:
                for window in windows:
                    data[f'{feature}_sma{window}'] = data[feature].rolling(window).mean()
                    data[f'{feature}_std{window}'] = data[feature].rolling(window).std()
                    if feature != 'spx_close':  # Don't create ratio for price level
                        data[f'{feature}_ratio{window}'] = data[feature] / data[f'{feature}_sma{window}']
        
        # VIX rolling features
        vix_rolling_features = ['vix_close', 'vix_return', 'vix_volatility']
        
        for feature in vix_rolling_features:
            if feature in data.columns:
                for window in windows:
                    data[f'{feature}_sma{window}'] = data[feature].rolling(window).mean()
                    data[f'{feature}_std{window}'] = data[feature].rolling(window).std()
                    if feature != 'vix_close':
                        data[f'{feature}_ratio{window}'] = data[feature] / data[f'{feature}_sma{window}']
        
        print(f"   ✅ Created rolling window features")
        
        # 4. Create momentum and trend features
        print("\n5. Creating momentum and trend features...")
        
        # SPX momentum
        for period in [5, 10, 20]:
            data[f'spx_momentum_{period}'] = data['spx_close'] / data['spx_close'].shift(period) - 1
            data[f'spx_rsi_{period}'] = calculate_rsi(data['spx_close'], period)
        
        # VIX momentum (if available)
        if 'vix_close' in data.columns:
            for period in [5, 10, 20]:
                data[f'vix_momentum_{period}'] = data['vix_close'] / data['vix_close'].shift(period) - 1
                data[f'vix_rsi_{period}'] = calculate_rsi(data['vix_close'], period)
        
        # Trend indicators
        data['spx_above_sma20'] = (data['spx_close'] > data['spx_close_sma20']).astype(int)
        data['spx_above_sma50'] = (data['spx_close'] > data['spx_close'].rolling(50).mean()).astype(int)
        
        if 'vix_close' in data.columns:
            data['vix_above_sma20'] = (data['vix_close'] > data['vix_close_sma20']).astype(int)
        
        print(f"   ✅ Created momentum and trend features")
        
        # 5. Create interaction features between SPX and VIX
        print("\n6. Creating SPX-VIX interaction features...")
        
        if 'vix_close' in data.columns:
            # VIX-SPX relationships
            data['vix_spx_ratio'] = data['vix_close'] / (data['spx_close'] / 1000)  # Normalize SPX
            data['vix_spx_correlation_5d'] = data['vix_return'].rolling(5).corr(data['spx_return'])
            data['vix_spx_correlation_20d'] = data['vix_return'].rolling(20).corr(data['spx_return'])
            
            # VIX term structure proxies
            data['vix_term_structure'] = data['vix_close'] / data['vix_close'].rolling(5).mean()
            
            # Combined volatility indicators
            data['vol_regime'] = pd.cut(data['vix_close'], bins=[0, 15, 20, 25, 100], 
                                      labels=[0, 1, 2, 3]).astype(float)
            
            # Options flow interactions
            if 'vix_total_volume' in data.columns:
                data['spx_vix_volume_ratio'] = data['total_volume'] / (data['vix_total_volume'] + 1)
            
            print(f"   ✅ Created SPX-VIX interaction features")
        
        # 6. Create advanced options features
        print("\n7. Creating advanced options features...")
        
        # Gamma exposure features
        data['net_gamma_exposure'] = data['gamma_call_wall_exposure'] - data['gamma_put_wall_exposure']
        data['gamma_imbalance'] = data['net_gamma_exposure'] / (data['total_gamma'] + 1)
        
        # Options positioning
        data['options_skew'] = data['put_notional'] / data['total_notional']
        data['options_concentration'] = data['total_options'] / data['unique_strikes']
        
        # VIX options features (if available)
        if 'vix_gamma_put_exposure' in data.columns:
            data['vix_net_gamma'] = data['vix_gamma_call_exposure'] - data['vix_gamma_put_exposure']
            data['vix_gamma_imbalance'] = data['vix_net_gamma'] / (data['vix_total_gamma'] + 1)
        
        print(f"   ✅ Created advanced options features")
        
        # 7. Clean up and finalize
        print("\n8. Finalizing enhanced dataset...")
        
        # Remove rows with too many missing values (first few rows due to lags)
        data = data.dropna(thresh=len(data.columns) * 0.7)  # Keep rows with at least 70% non-null
        
        # Fill remaining missing values
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if col not in ['target_direction']:
                data[col] = data[col].fillna(data[col].median())
        
        # Remove infinite values more aggressively
        print(f"   🔧 Cleaning infinite values...")

        # Replace infinite values with NaN
        data = data.replace([np.inf, -np.inf], np.nan)

        # Check for problematic columns
        inf_counts = {}
        for col in numeric_columns:
            if col not in ['target_direction']:
                inf_count = data[col].isnull().sum()
                if inf_count > 0:
                    inf_counts[col] = inf_count

        if inf_counts:
            print(f"   ⚠️  Found infinite/null values in {len(inf_counts)} columns")
            for col, count in sorted(inf_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"      {col}: {count} values")

        # Fill missing values with median, but cap extreme values first
        for col in numeric_columns:
            if col not in ['target_direction']:
                # Cap extreme values at 99.9th percentile
                if data[col].notna().sum() > 0:
                    q999 = data[col].quantile(0.999)
                    q001 = data[col].quantile(0.001)
                    data[col] = data[col].clip(lower=q001, upper=q999)

                    # Fill remaining NaN with median
                    median_val = data[col].median()
                    if pd.isna(median_val):
                        median_val = 0.0
                    data[col] = data[col].fillna(median_val)

        # Final check for any remaining infinite values
        final_inf_check = np.isinf(data.select_dtypes(include=[np.number])).sum().sum()
        if final_inf_check > 0:
            print(f"   ⚠️  Warning: {final_inf_check} infinite values remain")
            # Force replace any remaining infinite values
            data = data.replace([np.inf, -np.inf], 0.0)

        print(f"   ✅ Data cleaning completed")
        
        print(f"   ✅ Final enhanced dataset: {data.shape}")
        
        # 8. Save results
        print("\n9. Saving enhanced engineered data...")
        
        # Save engineered data
        data.to_csv(engineered_file, index=False)
        print(f"   ✅ Saved to: {engineered_file}")
        
        # Create and save feature info
        all_features = [col for col in data.columns if col not in ['date', 'target_direction', 'spx_return', 'next_day_return']]
        spx_features = [col for col in all_features if not col.startswith('vix_')]
        vix_features = [col for col in all_features if col.startswith('vix_')]
        
        feature_info = {
            'total_features': len(all_features),
            'spx_features': len(spx_features),
            'vix_features': len(vix_features),
            'all_features': all_features,
            'spx_feature_list': spx_features,
            'vix_feature_list': vix_features,
            'target_column': 'target_direction',
            'date_column': 'date'
        }
        
        feature_info_file = config.get_data_file('feature_info')
        import json
        with open(feature_info_file, 'w') as f:
            json.dump(feature_info, f, indent=2)
        
        print(f"   ✅ Feature info saved to: {feature_info_file}")
        
        # Summary
        print(f"\n✅ Enhanced feature engineering completed!")
        print(f"   📊 Total features: {len(all_features)}")
        print(f"   📈 SPX features: {len(spx_features)}")
        print(f"   📊 VIX features: {len(vix_features)}")
        print(f"   🎯 Target variable: target_direction")
        print(f"   📅 Final date range: {data['date'].min().date()} to {data['date'].max().date()}")
        
    except Exception as e:
        print(f"❌ Enhanced feature engineering failed: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

def calculate_rsi(prices, period=14):
    """Calculate RSI indicator"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

if __name__ == "__main__":
    main()
