#!/usr/bin/env python3
"""
Enhanced SPX Option Trading Strategy Backtesting
===============================================

Focus on improving Sharpe ratio through:
1. Dynamic position sizing based on confidence
2. Volatility-adjusted stop losses and take profits
3. Market regime detection
4. Risk-adjusted entry/exit rules
5. Portfolio heat management
6. Advanced risk metrics

Author: Enhanced by Manus AI
Date: June 23, 2025
"""

import pandas as pd
import numpy as np
import json
import pickle
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import configuration
from config import config

print("=== Enhanced SPX Option Trading Strategy Backtesting ===")

class EnhancedTradingStrategy:
    def __init__(self, data, model, scaler, selected_features):
        self.data = data
        self.model = model
        self.scaler = scaler
        self.selected_features = selected_features
        self.trades = []
        self.portfolio_value = 100000  # Starting capital
        self.max_portfolio_risk = 0.02  # Max 2% portfolio risk per trade
        self.max_concurrent_positions = 3
        self.current_positions = []
        
    def calculate_volatility_regime(self, window=20):
        """Calculate market volatility regime"""
        returns = self.data['spx_close'].pct_change()
        rolling_vol = returns.rolling(window).std() * np.sqrt(252)  # Annualized
        vol_percentile = rolling_vol.rolling(252).rank(pct=True)  # Percentile over 1 year
        
        # Define regimes
        regime = pd.Series(index=self.data.index, dtype='object')
        regime[vol_percentile <= 0.33] = 'low_vol'
        regime[(vol_percentile > 0.33) & (vol_percentile <= 0.67)] = 'medium_vol'
        regime[vol_percentile > 0.67] = 'high_vol'
        
        return regime.fillna('medium_vol')
    
    def calculate_dynamic_position_size(self, confidence, volatility_regime, current_vol):
        """Calculate position size based on confidence and market conditions"""
        base_size = 0.01  # 1% base position
        
        # Confidence adjustment (0.5-1.0 confidence -> 0.5x-2.0x multiplier)
        confidence_multiplier = 0.5 + (confidence - 0.5) * 3
        
        # Volatility regime adjustment
        vol_multipliers = {
            'low_vol': 1.5,    # Larger positions in low vol
            'medium_vol': 1.0,  # Normal positions
            'high_vol': 0.6     # Smaller positions in high vol
        }
        vol_multiplier = vol_multipliers.get(volatility_regime, 1.0)
        
        # Current volatility adjustment
        if current_vol > 0.25:  # High current volatility
            vol_adjustment = 0.7
        elif current_vol < 0.15:  # Low current volatility
            vol_adjustment = 1.2
        else:
            vol_adjustment = 1.0
        
        # Calculate final position size
        position_size = base_size * confidence_multiplier * vol_multiplier * vol_adjustment
        
        # Cap at maximum risk
        max_size = self.max_portfolio_risk
        position_size = min(position_size, max_size)
        
        return position_size
    
    def calculate_dynamic_stops(self, volatility_regime, current_vol, confidence):
        """Calculate dynamic stop loss and take profit based on market conditions"""
        
        # Base stops
        base_stop_loss = 1.5
        base_take_profit = 2.5
        
        # Volatility adjustments
        vol_adjustments = {
            'low_vol': {'stop': 0.8, 'profit': 0.9},      # Tighter stops in low vol
            'medium_vol': {'stop': 1.0, 'profit': 1.0},   # Normal stops
            'high_vol': {'stop': 1.4, 'profit': 1.3}      # Wider stops in high vol
        }
        
        vol_adj = vol_adjustments.get(volatility_regime, vol_adjustments['medium_vol'])
        
        # Current volatility fine-tuning
        if current_vol > 0.25:
            vol_fine_tune = 1.3
        elif current_vol < 0.15:
            vol_fine_tune = 0.8
        else:
            vol_fine_tune = 1.0
        
        # Confidence adjustment (higher confidence = wider stops)
        confidence_adj = 0.8 + (confidence - 0.5) * 0.6
        
        # Calculate final stops
        stop_loss = base_stop_loss * vol_adj['stop'] * vol_fine_tune * confidence_adj
        take_profit = base_take_profit * vol_adj['profit'] * vol_fine_tune * confidence_adj
        
        return stop_loss, take_profit
    
    def calculate_risk_adjusted_entry(self, row, confidence, volatility_regime):
        """Determine if we should enter based on risk-adjusted criteria"""
        
        # Base confidence threshold by regime
        base_thresholds = {
            'low_vol': 0.55,    # Lower threshold in stable markets
            'medium_vol': 0.60,  # Normal threshold
            'high_vol': 0.70     # Higher threshold in volatile markets
        }
        
        required_confidence = base_thresholds.get(volatility_regime, 0.60)
        
        # Additional filters
        
        # 1. Don't trade if too many concurrent positions
        if len(self.current_positions) >= self.max_concurrent_positions:
            return False, "max_positions"
        
        # 2. Check if confidence meets regime-adjusted threshold
        if confidence < required_confidence:
            return False, "low_confidence"
        
        # 3. Avoid trading in extreme market conditions
        recent_returns = self.data['spx_close'].pct_change().tail(5)
        if abs(recent_returns.sum()) > 0.05:  # Avoid if 5-day move > 5%
            return False, "extreme_market"
        
        # 4. Check for market gaps (avoid trading after large gaps)
        if 'spx_open' in row and row.name > 0:
            try:
                prev_close = self.data.iloc[row.name-1]['spx_close']
                gap_pct = abs(row['spx_open'] - prev_close) / prev_close
                if gap_pct > 0.015:  # Avoid if gap > 1.5%
                    return False, "large_gap"
            except (IndexError, KeyError):
                pass  # Skip gap check if previous data not available
        
        return True, "approved"
    
    def run_enhanced_backtest(self, start_date=None, end_date=None):
        """Run enhanced backtesting with improved risk management"""
        
        # Prepare data
        if start_date:
            self.data = self.data[self.data['date'] >= start_date]
        if end_date:
            self.data = self.data[self.data['date'] <= end_date]
        
        # Calculate volatility regime
        vol_regime = self.calculate_volatility_regime()
        
        # Calculate rolling volatility
        returns = self.data['spx_close'].pct_change()
        rolling_vol = returns.rolling(20).std() * np.sqrt(252)
        
        # Get predictions (assuming they're already in the data)
        # If not, we'd need to generate them here
        
        self.trades = []
        self.current_positions = []
        portfolio_values = [self.portfolio_value]
        
        for i in range(len(self.data) - 1):
            try:
                current_row = self.data.iloc[i]
                next_row = self.data.iloc[i + 1]
            except IndexError:
                continue
            
            current_regime = vol_regime.iloc[i]
            current_vol = rolling_vol.iloc[i] if not pd.isna(rolling_vol.iloc[i]) else 0.20
            
            # Check for position exits first
            positions_to_close = []
            for pos_idx, position in enumerate(self.current_positions):
                exit_signal, exit_reason = self.check_exit_conditions(
                    position, current_row, current_regime, current_vol
                )
                
                if exit_signal:
                    # Close position
                    trade_result = self.close_position(position, current_row, exit_reason)
                    self.trades.append(trade_result)
                    positions_to_close.append(pos_idx)
            
            # Remove closed positions
            for idx in reversed(positions_to_close):
                self.current_positions.pop(idx)
            
            # Check for new entries
            if hasattr(current_row, 'prediction_proba') and hasattr(current_row, 'prediction'):
                confidence = current_row['prediction_proba']
                prediction = current_row['prediction']
                
                # Risk-adjusted entry decision
                should_enter, entry_reason = self.calculate_risk_adjusted_entry(
                    current_row, confidence, current_regime
                )
                
                if should_enter:
                    # Calculate position parameters
                    position_size = self.calculate_dynamic_position_size(
                        confidence, current_regime, current_vol
                    )
                    stop_loss, take_profit = self.calculate_dynamic_stops(
                        current_regime, current_vol, confidence
                    )
                    
                    # Enter new position
                    new_position = self.enter_position(
                        next_row, prediction, confidence, position_size, 
                        stop_loss, take_profit, current_regime
                    )
                    
                    if new_position:
                        self.current_positions.append(new_position)
            
            # Update portfolio value
            current_portfolio_value = self.calculate_portfolio_value(current_row)
            portfolio_values.append(current_portfolio_value)
        
        # Close any remaining positions
        if self.current_positions:
            final_row = self.data.iloc[-1]
            for position in self.current_positions:
                trade_result = self.close_position(position, final_row, 'end_of_data')
                self.trades.append(trade_result)
        
        return pd.DataFrame(self.trades), portfolio_values
    
    def check_exit_conditions(self, position, current_row, regime, current_vol):
        """Check if position should be exited"""
        entry_price = position['entry_price']
        direction = position['direction']
        current_price = current_row['spx_close']
        
        # Calculate current P&L
        if direction == 'long':
            pnl_pct = (current_price - entry_price) / entry_price * 100
        else:
            pnl_pct = (entry_price - current_price) / entry_price * 100
        
        # Check stop loss
        if pnl_pct <= -position['stop_loss']:
            return True, 'stop_loss'
        
        # Check take profit
        if pnl_pct >= position['take_profit']:
            return True, 'take_profit'
        
        # Check time-based exit (max 5 days, but regime-dependent)
        days_held = (current_row['date'] - position['entry_date']).days
        max_days = {'low_vol': 5, 'medium_vol': 4, 'high_vol': 3}
        if days_held >= max_days.get(regime, 4):
            return True, 'time_exit'
        
        # Check for regime change exit
        if position['entry_regime'] != regime and days_held >= 2:
            return True, 'regime_change'
        
        return False, None
    
    def enter_position(self, entry_row, prediction, confidence, position_size, 
                      stop_loss, take_profit, regime):
        """Enter a new position"""
        
        direction = 'long' if prediction == 1 else 'short'
        entry_price = entry_row['spx_open']
        
        position = {
            'entry_date': entry_row['date'],
            'entry_price': entry_price,
            'direction': direction,
            'confidence': confidence,
            'position_size': position_size,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'entry_regime': regime,
            'entry_index': entry_row.name
        }
        
        return position
    
    def close_position(self, position, exit_row, exit_reason):
        """Close a position and record the trade"""
        
        exit_price = exit_row['spx_close']
        direction = position['direction']
        
        # Calculate P&L
        if direction == 'long':
            pnl_pct = (exit_price - position['entry_price']) / position['entry_price'] * 100
            pnl_points = exit_price - position['entry_price']
        else:
            pnl_pct = (position['entry_price'] - exit_price) / position['entry_price'] * 100
            pnl_points = position['entry_price'] - exit_price
        
        # Calculate position-size adjusted return
        portfolio_pnl = pnl_pct * position['position_size']
        
        days_held = (exit_row['date'] - position['entry_date']).days
        
        trade_record = {
            'entry_date': position['entry_date'],
            'exit_date': exit_row['date'],
            'direction': direction,
            'entry_price': position['entry_price'],
            'exit_price': exit_price,
            'pnl_pct': pnl_pct,
            'pnl_points': pnl_points,
            'portfolio_pnl': portfolio_pnl,
            'position_size': position['position_size'],
            'days_held': days_held,
            'exit_reason': exit_reason,
            'confidence': position['confidence'],
            'entry_regime': position['entry_regime'],
            'stop_loss': position['stop_loss'],
            'take_profit': position['take_profit']
        }
        
        return trade_record
    
    def calculate_portfolio_value(self, current_row):
        """Calculate current portfolio value including open positions"""
        # This is a simplified calculation
        # In practice, you'd track cash and position values separately
        return self.portfolio_value

def calculate_enhanced_metrics(trades_df):
    """Calculate enhanced performance metrics focused on Sharpe ratio"""
    if len(trades_df) == 0:
        return {}

    # Basic metrics
    total_trades = len(trades_df)
    winning_trades = len(trades_df[trades_df['portfolio_pnl'] > 0])
    win_rate = winning_trades / total_trades

    # Portfolio-adjusted returns
    portfolio_returns = trades_df['portfolio_pnl']
    total_return = portfolio_returns.sum()
    avg_return = portfolio_returns.mean()

    # Risk metrics
    return_std = portfolio_returns.std()
    sharpe_ratio = avg_return / return_std if return_std > 0 else 0

    # Annualized Sharpe (assuming average trade duration)
    avg_days = trades_df['days_held'].mean()
    trades_per_year = 252 / avg_days if avg_days > 0 else 0
    annualized_sharpe = sharpe_ratio * np.sqrt(trades_per_year) if trades_per_year > 0 else 0

    # Drawdown analysis
    cumulative_returns = portfolio_returns.cumsum()
    running_max = cumulative_returns.expanding().max()
    drawdown = cumulative_returns - running_max
    max_drawdown = drawdown.min()

    # Calmar ratio (return/max_drawdown)
    calmar_ratio = total_return / abs(max_drawdown) if max_drawdown != 0 else 0

    # Sortino ratio (downside deviation)
    negative_returns = portfolio_returns[portfolio_returns < 0]
    downside_std = negative_returns.std() if len(negative_returns) > 0 else return_std
    sortino_ratio = avg_return / downside_std if downside_std > 0 else 0

    # Win/Loss analysis
    wins = portfolio_returns[portfolio_returns > 0]
    losses = portfolio_returns[portfolio_returns <= 0]
    avg_win = wins.mean() if len(wins) > 0 else 0
    avg_loss = losses.mean() if len(losses) > 0 else 0
    profit_factor = abs(wins.sum() / losses.sum()) if len(losses) > 0 and losses.sum() != 0 else float('inf')

    # Consistency metrics
    positive_months = 0  # Would need to group by month
    total_months = 0     # Would need to group by month

    return {
        'total_trades': total_trades,
        'win_rate': win_rate,
        'total_return': total_return,
        'avg_return_per_trade': avg_return,
        'sharpe_ratio': sharpe_ratio,
        'annualized_sharpe': annualized_sharpe,
        'sortino_ratio': sortino_ratio,
        'calmar_ratio': calmar_ratio,
        'max_drawdown': max_drawdown,
        'profit_factor': profit_factor,
        'avg_win': avg_win,
        'avg_loss': avg_loss,
        'return_std': return_std
    }

# Main execution
if __name__ == "__main__":
    # Load data and models
    print("\n1. Loading data and models...")
    data = pd.read_csv(config.get_data_file('engineered_data'))
    data['date'] = pd.to_datetime(data['date'])

    with open(config.get_model_file('best_model'), 'rb') as f:
        best_model = pickle.load(f)

    with open(config.get_model_file('scaler'), 'rb') as f:
        scaler = pickle.load(f)

    with open(config.get_data_file('selected_features'), 'r') as f:
        selected_features = json.load(f)

    # Prepare data for backtesting (same as original)
    print("\n2. Preparing data for backtesting...")
    target_col = 'target_direction'
    data_clean = data.dropna(subset=[target_col]).copy()

    # Get predictions (reuse existing logic)
    with open(config.get_data_file('feature_info'), 'r') as f:
        feature_info = json.load(f)
    all_features = feature_info['all_features']

    X_all = data_clean[all_features].copy()
    X_all = X_all.replace([np.inf, -np.inf], np.nan)

    for col in X_all.columns:
        if X_all[col].dtype in ['float64', 'int64']:
            X_all[col] = X_all[col].fillna(X_all[col].median())

    missing_pct = X_all.isnull().sum() / len(X_all)
    features_to_keep = []

    for col in X_all.columns:
        if missing_pct[col] <= 0.5:
            if X_all[col].nunique() > 1:
                if np.isfinite(X_all[col]).all():
                    features_to_keep.append(col)

    X_all = X_all[features_to_keep]
    X_scaled_all = scaler.transform(X_all)
    X_scaled_all = np.nan_to_num(X_scaled_all, nan=0.0, posinf=3.0, neginf=-3.0)
    X_scaled_all = np.clip(X_scaled_all, -10, 10)
    X_scaled_all = pd.DataFrame(X_scaled_all, columns=X_all.columns, index=X_all.index)
    X_scaled = X_scaled_all[selected_features]

    # Get predictions
    predictions = best_model.predict(X_scaled)
    probabilities = best_model.predict_proba(X_scaled)[:, 1]

    data_clean['prediction'] = predictions
    data_clean['prediction_proba'] = probabilities
    data_clean['actual_direction'] = data_clean[target_col]

    print(f"Enhanced backtesting data shape: {data_clean.shape}")

    # Run enhanced backtesting
    print("\n3. Running enhanced backtesting...")
    strategy = EnhancedTradingStrategy(data_clean, best_model, scaler, selected_features)

    # Split data for walk-forward analysis
    split_date = data_clean['date'].quantile(0.7)  # Use 70% for in-sample, 30% for out-of-sample

    print("Running out-of-sample backtest...")
    trades_df, portfolio_values = strategy.run_enhanced_backtest(start_date=split_date)

    print(f"Enhanced backtest completed: {len(trades_df)} trades generated")

    # Calculate enhanced metrics
    print("\n4. Calculating enhanced performance metrics...")
    enhanced_metrics = calculate_enhanced_metrics(trades_df)

    print("Enhanced Performance Metrics:")
    print("="*60)
    for metric, value in enhanced_metrics.items():
        if isinstance(value, float):
            print(f"{metric:<25}: {value:.4f}")
        else:
            print(f"{metric:<25}: {value}")

    # Save enhanced results
    print("\n5. Saving enhanced results...")

    # Save trades
    if len(trades_df) > 0:
        trades_df.to_csv(config.get_output_file('best_trades'), index=False)

    # Save enhanced metrics as backtest results
    backtest_results = {
        'enhanced': enhanced_metrics
    }

    with open(config.get_data_file('backtest_results'), 'w') as f:
        json.dump(backtest_results, f, indent=2)

    # Create enhanced visualizations
    print("\n6. Creating enhanced visualizations...")

    if len(trades_df) > 0:
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Enhanced Trading Strategy Performance', fontsize=16, fontweight='bold')

        # Portfolio returns
        portfolio_returns = trades_df['portfolio_pnl'].cumsum()
        axes[0,0].plot(portfolio_returns.index, portfolio_returns.values, linewidth=2, color='blue')
        axes[0,0].set_title(f'Cumulative Portfolio Returns\nSharpe: {enhanced_metrics["sharpe_ratio"]:.3f}')
        axes[0,0].set_ylabel('Cumulative Return (%)')
        axes[0,0].grid(True, alpha=0.3)

        # Return distribution
        axes[0,1].hist(trades_df['portfolio_pnl'], bins=20, alpha=0.7, color='green', edgecolor='black')
        axes[0,1].axvline(x=0, color='red', linestyle='--', alpha=0.7)
        axes[0,1].set_title('Portfolio Return Distribution')
        axes[0,1].set_xlabel('Portfolio Return (%)')
        axes[0,1].set_ylabel('Frequency')
        axes[0,1].grid(True, alpha=0.3)

        # Drawdown
        cumulative = trades_df['portfolio_pnl'].cumsum()
        running_max = cumulative.expanding().max()
        drawdown = cumulative - running_max
        axes[0,2].fill_between(drawdown.index, drawdown.values, 0, alpha=0.7, color='red')
        axes[0,2].set_title(f'Drawdown\nMax: {enhanced_metrics["max_drawdown"]:.2f}%')
        axes[0,2].set_ylabel('Drawdown (%)')
        axes[0,2].grid(True, alpha=0.3)

        # Position size vs confidence
        axes[1,0].scatter(trades_df['confidence'], trades_df['position_size'], alpha=0.6, color='purple')
        axes[1,0].set_title('Position Size vs Confidence')
        axes[1,0].set_xlabel('Confidence')
        axes[1,0].set_ylabel('Position Size')
        axes[1,0].grid(True, alpha=0.3)

        # Performance by regime
        if 'entry_regime' in trades_df.columns:
            regime_performance = trades_df.groupby('entry_regime')['portfolio_pnl'].mean()
            axes[1,1].bar(regime_performance.index, regime_performance.values, alpha=0.7, color='orange')
            axes[1,1].set_title('Avg Return by Market Regime')
            axes[1,1].set_ylabel('Avg Portfolio Return (%)')
            axes[1,1].grid(True, alpha=0.3)

        # Risk-adjusted returns over time
        rolling_sharpe = trades_df['portfolio_pnl'].rolling(20).mean() / trades_df['portfolio_pnl'].rolling(20).std()
        axes[1,2].plot(rolling_sharpe.index, rolling_sharpe.values, linewidth=2, color='darkgreen')
        axes[1,2].set_title('Rolling 20-Trade Sharpe Ratio')
        axes[1,2].set_ylabel('Sharpe Ratio')
        axes[1,2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(config.get_output_file('trading_performance_chart'), dpi=300, bbox_inches='tight')
        plt.close()

    print("Enhanced backtesting completed!")
    print(f"Key Improvement: Sharpe Ratio = {enhanced_metrics.get('sharpe_ratio', 0):.4f}")
    print(f"Annualized Sharpe: {enhanced_metrics.get('annualized_sharpe', 0):.4f}")
    print(f"Sortino Ratio: {enhanced_metrics.get('sortino_ratio', 0):.4f}")
    print(f"Calmar Ratio: {enhanced_metrics.get('calmar_ratio', 0):.4f}")

    print("\n=== Enhanced backtesting completed ===")
