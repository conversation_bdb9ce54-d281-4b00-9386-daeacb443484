"""
Fix Critical Prediction Inversion Issue
The current system has R-squared = -1.0 because predictions are inverted
"""

import pandas as pd
import numpy as np
import pickle
import json
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, roc_auc_score
from config import config

def fix_prediction_system():
    """Fix the inverted prediction system with a simpler, more robust approach"""
    print("🔧 FIXING CRITICAL PREDICTION INVERSION")
    print("=" * 60)
    
    # 1. Load data
    print("1. Loading data...")
    data = pd.read_csv(config.get_data_file('engineered_data'))
    print(f"   📊 Data shape: {data.shape}")
    
    # 2. Create simple, robust target
    print("2. Creating robust target variable...")
    data['spx_return_1d'] = data['spx_close'].pct_change(1).shift(-1)  # Next day return
    data['target'] = (data['spx_return_1d'] > 0).astype(int)  # 1 if up, 0 if down
    
    # Remove last row (no future return)
    data = data[:-1].copy()
    
    print(f"   📈 Target distribution: {data['target'].value_counts().to_dict()}")
    print(f"   📊 Target balance: {data['target'].mean():.1%} positive")
    
    # 3. Use only the most reliable features
    print("3. Selecting robust features...")
    
    # Core reliable features
    reliable_features = [
        # Price momentum
        'spx_close_sma5', 'spx_close_sma20', 'spx_close_std5',
        'spx_return_ratio5', 'spx_return_ratio10',
        
        # VIX features
        'vix_close', 'vix_close_sma5', 'vix_close_sma20',
        'vix_return_ratio5', 'vix_return_ratio10',
        
        # Volume
        'total_volume', 'total_volume_sma5', 'total_volume_sma20',
        
        # Options flow
        'total_gamma', 'net_gamma_exposure', 'call_notional', 'put_notional',
        
        # Market structure
        'total_open_interest', 'unique_strikes'
    ]
    
    # Filter to available features
    available_features = [f for f in reliable_features if f in data.columns]
    print(f"   ✅ Using {len(available_features)} reliable features")
    
    # 4. Prepare clean data
    print("4. Preparing clean training data...")
    
    # Remove rows with missing target
    clean_data = data.dropna(subset=['target']).copy()
    
    # Prepare features
    X = clean_data[available_features].copy()
    y = clean_data['target'].copy()
    
    # Handle missing values
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    # Replace infinite values
    X = X.replace([np.inf, -np.inf], np.nan)
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    print(f"   📊 Clean data shape: {X.shape}")
    print(f"   🎯 Target shape: {y.shape}")
    
    # 5. Split data chronologically
    print("5. Creating chronological train/test split...")
    
    # Use first 80% for training, last 20% for testing
    split_idx = int(len(X) * 0.8)
    
    X_train = X.iloc[:split_idx].copy()
    X_test = X.iloc[split_idx:].copy()
    y_train = y.iloc[:split_idx].copy()
    y_test = y.iloc[split_idx:].copy()
    
    print(f"   📊 Train: {X_train.shape}, Test: {X_test.shape}")
    print(f"   📈 Train target: {y_train.mean():.1%} positive")
    print(f"   📈 Test target: {y_test.mean():.1%} positive")
    
    # 6. Train simple, robust model
    print("6. Training robust Random Forest model...")
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Train Random Forest (simple and robust)
    model = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        min_samples_split=20,
        min_samples_leaf=10,
        random_state=42,
        class_weight='balanced'
    )
    
    model.fit(X_train_scaled, y_train)
    
    # 7. Validate model
    print("7. Validating model performance...")
    
    # Predictions
    train_pred = model.predict(X_train_scaled)
    test_pred = model.predict(X_test_scaled)
    train_proba = model.predict_proba(X_train_scaled)[:, 1]
    test_proba = model.predict_proba(X_test_scaled)[:, 1]
    
    # Metrics
    train_acc = accuracy_score(y_train, train_pred)
    test_acc = accuracy_score(y_test, test_pred)
    train_auc = roc_auc_score(y_train, train_proba)
    test_auc = roc_auc_score(y_test, test_proba)
    
    print(f"   📊 Train Accuracy: {train_acc:.1%}")
    print(f"   📊 Test Accuracy: {test_acc:.1%}")
    print(f"   📊 Train AUC: {train_auc:.3f}")
    print(f"   📊 Test AUC: {test_auc:.3f}")
    
    # Check for overfitting
    if train_acc - test_acc > 0.1:
        print("   ⚠️  Warning: Possible overfitting detected")
    else:
        print("   ✅ Model appears robust")
    
    # 8. Test prediction direction
    print("8. Testing prediction direction...")
    
    # Create test predictions
    test_data = clean_data.iloc[split_idx:].copy()
    test_data['predicted_proba'] = test_proba
    test_data['predicted_direction'] = (test_proba > 0.5).astype(int)
    
    # Check if predictions align with reality
    correct_predictions = (test_data['predicted_direction'] == test_data['target']).mean()
    print(f"   🎯 Prediction accuracy: {correct_predictions:.1%}")
    
    if correct_predictions > 0.5:
        print("   ✅ Predictions are correctly aligned")
    else:
        print("   🚨 Predictions are still inverted!")
        return False
    
    # 9. Save corrected model
    print("9. Saving corrected model...")
    
    # Backup original files
    try:
        with open(config.get_model_file('best_model'), 'rb') as f:
            original_model = pickle.load(f)
        with open(config.get_model_file('best_model').replace('.pkl', '_broken.pkl'), 'wb') as f:
            pickle.dump(original_model, f)
        print("   📁 Original broken model backed up")
    except:
        print("   📁 No original model to backup")
    
    # Save new model and scaler
    with open(config.get_model_file('best_model'), 'wb') as f:
        pickle.dump(model, f)
    
    with open(config.get_model_file('scaler'), 'wb') as f:
        pickle.dump(scaler, f)
    
    # Save feature list
    with open(config.get_data_file('selected_features'), 'w') as f:
        json.dump(available_features, f, indent=2)
    
    print(f"   ✅ Corrected model saved")
    print(f"   ✅ Corrected scaler saved")
    print(f"   ✅ Feature list updated: {len(available_features)} features")
    
    print("\n✅ PREDICTION SYSTEM FIXED!")
    print(f"   🎯 Test Accuracy: {test_acc:.1%}")
    print(f"   📊 Test AUC: {test_auc:.3f}")
    print(f"   🔧 Using {len(available_features)} robust features")
    print(f"   🚀 Ready for corrected backtesting")
    
    return True

if __name__ == "__main__":
    success = fix_prediction_system()
    if success:
        print("\n🎉 Prediction system successfully fixed!")
        print("   Run the main pipeline again to see corrected results.")
    else:
        print("\n❌ Failed to fix prediction system!")
        print("   Manual intervention required.")
