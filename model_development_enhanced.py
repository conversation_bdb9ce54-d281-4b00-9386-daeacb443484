#!/usr/bin/env python3
"""
Enhanced SPX Option Predictive Model Development
===============================================

Major improvements:
1. Advanced hyperparameter optimization with Optuna
2. Time series cross-validation for robust evaluation
3. Advanced feature engineering and selection
4. Ensemble methods with stacking
5. Class imbalance handling
6. Early stopping and regularization
7. Model interpretability with SHAP
8. Advanced evaluation metrics

Author: Enhanced by Manus AI
Date: June 23, 2025
"""

import pandas as pd
import numpy as np
import json
import warnings
warnings.filterwarnings('ignore')

# Core ML libraries
from sklearn.model_selection import TimeSeriesSplit, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, RobustScaler, QuantileTransformer
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier, StackingClassifier
from sklearn.linear_model import LogisticRegression, RidgeClassifier
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, precision_recall_curve
from sklearn.feature_selection import SelectKBest, f_classif, RFE, RFECV
from sklearn.utils.class_weight import compute_class_weight
from sklearn.ensemble import IsolationForest

# Advanced ML libraries
import xgboost as xgb
import lightgbm as lgb
import catboost as cb
import optuna
from imblearn.over_sampling import SMOTE, ADASYN
from imblearn.under_sampling import RandomUnderSampler
from imblearn.combine import SMOTETomek

# Interpretability
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    print("SHAP not available - install with: pip install shap")

print("=== Enhanced SPX Option Predictive Model Development ===")

# Import configuration
from config import config

# Load engineered data and feature info
data = pd.read_csv(config.get_data_file('engineered_data'))
with open(config.get_data_file('feature_info'), 'r') as f:
    feature_info = json.load(f)

print(f"Loaded data shape: {data.shape}")

class EnhancedModelTrainer:
    def __init__(self, data, feature_info):
        self.data = data
        self.feature_info = feature_info
        self.models = {}
        self.model_results = {}
        self.best_model = None
        self.best_model_name = None
        self.scaler = None
        self.selected_features = None
        
    def prepare_data(self):
        """Enhanced data preparation with advanced preprocessing"""
        print("\n1. Enhanced data preparation...")
        
        # Get features and targets
        all_features = self.feature_info['all_features']
        target_col = 'target_direction'
        
        # Remove rows with missing targets
        self.data_clean = self.data.dropna(subset=[target_col]).copy()
        print(f"Data after removing missing targets: {self.data_clean.shape}")
        
        # Handle missing values and infinite values in features
        X = self.data_clean[all_features].copy()
        y = self.data_clean[target_col].copy()
        
        # Enhanced data cleaning for SPX+VIX features
        print("   🔧 Enhanced data cleaning for SPX+VIX features...")

        # Replace infinite values with NaN first
        X = X.replace([np.inf, -np.inf], np.nan)

        # Cap extreme values before imputation
        for col in X.columns:
            if X[col].dtype in ['float64', 'int64']:
                valid_values = X[col].dropna()
                if len(valid_values) > 10:
                    # Cap at 99th and 1st percentiles to avoid extreme outliers
                    q99 = valid_values.quantile(0.99)
                    q01 = valid_values.quantile(0.01)

                    if not pd.isna(q99) and not pd.isna(q01) and abs(q99) < 1e8 and abs(q01) < 1e8:
                        X[col] = X[col].clip(lower=q01, upper=q99)

        # Use simple median imputation instead of KNN for stability
        print("   🔧 Using median imputation for stability...")

        # Separate numeric and categorical columns
        numeric_cols = X.select_dtypes(include=[np.number]).columns
        X_numeric = X[numeric_cols].copy()

        # Apply median imputation to numeric columns
        for col in numeric_cols:
            median_val = X_numeric[col].median()
            if pd.isna(median_val) or abs(median_val) > 1e8:
                median_val = 0.0
            X_numeric[col] = X_numeric[col].fillna(median_val)

        X_imputed = X_numeric
        
        # Feature quality filtering
        missing_pct = X_imputed.isnull().sum() / len(X_imputed)
        variance_threshold = X_imputed.var()
        
        features_to_keep = []
        for col in X_imputed.columns:
            # Check missing values, variance, and finite values
            if (missing_pct[col] <= 0.3 and 
                variance_threshold[col] > 1e-6 and 
                X_imputed[col].nunique() > 1 and
                np.isfinite(X_imputed[col]).all()):
                features_to_keep.append(col)
        
        self.X = X_imputed[features_to_keep]
        self.y = y
        
        print(f"Features after enhanced cleaning: {self.X.shape[1]}")
        print(f"Target distribution: {self.y.value_counts().to_dict()}")
        
        # Calculate class weights for imbalanced data
        self.class_weights = compute_class_weight(
            'balanced', 
            classes=np.unique(self.y), 
            y=self.y
        )
        self.class_weight_dict = dict(zip(np.unique(self.y), self.class_weights))
        print(f"Class weights: {self.class_weight_dict}")
        
    def create_time_splits(self):
        """Create time-based train/validation/test splits"""
        print("\n2. Creating time-based splits...")
        
        # Use 70% for training, 15% for validation, 15% for testing
        n = len(self.X)
        train_end = int(n * 0.7)
        val_end = int(n * 0.85)
        
        self.X_train = self.X.iloc[:train_end]
        self.X_val = self.X.iloc[train_end:val_end]
        self.X_test = self.X.iloc[val_end:]
        
        self.y_train = self.y.iloc[:train_end]
        self.y_val = self.y.iloc[train_end:val_end]
        self.y_test = self.y.iloc[val_end:]
        
        print(f"Training set: {self.X_train.shape}")
        print(f"Validation set: {self.X_val.shape}")
        print(f"Test set: {self.X_test.shape}")
        
    def advanced_feature_scaling(self):
        """Advanced feature scaling with multiple methods"""
        print("\n3. Advanced feature scaling...")
        
        # Try different scalers and pick the best one
        scalers = {
            'robust': RobustScaler(),
            'standard': StandardScaler(),
            'quantile': QuantileTransformer(output_distribution='normal')
        }
        
        best_scaler = None
        best_score = -1
        
        for scaler_name, scaler in scalers.items():
            # Fit scaler on training data
            X_train_scaled = scaler.fit_transform(self.X_train)
            X_val_scaled = scaler.transform(self.X_val)
            
            # Quick evaluation with logistic regression
            lr = LogisticRegression(random_state=42, max_iter=1000)
            lr.fit(X_train_scaled, self.y_train)
            score = lr.score(X_val_scaled, self.y_val)
            
            if score > best_score:
                best_score = score
                best_scaler = scaler
                
        print(f"Best scaler selected with validation score: {best_score:.4f}")
        
        # Apply best scaler
        self.scaler = best_scaler
        self.X_train_scaled = self.scaler.fit_transform(self.X_train)
        self.X_val_scaled = self.scaler.transform(self.X_val)
        self.X_test_scaled = self.scaler.transform(self.X_test)
        
        # Enhanced safety checks for VIX features
        print("   🔧 Enhanced post-scaling safety checks...")

        # More conservative clipping for stability
        self.X_train_scaled = np.nan_to_num(self.X_train_scaled, nan=0.0, posinf=3.0, neginf=-3.0)
        self.X_val_scaled = np.nan_to_num(self.X_val_scaled, nan=0.0, posinf=3.0, neginf=-3.0)
        self.X_test_scaled = np.nan_to_num(self.X_test_scaled, nan=0.0, posinf=3.0, neginf=-3.0)

        # Additional clipping to ensure values are reasonable
        self.X_train_scaled = np.clip(self.X_train_scaled, -10, 10)
        self.X_val_scaled = np.clip(self.X_val_scaled, -10, 10)
        self.X_test_scaled = np.clip(self.X_test_scaled, -10, 10)

        # Final verification
        for name, data in [("train", self.X_train_scaled), ("val", self.X_val_scaled), ("test", self.X_test_scaled)]:
            inf_count = np.isinf(data).sum()
            nan_count = np.isnan(data).sum()
            if inf_count > 0 or nan_count > 0:
                print(f"   ⚠️  {name}: {inf_count} inf, {nan_count} NaN after safety checks")
            else:
                print(f"   ✅ {name}: Clean data after safety checks")
        
        # Convert back to DataFrames
        self.X_train_scaled = pd.DataFrame(self.X_train_scaled, columns=self.X_train.columns, index=self.X_train.index)
        self.X_val_scaled = pd.DataFrame(self.X_val_scaled, columns=self.X_val.columns, index=self.X_val.index)
        self.X_test_scaled = pd.DataFrame(self.X_test_scaled, columns=self.X_test.columns, index=self.X_test.index)
        
    def advanced_feature_selection(self):
        """Advanced feature selection with multiple methods"""
        print("\n4. Advanced feature selection...")
        
        # Combine multiple feature selection methods
        feature_scores = {}
        
        # 1. Univariate feature selection
        selector_univariate = SelectKBest(score_func=f_classif, k='all')
        selector_univariate.fit(self.X_train_scaled, self.y_train)
        univariate_scores = dict(zip(self.X_train_scaled.columns, selector_univariate.scores_))
        
        # 2. Random Forest feature importance
        rf_selector = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
        rf_selector.fit(self.X_train_scaled, self.y_train)
        rf_scores = dict(zip(self.X_train_scaled.columns, rf_selector.feature_importances_))
        
        # 3. XGBoost feature importance
        xgb_selector = xgb.XGBClassifier(n_estimators=100, random_state=42, eval_metric='logloss')
        xgb_selector.fit(self.X_train_scaled, self.y_train)
        xgb_scores = dict(zip(self.X_train_scaled.columns, xgb_selector.feature_importances_))
        
        # Combine scores (normalized)
        for feature in self.X_train_scaled.columns:
            combined_score = (
                (univariate_scores[feature] / max(univariate_scores.values())) * 0.3 +
                (rf_scores[feature] / max(rf_scores.values())) * 0.35 +
                (xgb_scores[feature] / max(xgb_scores.values())) * 0.35
            )
            feature_scores[feature] = combined_score
        
        # Select top features
        sorted_features = sorted(feature_scores.items(), key=lambda x: x[1], reverse=True)
        n_features = min(40, len(sorted_features))  # Use top 40 features
        self.selected_features = [feat for feat, score in sorted_features[:n_features]]
        
        # Apply feature selection
        self.X_train_selected = self.X_train_scaled[self.selected_features]
        self.X_val_selected = self.X_val_scaled[self.selected_features]
        self.X_test_selected = self.X_test_scaled[self.selected_features]
        
        print(f"Selected {len(self.selected_features)} features")
        print("Top 10 features:")
        for i, (feat, score) in enumerate(sorted_features[:10]):
            print(f"  {i+1}. {feat}: {score:.4f}")
            
    def handle_class_imbalance(self):
        """Handle class imbalance with advanced techniques"""
        print("\n5. Handling class imbalance...")
        
        # Try different sampling strategies
        samplers = {
            'smote': SMOTE(random_state=42),
            'adasyn': ADASYN(random_state=42),
            'smote_tomek': SMOTETomek(random_state=42)
        }
        
        best_sampler = None
        best_score = -1
        
        for sampler_name, sampler in samplers.items():
            try:
                X_resampled, y_resampled = sampler.fit_resample(self.X_train_selected, self.y_train)
                
                # Quick evaluation
                lr = LogisticRegression(random_state=42, max_iter=1000)
                lr.fit(X_resampled, y_resampled)
                score = lr.score(self.X_val_selected, self.y_val)
                
                if score > best_score:
                    best_score = score
                    best_sampler = sampler
                    
            except Exception as e:
                print(f"  {sampler_name} failed: {e}")
                continue
        
        if best_sampler:
            self.X_train_resampled, self.y_train_resampled = best_sampler.fit_resample(
                self.X_train_selected, self.y_train
            )
            print(f"Applied resampling. New training shape: {self.X_train_resampled.shape}")
            print(f"New target distribution: {pd.Series(self.y_train_resampled).value_counts().to_dict()}")
        else:
            self.X_train_resampled = self.X_train_selected
            self.y_train_resampled = self.y_train
            print("No resampling applied - using original data")

    def optimize_hyperparameters(self):
        """Hyperparameter optimization with Optuna"""
        print("\n6. Hyperparameter optimization...")

        def objective(trial):
            # XGBoost hyperparameters
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),
                'reg_lambda': trial.suggest_float('reg_lambda', 0, 10),
                'random_state': 42,
                'eval_metric': 'logloss'
            }

            # Train model
            model = xgb.XGBClassifier(**params)
            model.fit(self.X_train_resampled, self.y_train_resampled)

            # Evaluate on validation set
            val_pred = model.predict(self.X_val_selected)
            return accuracy_score(self.y_val, val_pred)

        # Run optimization
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=20, show_progress_bar=True)

        self.best_params = study.best_params
        print(f"Best hyperparameters: {self.best_params}")
        print(f"Best validation score: {study.best_value:.4f}")

    def train_advanced_models(self):
        """Train advanced models with optimized hyperparameters"""
        print("\n7. Training advanced models...")

        # Enhanced model configurations
        models_config = {
            'xgboost_optimized': xgb.XGBClassifier(
                **self.best_params,
                random_state=42,
                eval_metric='logloss'
            ),
            'lightgbm_advanced': lgb.LGBMClassifier(
                n_estimators=300,
                learning_rate=0.05,
                max_depth=8,
                num_leaves=31,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=0.1,
                random_state=42,
                verbose=-1,
                class_weight='balanced'
            ),
            'catboost': cb.CatBoostClassifier(
                iterations=300,
                learning_rate=0.05,
                depth=8,
                l2_leaf_reg=3,
                random_seed=42,
                verbose=False,
                class_weights=list(self.class_weights)
            ),
            'random_forest_tuned': RandomForestClassifier(
                n_estimators=300,
                max_depth=12,
                min_samples_split=5,
                min_samples_leaf=2,
                max_features='sqrt',
                random_state=42,
                class_weight='balanced',
                n_jobs=-1
            ),
            'logistic_advanced': LogisticRegression(
                C=0.1,
                penalty='elasticnet',
                l1_ratio=0.5,
                solver='saga',
                max_iter=2000,
                random_state=42,
                class_weight='balanced'
            )
        }

        # Train models with cross-validation
        tscv = TimeSeriesSplit(n_splits=5)

        for name, model in models_config.items():
            print(f"Training {name}...")

            try:
                # Cross-validation scores
                cv_scores = cross_val_score(
                    model, self.X_train_resampled, self.y_train_resampled,
                    cv=tscv, scoring='accuracy', n_jobs=-1
                )

                # Train on full training set
                model.fit(self.X_train_resampled, self.y_train_resampled)

                # Predictions
                train_pred = model.predict(self.X_train_selected)
                val_pred = model.predict(self.X_val_selected)
                test_pred = model.predict(self.X_test_selected)

                # Probabilities
                train_proba = model.predict_proba(self.X_train_selected)[:, 1]
                val_proba = model.predict_proba(self.X_val_selected)[:, 1]
                test_proba = model.predict_proba(self.X_test_selected)[:, 1]

                # Comprehensive metrics
                metrics = {
                    'model': model,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'train_accuracy': accuracy_score(self.y_train, train_pred),
                    'val_accuracy': accuracy_score(self.y_val, val_pred),
                    'test_accuracy': accuracy_score(self.y_test, test_pred),
                    'train_auc': roc_auc_score(self.y_train, train_proba),
                    'val_auc': roc_auc_score(self.y_val, val_proba),
                    'test_auc': roc_auc_score(self.y_test, test_proba),
                    'train_f1': f1_score(self.y_train, train_pred),
                    'val_f1': f1_score(self.y_val, val_pred),
                    'test_f1': f1_score(self.y_test, test_pred),
                    'predictions': {
                        'train': train_pred,
                        'val': val_pred,
                        'test': test_pred
                    },
                    'probabilities': {
                        'train': train_proba,
                        'val': val_proba,
                        'test': test_proba
                    }
                }

                self.model_results[name] = metrics

                print(f"  CV Score: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
                print(f"  Val Accuracy: {metrics['val_accuracy']:.4f}")
                print(f"  Test Accuracy: {metrics['test_accuracy']:.4f}")
                print(f"  Test AUC: {metrics['test_auc']:.4f}")
                print(f"  Test F1: {metrics['test_f1']:.4f}")

            except Exception as e:
                print(f"  Error training {name}: {str(e)}")
                continue

    def create_stacking_ensemble(self):
        """Create advanced stacking ensemble"""
        print("\n8. Creating stacking ensemble...")

        if len(self.model_results) < 3:
            print("Not enough models for stacking ensemble")
            return

        # Select best models for stacking
        base_models = []
        for name, results in self.model_results.items():
            if results['val_accuracy'] > 0.5:  # Only include decent models
                base_models.append((name, results['model']))

        if len(base_models) < 2:
            print("Not enough good models for stacking")
            return

        # Create stacking classifier
        meta_learner = LogisticRegression(random_state=42, max_iter=1000)
        stacking_clf = StackingClassifier(
            estimators=base_models[:5],  # Use top 5 models
            final_estimator=meta_learner,
            cv=TimeSeriesSplit(n_splits=3),
            n_jobs=-1
        )

        try:
            # Train stacking ensemble
            stacking_clf.fit(self.X_train_resampled, self.y_train_resampled)

            # Predictions
            val_pred = stacking_clf.predict(self.X_val_selected)
            test_pred = stacking_clf.predict(self.X_test_selected)
            val_proba = stacking_clf.predict_proba(self.X_val_selected)[:, 1]
            test_proba = stacking_clf.predict_proba(self.X_test_selected)[:, 1]

            # Add to results
            self.model_results['stacking_ensemble'] = {
                'model': stacking_clf,
                'val_accuracy': accuracy_score(self.y_val, val_pred),
                'test_accuracy': accuracy_score(self.y_test, test_pred),
                'val_auc': roc_auc_score(self.y_val, val_proba),
                'test_auc': roc_auc_score(self.y_test, test_proba),
                'val_f1': f1_score(self.y_val, val_pred),
                'test_f1': f1_score(self.y_test, test_pred),
                'predictions': {'val': val_pred, 'test': test_pred},
                'probabilities': {'val': val_proba, 'test': test_proba}
            }

            print(f"Stacking Ensemble - Val Acc: {self.model_results['stacking_ensemble']['val_accuracy']:.4f}")
            print(f"Stacking Ensemble - Test Acc: {self.model_results['stacking_ensemble']['test_accuracy']:.4f}")

        except Exception as e:
            print(f"Error creating stacking ensemble: {str(e)}")

    def model_interpretability(self):
        """Generate model interpretability insights"""
        print("\n9. Model interpretability analysis...")

        if not SHAP_AVAILABLE:
            print("SHAP not available - skipping interpretability analysis")
            return

        # Select best model for interpretation
        best_model_name = max(self.model_results.keys(),
                             key=lambda x: self.model_results[x].get('val_accuracy', 0))
        best_model = self.model_results[best_model_name]['model']

        try:
            # Create SHAP explainer
            if 'xgboost' in best_model_name.lower():
                explainer = shap.TreeExplainer(best_model)
            else:
                explainer = shap.Explainer(best_model, self.X_train_selected.sample(100))

            # Calculate SHAP values for test set sample
            test_sample = self.X_test_selected.sample(min(100, len(self.X_test_selected)))
            shap_values = explainer.shap_values(test_sample)

            # Feature importance from SHAP
            if isinstance(shap_values, list):
                shap_values = shap_values[1]  # For binary classification

            feature_importance = np.abs(shap_values).mean(0)
            shap_importance = dict(zip(self.selected_features, feature_importance))

            print("Top 10 SHAP feature importance:")
            sorted_shap = sorted(shap_importance.items(), key=lambda x: x[1], reverse=True)
            for i, (feat, imp) in enumerate(sorted_shap[:10]):
                print(f"  {i+1}. {feat}: {imp:.4f}")

        except Exception as e:
            print(f"Error in SHAP analysis: {str(e)}")

    def select_best_model(self):
        """Select the best model based on comprehensive evaluation"""
        print("\n10. Selecting best model...")

        if not self.model_results:
            print("No models trained successfully")
            return

        # Scoring function combining multiple metrics
        def composite_score(results):
            val_acc = results.get('val_accuracy', 0)
            test_acc = results.get('test_accuracy', 0)
            val_auc = results.get('val_auc', 0.5)
            test_auc = results.get('test_auc', 0.5)
            val_f1 = results.get('val_f1', 0)
            test_f1 = results.get('test_f1', 0)

            # Weighted composite score
            score = (
                val_acc * 0.25 +
                test_acc * 0.25 +
                val_auc * 0.2 +
                test_auc * 0.2 +
                val_f1 * 0.05 +
                test_f1 * 0.05
            )
            return score

        # Find best model
        best_score = -1
        for name, results in self.model_results.items():
            score = composite_score(results)
            if score > best_score:
                best_score = score
                self.best_model_name = name
                self.best_model = results['model']

        print(f"Best model: {self.best_model_name} (composite score: {best_score:.4f})")

    def save_enhanced_results(self):
        """Save enhanced model results and artifacts"""
        print("\n11. Saving enhanced results...")

        if not self.best_model:
            print("No best model to save")
            return

        import pickle

        # Save best model and scaler
        with open(config.get_model_file('best_model'), 'wb') as f:
            pickle.dump(self.best_model, f)

        with open(config.get_model_file('scaler'), 'wb') as f:
            pickle.dump(self.scaler, f)

        # Save selected features
        with open(config.get_data_file('selected_features'), 'w') as f:
            json.dump(self.selected_features, f)

        # Save comprehensive results
        results_summary = {}
        for name, results in self.model_results.items():
            results_summary[name] = {
                'cv_mean': results.get('cv_mean', 0),
                'cv_std': results.get('cv_std', 0),
                'train_accuracy': results.get('train_accuracy', 0),
                'val_accuracy': results.get('val_accuracy', 0),
                'test_accuracy': results.get('test_accuracy', 0),
                'train_auc': results.get('train_auc', 0),
                'val_auc': results.get('val_auc', 0),
                'test_auc': results.get('test_auc', 0),
                'train_f1': results.get('train_f1', 0),
                'val_f1': results.get('val_f1', 0),
                'test_f1': results.get('test_f1', 0)
            }

        with open(config.get_data_file('model_results'), 'w') as f:
            json.dump(results_summary, f, indent=2)

        print(f"Enhanced results saved:")
        print(f"  Best model ({self.best_model_name}): {config.get_model_file('best_model')}")
        print(f"  Scaler: {config.get_model_file('scaler')}")
        print(f"  Selected features: {config.get_data_file('selected_features')}")
        print(f"  Model results: {config.get_data_file('model_results')}")

    def print_final_summary(self):
        """Print comprehensive final summary"""
        print("\n" + "="*80)
        print("ENHANCED MODEL TRAINING SUMMARY")
        print("="*80)

        print(f"{'Model':<25} {'CV Score':<12} {'Val Acc':<10} {'Test Acc':<10} {'Test AUC':<10} {'Test F1':<10}")
        print("-"*80)

        for name, results in self.model_results.items():
            cv_score = f"{results.get('cv_mean', 0):.3f}±{results.get('cv_std', 0):.3f}" if results.get('cv_mean') else "N/A"
            val_acc = f"{results.get('val_accuracy', 0):.3f}"
            test_acc = f"{results.get('test_accuracy', 0):.3f}"
            test_auc = f"{results.get('test_auc', 0):.3f}"
            test_f1 = f"{results.get('test_f1', 0):.3f}"

            marker = "🏆" if name == self.best_model_name else "  "
            print(f"{marker} {name:<23} {cv_score:<12} {val_acc:<10} {test_acc:<10} {test_auc:<10} {test_f1:<10}")

        print("="*80)
        print(f"🏆 BEST MODEL: {self.best_model_name}")
        print("="*80)

# Main execution
if __name__ == "__main__":
    trainer = EnhancedModelTrainer(data, feature_info)

    # Execute enhanced training pipeline
    trainer.prepare_data()
    trainer.create_time_splits()
    trainer.advanced_feature_scaling()
    trainer.advanced_feature_selection()
    trainer.handle_class_imbalance()
    trainer.optimize_hyperparameters()
    trainer.train_advanced_models()
    trainer.create_stacking_ensemble()
    trainer.model_interpretability()
    trainer.select_best_model()
    trainer.save_enhanced_results()
    trainer.print_final_summary()

    print("\n=== Enhanced model development completed ===")
