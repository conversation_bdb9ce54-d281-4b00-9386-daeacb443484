"""
Train the Enhanced Model with Accepted Features
"""

import pandas as pd
import numpy as np
import pickle
import json
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
from config import config

def train_enhanced_model():
    """Train the final enhanced model"""
    print("🎯 TRAINING ENHANCED MODEL")
    print("=" * 60)
    
    # 1. Load enhanced features
    print("1. Loading enhanced features...")
    with open(config.get_data_file('selected_features'), 'r') as f:
        selected_features = json.load(f)
    
    print(f"   ✅ Enhanced features: {len(selected_features)}")
    
    # 2. Prepare data
    print("2. Preparing enhanced data...")
    
    # Load processed data
    data = pd.read_csv('spx_processed_data.csv')
    data['date'] = pd.to_datetime(data['date'])
    
    # Recreate all features (baseline + enhancements)
    # Baseline features
    data['spx_return_1d'] = data['spx_close'].pct_change(1)
    data['spx_return_5d'] = data['spx_close'].pct_change(5)
    data['spx_sma_5'] = data['spx_close'].rolling(5).mean()
    data['spx_sma_20'] = data['spx_close'].rolling(20).mean()
    data['spx_above_sma5'] = (data['spx_close'] > data['spx_sma_5']).astype(int)
    data['spx_above_sma20'] = (data['spx_close'] > data['spx_sma_20']).astype(int)
    data['vix_sma_5'] = data['vix_close'].rolling(5).mean()
    data['vix_sma_20'] = data['vix_close'].rolling(20).mean()
    data['vix_above_20'] = (data['vix_close'] > 20).astype(int)
    data['vix_above_30'] = (data['vix_close'] > 30).astype(int)
    data['volume_sma_5'] = data['total_volume'].rolling(5).mean()
    data['volume_sma_20'] = data['total_volume'].rolling(20).mean()
    data['volume_above_avg'] = (data['total_volume'] > data['volume_sma_20']).astype(int)
    data['gamma_normalized'] = data['total_gamma'] / data['total_gamma'].rolling(20).mean()
    data['put_call_ratio'] = data['put_notional'] / (data['call_notional'] + 1e-6)
    
    # Enhanced features (accepted ones)
    data['gamma_to_volume_ratio'] = data['total_gamma'] / (data['total_volume'] + 1e-6)
    data['oi_to_volume_ratio'] = data['total_open_interest'] / (data['total_volume'] + 1e-6)
    data['notional_concentration'] = (data['call_notional'] + data['put_notional']) / (data['unique_strikes'] + 1)
    
    # Target
    data['target'] = (data['next_day_return'] > 0).astype(int)
    
    print(f"   📊 Data shape: {data.shape}")
    
    # 3. Clean data
    print("3. Cleaning data...")
    
    clean_data = data.dropna(subset=['target'] + selected_features).copy()
    
    X = clean_data[selected_features].copy()
    y = clean_data['target'].copy()
    
    # Handle missing values
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    X = X.replace([np.inf, -np.inf], np.nan)
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    print(f"   📊 Clean data shape: {X.shape}")
    print(f"   🎯 Target distribution: {y.value_counts().to_dict()}")
    print(f"   📈 Target balance: {y.mean():.1%} positive")
    
    # 4. Train/test split (chronological)
    print("4. Creating train/test split...")
    
    split_idx = int(len(X) * 0.8)
    
    X_train = X.iloc[:split_idx].copy()
    X_test = X.iloc[split_idx:].copy()
    y_train = y.iloc[:split_idx].copy()
    y_test = y.iloc[split_idx:].copy()
    
    print(f"   📊 Train: {X_train.shape}, Test: {X_test.shape}")
    
    # 5. Scale features
    print("5. Scaling features...")
    
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 6. Train enhanced model
    print("6. Training enhanced Random Forest...")
    
    model = RandomForestClassifier(
        n_estimators=150,  # Slightly more trees
        max_depth=10,      # Slightly deeper
        min_samples_split=40,
        min_samples_leaf=15,
        random_state=42,
        class_weight='balanced',
        n_jobs=-1
    )
    
    model.fit(X_train_scaled, y_train)
    
    # 7. Evaluate model
    print("7. Evaluating enhanced model...")
    
    train_pred = model.predict(X_train_scaled)
    test_pred = model.predict(X_test_scaled)
    train_proba = model.predict_proba(X_train_scaled)[:, 1]
    test_proba = model.predict_proba(X_test_scaled)[:, 1]
    
    train_acc = accuracy_score(y_train, train_pred)
    test_acc = accuracy_score(y_test, test_pred)
    train_auc = roc_auc_score(y_train, train_proba)
    test_auc = roc_auc_score(y_test, test_proba)
    
    print(f"   📊 Train Accuracy: {train_acc:.1%}")
    print(f"   📊 Test Accuracy: {test_acc:.1%}")
    print(f"   📊 Train AUC: {train_auc:.3f}")
    print(f"   📊 Test AUC: {test_auc:.3f}")
    
    # Check for overfitting
    overfitting = train_acc - test_acc
    if overfitting > 0.15:
        print(f"   ⚠️  Warning: Overfitting detected ({overfitting:.1%})")
    else:
        print(f"   ✅ Overfitting under control ({overfitting:.1%})")
    
    # Feature importance
    feature_importance = pd.DataFrame({
        'feature': selected_features,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"\\n   🎯 Top 10 Features:")
    for i, row in feature_importance.head(10).iterrows():
        print(f"      {row['feature']}: {row['importance']:.3f}")
    
    # 8. Validate predictions are correct
    print("\\n8. Validating prediction direction...")
    
    test_data = clean_data.iloc[split_idx:].copy()
    test_data['prediction'] = test_pred
    test_data['probability'] = test_proba
    
    # Check prediction accuracy by direction
    correct_when_pred_up = test_data[test_data['prediction'] == 1]['target'].mean()
    correct_when_pred_down = test_data[test_data['prediction'] == 0]['target'].mean()
    
    print(f"   📈 When predicted UP: {correct_when_pred_up:.1%} actually went up")
    print(f"   📉 When predicted DOWN: {(1-correct_when_pred_down):.1%} actually went down")
    
    if correct_when_pred_up > 0.5 and correct_when_pred_down < 0.5:
        print("   ✅ Predictions are correctly aligned")
        predictions_correct = True
    else:
        print("   🚨 Predictions may be misaligned!")
        predictions_correct = False
    
    # 9. Save enhanced model
    print("\\n9. Saving enhanced model...")
    
    # Backup existing model
    try:
        with open(config.get_model_file('best_model'), 'rb') as f:
            old_model = pickle.load(f)
        with open(config.get_model_file('best_model').replace('.pkl', '_baseline.pkl'), 'wb') as f:
            pickle.dump(old_model, f)
        print("   📁 Baseline model backed up")
    except:
        print("   📁 No existing model to backup")
    
    # Save enhanced model
    with open(config.get_model_file('best_model'), 'wb') as f:
        pickle.dump(model, f)
    
    with open(config.get_model_file('scaler'), 'wb') as f:
        pickle.dump(scaler, f)
    
    # Save model info
    model_info = {
        'model_type': 'Enhanced RandomForest',
        'n_features': len(selected_features),
        'train_accuracy': train_acc,
        'test_accuracy': test_acc,
        'train_auc': train_auc,
        'test_auc': test_auc,
        'overfitting': overfitting,
        'predictions_correct': predictions_correct,
        'feature_importance': feature_importance.to_dict('records')
    }
    
    with open('enhanced_model_info.json', 'w') as f:
        json.dump(model_info, f, indent=2)
    
    print(f"   ✅ Enhanced model saved")
    print(f"   ✅ Enhanced scaler saved")
    print(f"   ✅ Model info saved")
    
    print("\\n✅ ENHANCED MODEL TRAINING COMPLETED!")
    print(f"   🎯 Test Accuracy: {test_acc:.1%}")
    print(f"   📊 Test AUC: {test_auc:.3f}")
    print(f"   🔧 Features: {len(selected_features)}")
    print(f"   🚀 Ready for enhanced backtesting")
    
    return predictions_correct and test_acc > 0.5

if __name__ == "__main__":
    success = train_enhanced_model()
    if success:
        print("\\n🎉 Enhanced model ready for backtesting!")
    else:
        print("\\n⚠️ Enhanced model needs review before backtesting")
