"""
Simple Baseline Backtesting
Test the baseline system to see if predictions are working correctly
"""

import pandas as pd
import numpy as np
import pickle
import json
from config import config

def run_baseline_backtest():
    """Run a simple backtest to validate the baseline system"""
    print("📈 BASELINE BACKTESTING")
    print("=" * 60)
    
    # 1. Load data and model
    print("1. Loading data and model...")
    
    # Load processed data
    data = pd.read_csv('spx_processed_data.csv')
    data['date'] = pd.to_datetime(data['date'])
    
    # Recreate the same features as in baseline
    data['spx_return_1d'] = data['spx_close'].pct_change(1)
    data['spx_return_5d'] = data['spx_close'].pct_change(5)
    data['spx_sma_5'] = data['spx_close'].rolling(5).mean()
    data['spx_sma_20'] = data['spx_close'].rolling(20).mean()
    data['spx_above_sma5'] = (data['spx_close'] > data['spx_sma_5']).astype(int)
    data['spx_above_sma20'] = (data['spx_close'] > data['spx_sma_20']).astype(int)
    data['vix_sma_5'] = data['vix_close'].rolling(5).mean()
    data['vix_sma_20'] = data['vix_close'].rolling(20).mean()
    data['vix_above_20'] = (data['vix_close'] > 20).astype(int)
    data['vix_above_30'] = (data['vix_close'] > 30).astype(int)
    data['volume_sma_5'] = data['total_volume'].rolling(5).mean()
    data['volume_sma_20'] = data['total_volume'].rolling(20).mean()
    data['volume_above_avg'] = (data['total_volume'] > data['volume_sma_20']).astype(int)
    data['gamma_normalized'] = data['total_gamma'] / data['total_gamma'].rolling(20).mean()
    data['put_call_ratio'] = data['put_notional'] / (data['call_notional'] + 1e-6)
    data['target'] = (data['next_day_return'] > 0).astype(int)
    
    # Load model and features
    with open(config.get_model_file('best_model'), 'rb') as f:
        model = pickle.load(f)
    
    with open(config.get_model_file('scaler'), 'rb') as f:
        scaler = pickle.load(f)
    
    with open(config.get_data_file('selected_features'), 'r') as f:
        selected_features = json.load(f)
    
    print(f"   ✅ Loaded model and {len(selected_features)} features")
    
    # 2. Prepare data
    print("2. Preparing data...")
    
    # Clean data
    clean_data = data.dropna(subset=['target'] + selected_features).copy()
    
    X = clean_data[selected_features].copy()
    y = clean_data['target'].copy()
    
    # Handle missing values
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    X = X.replace([np.inf, -np.inf], np.nan)
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    print(f"   📊 Clean data shape: {X.shape}")
    
    # 3. Generate predictions
    print("3. Generating predictions...")
    
    X_scaled = scaler.transform(X)
    predictions = model.predict(X_scaled)
    probabilities = model.predict_proba(X_scaled)[:, 1]
    
    # Add predictions to data
    clean_data['prediction'] = predictions
    clean_data['probability'] = probabilities
    clean_data['actual_direction'] = y
    
    print(f"   ✅ Generated {len(predictions)} predictions")
    
    # 4. Analyze prediction accuracy
    print("4. Analyzing prediction accuracy...")
    
    accuracy = (predictions == y).mean()
    print(f"   📊 Overall Accuracy: {accuracy:.1%}")
    
    # Check by prediction
    correct_when_pred_up = clean_data[clean_data['prediction'] == 1]['actual_direction'].mean()
    correct_when_pred_down = clean_data[clean_data['prediction'] == 0]['actual_direction'].mean()
    
    print(f"   📈 When predicted UP: {correct_when_pred_up:.1%} actually went up")
    print(f"   📉 When predicted DOWN: {(1-correct_when_pred_down):.1%} actually went down")
    
    # Check if predictions are inverted
    if correct_when_pred_up < 0.5 and correct_when_pred_down > 0.5:
        print("   🚨 PREDICTIONS ARE INVERTED!")
        invert_predictions = True
    else:
        print("   ✅ Predictions appear correctly aligned")
        invert_predictions = False
    
    # 5. Simple trading simulation
    print("5. Running simple trading simulation...")
    
    # Use last 500 days for trading simulation
    trading_data = clean_data.tail(500).copy()
    
    if invert_predictions:
        print("   🔄 Inverting predictions for trading...")
        trading_data['trading_signal'] = 1 - trading_data['prediction']
    else:
        trading_data['trading_signal'] = trading_data['prediction']
    
    # Simple strategy: trade when confident
    confidence_threshold = 0.6
    confident_trades = trading_data[
        (trading_data['probability'] > confidence_threshold) | 
        (trading_data['probability'] < (1 - confidence_threshold))
    ].copy()
    
    if len(confident_trades) == 0:
        print("   ⚠️  No confident trades found")
        return
    
    # Calculate returns
    confident_trades['trade_return'] = np.where(
        confident_trades['trading_signal'] == 1,
        confident_trades['next_day_return'],  # Long trade
        -confident_trades['next_day_return']  # Short trade
    )
    
    # Results
    total_trades = len(confident_trades)
    win_rate = (confident_trades['trade_return'] > 0).mean()
    avg_return = confident_trades['trade_return'].mean()
    total_return = confident_trades['trade_return'].sum()
    
    print(f"   📊 Trading Results:")
    print(f"      Total Trades: {total_trades}")
    print(f"      Win Rate: {win_rate:.1%}")
    print(f"      Average Return: {avg_return:.3f}%")
    print(f"      Total Return: {total_return:.2f}%")
    
    # 6. Check correlation
    print("6. Checking prediction-return correlation...")
    
    # Calculate correlation between probability and actual returns
    correlation = np.corrcoef(trading_data['probability'], trading_data['next_day_return'])[0, 1]
    
    if invert_predictions:
        # If we inverted, check inverted correlation
        inverted_prob = 1 - trading_data['probability']
        correlation = np.corrcoef(inverted_prob, trading_data['next_day_return'])[0, 1]
    
    print(f"   📊 Probability-Return Correlation: {correlation:.3f}")
    
    if abs(correlation) > 0.1:
        print("   ✅ Reasonable correlation found")
    else:
        print("   ⚠️  Weak correlation - model may need improvement")
    
    print("\\n✅ BASELINE BACKTEST COMPLETED!")
    
    if win_rate > 0.52 and total_return > 0:
        print("   🎉 Baseline system shows promise!")
        return True
    else:
        print("   ⚠️  Baseline system needs improvement")
        return False

if __name__ == "__main__":
    success = run_baseline_backtest()
    if success:
        print("\\n🚀 Ready to enhance the baseline system!")
    else:
        print("\\n🔧 Need to fix baseline issues first")
