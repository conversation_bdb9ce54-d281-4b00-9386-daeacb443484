#!/usr/bin/env python3
"""
Display the directory structure of the SPX Trading System
"""

import os
from pathlib import Path

def show_directory_structure():
    """Display the organized directory structure"""
    print("🏗️ SPX TRADING SYSTEM - DIRECTORY STRUCTURE")
    print("=" * 60)
    
    root = Path(".")
    
    # Main files
    print("📁 Root Directory:")
    main_files = ["main.py", "README.md", "activate_env.sh"]
    for file in main_files:
        if (root / file).exists():
            print(f"   📄 {file}")
    
    # Documentation
    print("\n📚 Documentation:")
    doc_files = [f for f in os.listdir(".") if f.endswith(".md") and f != "README.md"]
    for file in sorted(doc_files):
        print(f"   📄 {file}")
    
    # Source directory
    print("\n📂 src/ (Source Code):")
    if (root / "src").exists():
        src_files = sorted([f for f in os.listdir("src") if f.endswith(".py")])
        key_files = [
            "main_complete_system.py",
            "enhanced_data_loader.py", 
            "data_loader.py",
            "backtesting.py",
            "model_development.py"
        ]
        
        # Show key files first
        for file in key_files:
            if file in src_files:
                print(f"   📄 {file}")
                src_files.remove(file)
        
        # Show remaining files
        if src_files:
            print(f"   📄 ... and {len(src_files)} other Python files")
    
    # Config directory
    print("\n📂 cfg/ (Configuration):")
    if (root / "cfg").exists():
        cfg_files = sorted(os.listdir("cfg"))
        for file in cfg_files:
            if not file.startswith("__"):
                print(f"   📄 {file}")
    
    # Output directory
    print("\n📂 output/ (Generated Files):")
    if (root / "output").exists():
        output_files = sorted([f for f in os.listdir("output") if not f.startswith("__")])
        key_outputs = [
            "spx_complete_report.html",
            "spx_equity_curve.png",
            "spx_trades.csv",
            "spx_results.json"
        ]
        
        # Show key outputs first
        for file in key_outputs:
            if file in output_files:
                print(f"   📄 {file}")
                output_files.remove(file)
        
        # Show remaining files by type
        html_files = [f for f in output_files if f.endswith(".html")]
        png_files = [f for f in output_files if f.endswith(".png")]
        csv_files = [f for f in output_files if f.endswith(".csv")]
        json_files = [f for f in output_files if f.endswith(".json")]
        pkl_files = [f for f in output_files if f.endswith(".pkl")]
        
        if html_files:
            print(f"   📊 {len(html_files)} additional HTML reports")
        if png_files:
            print(f"   📈 {len(png_files)} additional charts")
        if csv_files:
            print(f"   💾 {len(csv_files)} additional data files")
        if json_files:
            print(f"   📋 {len(json_files)} additional result files")
        if pkl_files:
            print(f"   🤖 {len(pkl_files)} model files")
    
    print("\n" + "=" * 60)
    print("🚀 USAGE:")
    print("   python main.py                    # Run complete system")
    print("   python src/main_complete_system.py # Run from src directly")
    print("   open output/spx_complete_report.html # View latest report")
    print("=" * 60)

if __name__ == "__main__":
    show_directory_structure()
