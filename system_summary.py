"""
Display System Summary from Main Program Results
"""

import json
import pandas as pd

def display_system_summary():
    """Display comprehensive system summary"""
    print("🎉 ROBUST SPX SYSTEM - EXECUTION SUMMARY")
    print("=" * 60)
    
    # Load results
    with open('system_results.json', 'r') as f:
        results = json.load(f)
    
    trades_df = pd.read_csv('system_trades.csv')
    trades_df['date'] = pd.to_datetime(trades_df['date'])
    
    # Current prediction
    current = results['current_prediction']
    
    print(f"📅 CURRENT MARKET STATUS ({current['date'][:10]}):")
    print(f"   📈 SPX: ${current['spx_close']:,.2f}")
    print(f"   📊 VIX: {current['vix_close']:.2f}")
    print(f"   🎯 Prediction: {current['prediction']}")
    print(f"   📊 Confidence: {current['confidence']:.1%}")
    print(f"   🚦 Trade Signal: {current['trade_signal']}")
    
    print(f"\n🏆 OVERALL SYSTEM PERFORMANCE:")
    print(f"   📊 Total Trades: {results['total_trades']}")
    print(f"   🎯 Win Rate: {results['win_rate']:.1%}")
    print(f"   💰 Total Return: {results['total_return']:.1f}%")
    print(f"   📅 Monthly Win Rate: {results['monthly_win_rate']:.1%}")
    print(f"   📈 Avg Return/Trade: {results['avg_return_per_trade']:.3f}%")
    print(f"   📊 Prediction Accuracy: {results['prediction_accuracy']:.1%}")
    
    # Last 5 trades
    last_5 = trades_df.tail(5)
    
    print(f"\n📋 LAST 5 TRADES:")
    print("=" * 70)
    for i, (_, trade) in enumerate(last_5.iterrows(), 1):
        status = "✅ WIN" if trade['win'] else "❌ LOSS"
        direction_emoji = "📈" if trade['direction'] == 'long' else "📉"
        
        print(f"{i}. {trade['date'].strftime('%Y-%m-%d')} | {direction_emoji} {trade['direction'].upper()}")
        print(f"   Entry: ${trade['entry_price']:,.2f} | Return: {trade['return_pct']:+.3f}% | {status}")
        print(f"   Confidence: {trade['confidence']:.1%}")
        print()
    
    # Last 5 summary
    last_5_wins = last_5['win'].sum()
    last_5_return = last_5['return_pct'].sum()
    
    print(f"📊 LAST 5 TRADES SUMMARY:")
    print(f"   🏆 Win Rate: {last_5['win'].mean():.1%} ({last_5_wins}/5)")
    print(f"   💰 Total Return: {last_5_return:+.2f}%")
    print(f"   📈 Average Return: {last_5['return_pct'].mean():+.3f}%")
    
    # Trading recommendation
    print(f"\n🚨 TRADING RECOMMENDATION:")
    if current['trade_signal'] != 'NO TRADE':
        print(f"   🎯 ACTION: {current['trade_signal']}")
        print(f"   📊 Confidence: {current['confidence']:.1%}")
        if current['trade_signal'] == 'LONG':
            print(f"   📈 Strategy: Buy SPX calls or go long")
        else:
            print(f"   📉 Strategy: Buy SPX puts or go short")
    else:
        print(f"   ⏸️  WAIT: Confidence {current['confidence']:.1%} below threshold")
        print(f"   📊 Need confidence > 55% or < 45% to trade")
    
    print(f"\n✅ FILES GENERATED:")
    print(f"   📊 Complete Report: robust_system_complete_report.html")
    print(f"   📈 Equity Curve: equity_curve.png")
    print(f"   💾 Trade Data: system_trades.csv")
    print(f"   📋 Results: system_results.json")
    
    print(f"\n🎉 SYSTEM STATUS: FULLY OPERATIONAL")
    print(f"📊 Ready for live trading analysis!")

if __name__ == "__main__":
    display_system_summary()
