"""
Robust Backtesting for the Working Baseline System
"""

import pandas as pd
import numpy as np
import pickle
import json
from config import config

def run_robust_backtest():
    """Run comprehensive backtest on the robust baseline"""
    print("📈 ROBUST BASELINE BACKTESTING")
    print("=" * 60)
    
    # 1. Load data and model
    print("1. Loading data and model...")
    
    # Load processed data
    data = pd.read_csv('spx_processed_data.csv')
    data['date'] = pd.to_datetime(data['date'])
    
    # Recreate features
    data['spx_return_1d'] = data['spx_close'].pct_change(1)
    data['spx_return_5d'] = data['spx_close'].pct_change(5)
    data['spx_sma_5'] = data['spx_close'].rolling(5).mean()
    data['spx_sma_20'] = data['spx_close'].rolling(20).mean()
    data['spx_above_sma5'] = (data['spx_close'] > data['spx_sma_5']).astype(int)
    data['spx_above_sma20'] = (data['spx_close'] > data['spx_sma_20']).astype(int)
    data['vix_sma_5'] = data['vix_close'].rolling(5).mean()
    data['vix_sma_20'] = data['vix_close'].rolling(20).mean()
    data['vix_above_20'] = (data['vix_close'] > 20).astype(int)
    data['vix_above_30'] = (data['vix_close'] > 30).astype(int)
    data['volume_sma_5'] = data['total_volume'].rolling(5).mean()
    data['volume_sma_20'] = data['total_volume'].rolling(20).mean()
    data['volume_above_avg'] = (data['total_volume'] > data['volume_sma_20']).astype(int)
    data['gamma_normalized'] = data['total_gamma'] / data['total_gamma'].rolling(20).mean()
    data['put_call_ratio'] = data['put_notional'] / (data['call_notional'] + 1e-6)
    data['target'] = (data['next_day_return'] > 0).astype(int)
    
    # Load model
    with open(config.get_model_file('best_model'), 'rb') as f:
        model = pickle.load(f)
    
    with open(config.get_model_file('scaler'), 'rb') as f:
        scaler = pickle.load(f)
    
    with open(config.get_data_file('selected_features'), 'r') as f:
        selected_features = json.load(f)
    
    print(f"   ✅ Loaded model with {len(selected_features)} features")
    
    # 2. Prepare data
    print("2. Preparing data...")
    
    clean_data = data.dropna(subset=['target'] + selected_features).copy()
    X = clean_data[selected_features].copy()
    
    # Handle missing values
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    X = X.replace([np.inf, -np.inf], np.nan)
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    print(f"   📊 Clean data shape: {X.shape}")
    
    # 3. Generate predictions
    print("3. Generating predictions...")
    
    X_scaled = scaler.transform(X)
    predictions = model.predict(X_scaled)
    probabilities = model.predict_proba(X_scaled)[:, 1]
    
    # Add to data
    clean_data['prediction'] = predictions
    clean_data['probability'] = probabilities
    
    print(f"   ✅ Generated {len(predictions)} predictions")
    
    # 4. Simple trading strategy
    print("4. Running trading strategy...")
    
    # Use last 800 days for trading (about 3 years)
    trading_data = clean_data.tail(800).copy()
    
    # Strategy parameters
    confidence_thresholds = [0.45, 0.5, 0.55, 0.6]
    
    best_strategy = None
    best_return = -999
    
    for threshold in confidence_thresholds:
        print(f"\\n   Testing confidence threshold: {threshold}")
        
        # Select confident trades
        confident_long = trading_data[trading_data['probability'] > threshold].copy()
        confident_short = trading_data[trading_data['probability'] < (1 - threshold)].copy()
        
        trades = []
        
        # Long trades
        for _, row in confident_long.iterrows():
            trade_return = row['next_day_return']
            trades.append({
                'direction': 'long',
                'return': trade_return,
                'confidence': row['probability'],
                'date': row['date']
            })
        
        # Short trades
        for _, row in confident_short.iterrows():
            trade_return = -row['next_day_return']  # Inverse for short
            trades.append({
                'direction': 'short',
                'return': trade_return,
                'confidence': 1 - row['probability'],
                'date': row['date']
            })
        
        if len(trades) == 0:
            print(f"      No trades found")
            continue
        
        # Calculate performance
        trades_df = pd.DataFrame(trades)
        total_trades = len(trades_df)
        win_rate = (trades_df['return'] > 0).mean()
        avg_return = trades_df['return'].mean()
        total_return = trades_df['return'].sum()
        
        print(f"      Trades: {total_trades}")
        print(f"      Win Rate: {win_rate:.1%}")
        print(f"      Avg Return: {avg_return:.3f}%")
        print(f"      Total Return: {total_return:.2f}%")
        
        # Track best strategy
        if total_return > best_return and total_trades > 20:
            best_return = total_return
            best_strategy = {
                'threshold': threshold,
                'trades': total_trades,
                'win_rate': win_rate,
                'avg_return': avg_return,
                'total_return': total_return,
                'trades_df': trades_df
            }
    
    # 5. Results
    print("\\n5. Best Strategy Results:")
    print("=" * 40)
    
    if best_strategy:
        print(f"   🎯 Best Threshold: {best_strategy['threshold']}")
        print(f"   📊 Total Trades: {best_strategy['trades']}")
        print(f"   🏆 Win Rate: {best_strategy['win_rate']:.1%}")
        print(f"   💰 Average Return: {best_strategy['avg_return']:.3f}%")
        print(f"   📈 Total Return: {best_strategy['total_return']:.2f}%")
        
        # Monthly analysis
        trades_df = best_strategy['trades_df']
        trades_df['month'] = pd.to_datetime(trades_df['date']).dt.to_period('M')
        monthly_returns = trades_df.groupby('month')['return'].sum()
        
        positive_months = (monthly_returns > 0).sum()
        total_months = len(monthly_returns)
        monthly_win_rate = positive_months / total_months if total_months > 0 else 0
        
        print(f"   📅 Monthly Win Rate: {monthly_win_rate:.1%} ({positive_months}/{total_months})")
        print(f"   📊 Best Month: {monthly_returns.max():.2f}%")
        print(f"   📉 Worst Month: {monthly_returns.min():.2f}%")
        
        # Save results
        results = {
            'strategy': 'Robust Baseline',
            'confidence_threshold': best_strategy['threshold'],
            'total_trades': best_strategy['trades'],
            'win_rate': best_strategy['win_rate'],
            'avg_return_per_trade': best_strategy['avg_return'],
            'total_return': best_strategy['total_return'],
            'monthly_win_rate': monthly_win_rate,
            'best_month': monthly_returns.max(),
            'worst_month': monthly_returns.min()
        }
        
        with open('robust_backtest_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        # Save trades
        trades_df.to_csv('robust_trades.csv', index=False)
        
        print(f"\\n   ✅ Results saved to robust_backtest_results.json")
        print(f"   ✅ Trades saved to robust_trades.csv")
        
        # Success criteria
        if (best_strategy['win_rate'] > 0.52 and 
            best_strategy['total_return'] > 5 and 
            best_strategy['trades'] > 50):
            print("\\n🎉 ROBUST BASELINE SYSTEM IS SUCCESSFUL!")
            return True
        else:
            print("\\n⚠️ Baseline system shows promise but needs optimization")
            return False
    else:
        print("   ❌ No profitable strategy found")
        return False

if __name__ == "__main__":
    success = run_robust_backtest()
    if success:
        print("\\n🚀 Ready to add conservative enhancements!")
    else:
        print("\\n🔧 Need to optimize baseline further")
