# SPX Trading System

A robust machine learning-based trading system for S&P 500 (SPX) options with VIX integration.

## 🏗️ Directory Structure

```
SPX_Predictive_System/
├── main.py                     # Main entry point
├── activate_env.sh            # Environment activation script
├── README.md                  # This file
├── todo.md                    # Development tasks
├── *.md                       # Documentation files
│
├── src/                       # 📁 Source Code
│   ├── main_complete_system.py    # Complete system pipeline
│   ├── enhanced_data_loader.py    # SPX + VIX data loading
│   ├── data_loader.py             # Base data loader
│   ├── backtesting*.py            # Backtesting modules
│   ├── model_development*.py      # Model training
│   ├── feature_engineering.py     # Feature creation
│   ├── create_*.py                # Report generators
│   └── test_*.py                  # Test scripts
│
├── cfg/                       # ⚙️ Configuration
│   ├── config.py                  # System configuration
│   └── requirements.txt           # Python dependencies
│
└── output/                    # 📊 Generated Files
    ├── spx_complete_report.html   # Main HTML report
    ├── spx_equity_curve.png       # Performance chart
    ├── spx_trades.csv             # Trade history
    ├── spx_results.json           # System metrics
    ├── *.pkl                      # Trained models
    └── *.csv                      # Processed data
```

## 🚀 Quick Start

### 1. Setup Environment
```bash
# Activate virtual environment
source activate_env.sh

# Install dependencies (if needed)
pip install -r cfg/requirements.txt
```

### 2. Run Complete System
```bash
# Run the complete trading system
python main.py
```

This will:
- ✅ Load SPX + VIX options data (2020-2025)
- ✅ Train robust ML model
- ✅ Run trading simulation
- ✅ Generate current market prediction
- ✅ Create comprehensive HTML report

### 3. View Results
- **📊 HTML Report**: `output/spx_complete_report.html`
- **📈 Equity Curve**: `output/spx_equity_curve.png`
- **💾 Trade Data**: `output/spx_trades.csv`
- **📋 System Metrics**: `output/spx_results.json`

## 📊 System Performance

- **🎯 Win Rate**: ~70%
- **💰 Total Return**: ~94%
- **📅 Monthly Win Rate**: ~77%
- **📊 Total Trades**: 346
- **🎯 Prediction Accuracy**: ~66%

## 🔧 Key Features

### Data Integration
- **SPX Options**: S&P 500 index options data
- **VIX Integration**: Volatility index for enhanced predictions
- **Multi-Year Data**: 2020-2025 historical data
- **Quarterly Updates**: Automated data loading from quarterly files

### Machine Learning
- **Random Forest**: Conservative, robust model
- **15 Features**: Carefully selected predictive features
- **Time Series CV**: Proper temporal validation
- **Risk Management**: Confidence-based trading thresholds

### Trading Strategy
- **Long/Short**: Directional predictions
- **Confidence Thresholds**: 52%/48% for trade signals
- **Risk Control**: Conservative position sizing
- **Performance Tracking**: Comprehensive trade analysis

### Reporting
- **HTML Reports**: Professional styling with charts
- **Real-time Signals**: Current market predictions
- **Trade History**: Last 5 trades with details
- **Performance Metrics**: Win rates, returns, accuracy

## 🛠️ Development

### Running Individual Components

```bash
# Data loading and exploration
python src/enhanced_data_loader.py
python src/data_exploration.py

# Model development
python src/model_development.py
python src/feature_engineering.py

# Backtesting
python src/backtesting.py
python src/baseline_backtest.py

# Report generation
python src/create_html_report.py
python src/system_summary.py
```

### Configuration

Edit `cfg/config.py` to modify:
- Data directory paths
- Model parameters
- Trading thresholds
- Output settings

### Adding New Features

1. Add feature engineering in `src/feature_engineering.py`
2. Update model in `src/model_development.py`
3. Test with `src/test_*.py` scripts
4. Run complete system with `python main.py`

## 📈 Current Market Status

The system provides real-time market analysis including:
- **Current SPX Level**: Latest S&P 500 price
- **VIX Level**: Market volatility indicator
- **Prediction**: UP/DOWN direction forecast
- **Confidence**: Model confidence percentage
- **Trade Signal**: LONG/SHORT/NO TRADE recommendation

## 🎯 Trading Signals

- **LONG**: Probability > 52% (bullish signal)
- **SHORT**: Probability < 48% (bearish signal)  
- **NO TRADE**: 48% ≤ Probability ≤ 52% (neutral)

## 📊 Performance Monitoring

The system tracks:
- **Trade-by-trade results**: Individual trade performance
- **Monthly performance**: Consistent profitability
- **Model accuracy**: Prediction quality over time
- **Risk metrics**: Drawdowns and volatility

## 🔄 Updates

The system automatically:
- Loads latest quarterly data
- Retrains models with new data
- Updates predictions daily
- Generates fresh reports

---

**Built with robust machine learning and conservative risk management for consistent SPX trading performance.**
