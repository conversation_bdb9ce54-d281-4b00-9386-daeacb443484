import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import IsolationForest
import warnings
warnings.filterwarnings('ignore')

# Import configuration
from config import config

print("=== SPX Option Feature Engineering ===")

# Load processed data
data = pd.read_csv(config.get_data_file('processed_data'))
data['date'] = pd.to_datetime(data['date'])
data = data.sort_values('date').reset_index(drop=True)

print(f"Starting with {len(data)} observations")
print(f"Date range: {data['date'].min().date()} to {data['date'].max().date()}")

# 1. Create lagged features (looking back 1-5 days)
print("\n1. Creating lagged features...")
lag_features = ['spx_return', 'spx_range_pct', 'put_call_oi_ratio', 'put_call_notional_ratio',
               'total_gamma', 'total_vega', 'total_volume', 'spx_to_put_wall', 'spx_to_call_wall']

for feature in lag_features:
    for lag in range(1, 6):  # 1-5 day lags
        data[f'{feature}_lag{lag}'] = data[feature].shift(lag)

# 2. Create rolling statistics (3, 5, 10 day windows)
print("2. Creating rolling statistics...")
rolling_features = ['spx_return', 'spx_range_pct', 'put_call_oi_ratio', 'total_gamma', 'total_volume']
windows = [3, 5, 10]

for feature in rolling_features:
    for window in windows:
        data[f'{feature}_ma{window}'] = data[feature].rolling(window=window).mean()
        data[f'{feature}_std{window}'] = data[feature].rolling(window=window).std()
        data[f'{feature}_min{window}'] = data[feature].rolling(window=window).min()
        data[f'{feature}_max{window}'] = data[feature].rolling(window=window).max()

# 3. Create momentum and trend features
print("3. Creating momentum and trend features...")
# Price momentum
data['spx_momentum_3d'] = data['spx_close'] / data['spx_close'].shift(3) - 1
data['spx_momentum_5d'] = data['spx_close'] / data['spx_close'].shift(5) - 1
data['spx_momentum_10d'] = data['spx_close'] / data['spx_close'].shift(10) - 1

# Return momentum
data['return_momentum_3d'] = data['spx_return'].rolling(3).sum()
data['return_momentum_5d'] = data['spx_return'].rolling(5).sum()

# Volatility features
data['realized_vol_5d'] = data['spx_return'].rolling(5).std() * np.sqrt(252)
data['realized_vol_10d'] = data['spx_return'].rolling(10).std() * np.sqrt(252)

# 4. Create options-specific advanced features
print("4. Creating options-specific features...")
# Gamma exposure changes
data['gamma_change_1d'] = data['total_gamma'].diff()
data['gamma_change_3d'] = data['total_gamma'] - data['total_gamma'].shift(3)

# Put/call ratio changes
data['pc_ratio_change_1d'] = data['put_call_oi_ratio'].diff()
data['pc_ratio_change_3d'] = data['put_call_oi_ratio'] - data['put_call_oi_ratio'].shift(3)

# Volume surge indicators
data['volume_surge_3d'] = data['total_volume'] / data['total_volume'].rolling(3).mean()
data['volume_surge_5d'] = data['total_volume'] / data['total_volume'].rolling(5).mean()

# Wall distance changes
data['put_wall_distance_change'] = data['spx_to_put_wall'].diff()
data['call_wall_distance_change'] = data['spx_to_call_wall'].diff()

# 5. Create interaction features (nonlinear relationships)
print("5. Creating interaction features...")
# Gamma * Put/Call ratio interactions
data['gamma_pc_interaction'] = data['total_gamma'] * data['put_call_oi_ratio']
data['gamma_vol_interaction'] = data['total_gamma'] * data['total_volume']

# Wall distance interactions
data['wall_squeeze'] = 1 / (data['spx_to_put_wall'] + data['spx_to_call_wall'] + 1)
data['wall_asymmetry'] = (data['spx_to_call_wall'] - data['spx_to_put_wall']) / (data['spx_to_call_wall'] + data['spx_to_put_wall'] + 1)

# Volatility * Options flow
data['vol_gamma_interaction'] = data['realized_vol_5d'] * data['total_gamma']
data['vol_pc_interaction'] = data['realized_vol_5d'] * data['put_call_oi_ratio']

# 6. Create warped/transformed features
print("6. Creating warped features...")
# Log transforms for skewed features
data['log_total_volume'] = np.log1p(data['total_volume'])
data['log_total_oi'] = np.log1p(data['total_open_interest'])

# Square root transforms
data['sqrt_range'] = np.sqrt(data['spx_range_pct'])

# Rank transforms (to capture nonlinear relationships)
for feature in ['put_call_oi_ratio', 'total_gamma', 'total_volume']:
    data[f'{feature}_rank'] = data[feature].rolling(20).rank() / 20

# 7. Create regime indicators
print("7. Creating regime indicators...")
# Volatility regime
vol_threshold = data['realized_vol_5d'].quantile(0.7)
data['high_vol_regime'] = (data['realized_vol_5d'] > vol_threshold).astype(int)

# Trend regime
data['uptrend_regime'] = (data['spx_momentum_5d'] > 0).astype(int)

# Options activity regime
vol_activity_threshold = data['total_volume'].quantile(0.7)
data['high_options_activity'] = (data['total_volume'] > vol_activity_threshold).astype(int)

# 8. Create forward-shifted features (for prediction targets)
print("8. Creating prediction targets...")
# Next day direction (primary target)
data['target_direction'] = (data['spx_return'].shift(-1) > 0).astype(int)

# Next day return magnitude
data['target_return'] = data['spx_return'].shift(-1)

# Next day range (volatility prediction)
data['target_range'] = data['spx_range_pct'].shift(-1)

# Multi-day targets
data['target_direction_2d'] = (data['spx_return'].rolling(2).sum().shift(-2) > 0).astype(int)
data['target_return_2d'] = data['spx_return'].rolling(2).sum().shift(-2)

# 9. Handle outliers using Isolation Forest
print("9. Detecting outliers...")
# Select key features for outlier detection
outlier_features = ['total_gamma', 'total_volume', 'put_call_oi_ratio', 'spx_range_pct']
outlier_data = data[outlier_features].copy()

# Handle infinite values
outlier_data = outlier_data.replace([np.inf, -np.inf], np.nan)
outlier_data = outlier_data.fillna(outlier_data.median())

# Fit Isolation Forest
iso_forest = IsolationForest(contamination=0.1, random_state=42)
outlier_scores = iso_forest.fit_predict(outlier_data)
data['is_outlier'] = (outlier_scores == -1).astype(int)

print(f"Detected {sum(data['is_outlier'])} outliers ({sum(data['is_outlier'])/len(data)*100:.1f}%)")

# 10. Create feature lists for modeling
print("10. Organizing features for modeling...")

# Base features
base_features = ['spx_return', 'spx_range_pct', 'put_call_oi_ratio', 'put_call_notional_ratio',
                'total_gamma', 'total_vega', 'total_volume', 'spx_to_put_wall', 'spx_to_call_wall']

# Lagged features
lagged_features = [col for col in data.columns if '_lag' in col]

# Rolling features
rolling_features = [col for col in data.columns if any(x in col for x in ['_ma', '_std', '_min', '_max'])]

# Momentum features
momentum_features = [col for col in data.columns if 'momentum' in col or 'vol_' in col]

# Options features
options_features = [col for col in data.columns if any(x in col for x in ['gamma_', 'pc_', 'volume_surge', 'wall_'])]

# Interaction features
interaction_features = [col for col in data.columns if 'interaction' in col or col in ['wall_squeeze', 'wall_asymmetry']]

# Warped features
warped_features = [col for col in data.columns if any(x in col for x in ['log_', 'sqrt_', '_rank'])]

# Regime features
regime_features = [col for col in data.columns if 'regime' in col or 'high_' in col]

# All predictive features (excluding targets and metadata)
all_features = (base_features + lagged_features + rolling_features + momentum_features + 
               options_features + interaction_features + warped_features + regime_features)

# Remove duplicates
all_features = list(set(all_features))

print(f"\nFeature summary:")
print(f"Base features: {len(base_features)}")
print(f"Lagged features: {len(lagged_features)}")
print(f"Rolling features: {len(rolling_features)}")
print(f"Momentum features: {len(momentum_features)}")
print(f"Options features: {len(options_features)}")
print(f"Interaction features: {len(interaction_features)}")
print(f"Warped features: {len(warped_features)}")
print(f"Regime features: {len(regime_features)}")
print(f"Total features: {len(all_features)}")

# 11. Save feature lists and processed data
feature_info = {
    'all_features': all_features,
    'base_features': base_features,
    'lagged_features': lagged_features,
    'rolling_features': rolling_features,
    'momentum_features': momentum_features,
    'options_features': options_features,
    'interaction_features': interaction_features,
    'warped_features': warped_features,
    'regime_features': regime_features,
    'target_features': ['target_direction', 'target_return', 'target_range', 'target_direction_2d', 'target_return_2d']
}

# Import configuration
from config import config

# Save to files
data.to_csv(config.get_data_file('engineered_data'), index=False)

import json
with open(config.get_data_file('feature_info'), 'w') as f:
    json.dump(feature_info, f, indent=2)

print(f"\nData saved to: {config.get_data_file('engineered_data')}")
print(f"Feature info saved to: {config.get_data_file('feature_info')}")
print(f"Final dataset shape: {data.shape}")

# Check for missing values in key features
print(f"\nMissing values in engineered features:")
missing_counts = data[all_features].isnull().sum()
missing_features = missing_counts[missing_counts > 0]
if len(missing_features) > 0:
    print(missing_features.head(10))
else:
    print("No missing values in key features")

print("\n=== Feature engineering completed ===")

