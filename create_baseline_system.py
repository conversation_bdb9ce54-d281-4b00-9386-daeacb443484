"""
Create a Simple, Working Baseline System
Start with basic features and build up from there
"""

import pandas as pd
import numpy as np
import pickle
import json
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import accuracy_score, roc_auc_score, classification_report
from config import config

def create_baseline_system():
    """Create a simple, working baseline system"""
    print("🔧 CREATING BASELINE SYSTEM")
    print("=" * 60)
    
    # 1. Load processed data
    print("1. Loading processed data...")
    data = pd.read_csv('spx_processed_data.csv')
    data['date'] = pd.to_datetime(data['date'])
    print(f"   📊 Data shape: {data.shape}")
    print(f"   📅 Date range: {data['date'].min()} to {data['date'].max()}")
    
    # 2. Create simple features
    print("2. Creating simple, robust features...")
    
    # Price features
    data['spx_return_1d'] = data['spx_close'].pct_change(1)
    data['spx_return_5d'] = data['spx_close'].pct_change(5)
    data['spx_sma_5'] = data['spx_close'].rolling(5).mean()
    data['spx_sma_20'] = data['spx_close'].rolling(20).mean()
    data['spx_above_sma5'] = (data['spx_close'] > data['spx_sma_5']).astype(int)
    data['spx_above_sma20'] = (data['spx_close'] > data['spx_sma_20']).astype(int)
    
    # VIX features
    data['vix_sma_5'] = data['vix_close'].rolling(5).mean()
    data['vix_sma_20'] = data['vix_close'].rolling(20).mean()
    data['vix_above_20'] = (data['vix_close'] > 20).astype(int)
    data['vix_above_30'] = (data['vix_close'] > 30).astype(int)
    
    # Volume features
    data['volume_sma_5'] = data['total_volume'].rolling(5).mean()
    data['volume_sma_20'] = data['total_volume'].rolling(20).mean()
    data['volume_above_avg'] = (data['total_volume'] > data['volume_sma_20']).astype(int)
    
    # Options features
    data['gamma_normalized'] = data['total_gamma'] / data['total_gamma'].rolling(20).mean()
    data['put_call_ratio'] = data['put_notional'] / (data['call_notional'] + 1e-6)
    
    # Target: next day direction
    data['target'] = (data['next_day_return'] > 0).astype(int)
    
    print(f"   ✅ Created features, data shape: {data.shape}")
    
    # 3. Select features
    print("3. Selecting baseline features...")
    
    feature_columns = [
        'spx_return_1d', 'spx_return_5d', 'spx_above_sma5', 'spx_above_sma20',
        'vix_close', 'vix_return', 'vix_above_20', 'vix_above_30',
        'total_volume', 'volume_above_avg',
        'total_gamma', 'gamma_normalized', 'put_call_ratio',
        'total_open_interest', 'unique_strikes'
    ]
    
    # Filter to available features
    available_features = [f for f in feature_columns if f in data.columns]
    print(f"   ✅ Using {len(available_features)} baseline features")
    
    # 4. Prepare clean data
    print("4. Preparing clean data...")
    
    # Remove rows with missing target or key features
    clean_data = data.dropna(subset=['target'] + available_features).copy()
    
    X = clean_data[available_features].copy()
    y = clean_data['target'].copy()
    
    # Handle any remaining missing values
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    # Replace infinite values
    X = X.replace([np.inf, -np.inf], np.nan)
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    print(f"   📊 Clean data shape: {X.shape}")
    print(f"   🎯 Target distribution: {y.value_counts().to_dict()}")
    print(f"   📈 Target balance: {y.mean():.1%} positive")
    
    # 5. Time series split for validation
    print("5. Creating time series validation...")
    
    tscv = TimeSeriesSplit(n_splits=3)
    cv_scores = []
    
    for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
        X_train_fold = X.iloc[train_idx]
        X_val_fold = X.iloc[val_idx]
        y_train_fold = y.iloc[train_idx]
        y_val_fold = y.iloc[val_idx]
        
        # Scale features
        scaler_fold = StandardScaler()
        X_train_scaled = scaler_fold.fit_transform(X_train_fold)
        X_val_scaled = scaler_fold.transform(X_val_fold)
        
        # Train model
        model_fold = RandomForestClassifier(
            n_estimators=50,
            max_depth=8,
            min_samples_split=50,
            min_samples_leaf=20,
            random_state=42,
            class_weight='balanced'
        )
        
        model_fold.fit(X_train_scaled, y_train_fold)
        
        # Validate
        val_pred = model_fold.predict(X_val_scaled)
        val_acc = accuracy_score(y_val_fold, val_pred)
        cv_scores.append(val_acc)
        
        print(f"   Fold {fold+1}: {val_acc:.1%} accuracy")
    
    avg_cv_score = np.mean(cv_scores)
    print(f"   📊 Average CV Score: {avg_cv_score:.1%}")
    
    # 6. Train final model
    print("6. Training final model...")
    
    # Use last 80% for training, 20% for testing
    split_idx = int(len(X) * 0.8)
    
    X_train = X.iloc[:split_idx].copy()
    X_test = X.iloc[split_idx:].copy()
    y_train = y.iloc[:split_idx].copy()
    y_test = y.iloc[split_idx:].copy()
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Train final model
    model = RandomForestClassifier(
        n_estimators=100,
        max_depth=8,
        min_samples_split=50,
        min_samples_leaf=20,
        random_state=42,
        class_weight='balanced'
    )
    
    model.fit(X_train_scaled, y_train)
    
    # 7. Evaluate model
    print("7. Evaluating model...")
    
    train_pred = model.predict(X_train_scaled)
    test_pred = model.predict(X_test_scaled)
    train_proba = model.predict_proba(X_train_scaled)[:, 1]
    test_proba = model.predict_proba(X_test_scaled)[:, 1]
    
    train_acc = accuracy_score(y_train, train_pred)
    test_acc = accuracy_score(y_test, test_pred)
    train_auc = roc_auc_score(y_train, train_proba)
    test_auc = roc_auc_score(y_test, test_proba)
    
    print(f"   📊 Train Accuracy: {train_acc:.1%}")
    print(f"   📊 Test Accuracy: {test_acc:.1%}")
    print(f"   📊 Train AUC: {train_auc:.3f}")
    print(f"   📊 Test AUC: {test_auc:.3f}")
    
    # Check for reasonable performance
    if test_acc < 0.45 or test_acc > 0.65:
        print(f"   ⚠️  Warning: Test accuracy {test_acc:.1%} seems unusual")
    else:
        print(f"   ✅ Test accuracy {test_acc:.1%} looks reasonable")
    
    # Feature importance
    feature_importance = pd.DataFrame({
        'feature': available_features,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"\\n   🎯 Top 5 Features:")
    for i, row in feature_importance.head().iterrows():
        print(f"      {row['feature']}: {row['importance']:.3f}")
    
    # 8. Save model
    print("8. Saving baseline model...")
    
    # Save model
    with open(config.get_model_file('best_model'), 'wb') as f:
        pickle.dump(model, f)
    
    # Save scaler
    with open(config.get_model_file('scaler'), 'wb') as f:
        pickle.dump(scaler, f)
    
    # Save selected features
    with open(config.get_data_file('selected_features'), 'w') as f:
        json.dump(available_features, f, indent=2)
    
    # Save feature info
    feature_info = {
        'all_features': available_features,
        'n_features': len(available_features),
        'feature_importance': feature_importance.to_dict('records')
    }
    
    with open(config.get_data_file('feature_info'), 'w') as f:
        json.dump(feature_info, f, indent=2)
    
    print(f"   ✅ Model saved: {config.get_model_file('best_model')}")
    print(f"   ✅ Scaler saved: {config.get_model_file('scaler')}")
    print(f"   ✅ Features saved: {len(available_features)} features")
    
    print("\\n✅ BASELINE SYSTEM CREATED!")
    print(f"   🎯 Test Accuracy: {test_acc:.1%}")
    print(f"   📊 Test AUC: {test_auc:.3f}")
    print(f"   🔧 Features: {len(available_features)}")
    print(f"   🚀 Ready for backtesting")
    
    return True

if __name__ == "__main__":
    success = create_baseline_system()
    if success:
        print("\\n🎉 Baseline system created successfully!")
    else:
        print("\\n❌ Failed to create baseline system!")
