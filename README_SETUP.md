# SPX Predictive System - Setup Guide

## Overview

This SPX Options Predictive Trading System has been updated with:
- ✅ **Centralized Configuration**: All directory paths are now managed in `config.py`
- ✅ **Virtual Environment**: Isolated Python environment with all dependencies
- ✅ **Portable Setup**: No more hardcoded `/home/<USER>/` paths

## Quick Start

### 1. Activate Virtual Environment

```bash
# Option A: Use the activation script (recommended)
./activate_env.sh

# Option B: Manual activation
source .venv/bin/activate
```

### 2. Run the Complete Pipeline

```bash
# Run the complete pipeline (recommended)
python main.py

# Or run specific stages
python main.py --data-only        # Data processing only
python main.py --skip-training    # Skip model training
python main.py --force-retrain    # Force model retraining

# See all options
python main.py --help
```

### 3. Verify Setup

```bash
# Test configuration
python config.py

# Run the prediction system
python demo_prediction_system.py
```

## Configuration System

### Key Features

- **Centralized Paths**: All file paths are defined in `config.py`
- **Flexible Base Directory**: Easily change where data files are stored
- **Automatic Directory Creation**: Directories are created as needed
- **Path Validation**: Built-in checks for file existence

### Configuration Usage

```python
# Import the default configuration
from config import config

# Get file paths
data_path = config.get_data_file('engineered_data')
model_path = config.get_model_file('best_model')
output_path = config.get_output_file('trading_performance_chart')

# Print current configuration
config.print_config()

# Create custom configuration with different base directory
from config import Config
custom_config = Config(base_dir='/path/to/your/data')
```

### File Organization

The system organizes files into three categories:

1. **Data Files** (`config.data_files`):
   - `spx_engineered_data.csv`
   - `feature_info.json`
   - `selected_features.json`
   - `backtest_results.json`
   - etc.

2. **Model Files** (`config.model_files`):
   - `best_model.pkl`
   - `scaler.pkl`
   - `spx_model.pkl`
   - etc.

3. **Output Files** (`config.output_files`):
   - `best_trades.csv`
   - `trading_performance.png`
   - Analysis charts and visualizations

## System Components

### Updated Files

All Python files have been updated to use the centralized configuration:

- ✅ `backtesting.py` - Trading strategy backtesting
- ✅ `demo_prediction_system.py` - Main prediction system
- ✅ `model_development_fixed.py` - Model training
- ✅ `feature_engineering.py` - Feature creation
- ✅ `create_visualizations.py` - Chart generation
- ✅ `data_exploration.py` - Data analysis

### Core Scripts

```bash
# Complete pipeline (RECOMMENDED - start here)
python main.py                    # Full pipeline: data → features → models → backtest
python main.py --data-only        # Data processing and feature engineering only
python main.py --skip-training    # Skip model training (use existing models)
python main.py --force-retrain    # Force model retraining
python main.py --skip-backtest    # Skip backtesting phase
python main.py --verbose          # Verbose logging
python main.py --log-file run.log # Save logs to file

# Individual components
python demo_prediction_system.py  # Prediction system (requires trained models)
python backtesting.py             # Backtesting analysis only
python model_development_fixed.py # Model training only
python create_visualizations.py   # Create charts and visualizations
python data_exploration.py        # Data exploration only
python feature_engineering.py     # Feature engineering only
```

## Virtual Environment

### What's Installed

The virtual environment includes all required packages:

- **Data Science**: pandas, numpy, scipy
- **Machine Learning**: scikit-learn, xgboost, lightgbm
- **Visualization**: matplotlib, seaborn
- **Development**: jupyter, ipython
- **Utilities**: joblib, threadpoolctl

### Managing the Environment

```bash
# Activate environment
source .venv/bin/activate

# Check installed packages
pip list

# Install additional packages
pip install package_name

# Update requirements.txt
pip freeze > requirements.txt

# Deactivate environment
deactivate
```

## Troubleshooting

### Common Issues

1. **Virtual Environment Not Found**
   ```bash
   python3 -m venv .venv
   source .venv/bin/activate
   pip install -r requirements.txt
   ```

2. **Permission Denied on activate_env.sh**
   ```bash
   chmod +x activate_env.sh
   ```

3. **Missing Dependencies**
   ```bash
   source .venv/bin/activate
   pip install -r requirements.txt
   ```

4. **Configuration Issues**
   ```bash
   python config.py  # Check configuration
   ```

### File Path Issues

If you encounter file not found errors:

1. Check the configuration:
   ```python
   from config import config
   config.print_config()
   ```

2. Verify files exist in the expected locations
3. Update the base directory if needed:
   ```python
   from config import Config
   config = Config(base_dir='/your/data/directory')
   ```

## Migration Notes

### Changes Made

- **Removed hardcoded paths**: No more `/home/<USER>/` references
- **Added configuration system**: Centralized path management
- **Created virtual environment**: Isolated dependencies
- **Updated all scripts**: Now use `config.py` for paths

### Backward Compatibility

The system maintains backward compatibility through:
- Individual path variables in `config.py`
- Same file names and structure
- Identical functionality

## Next Steps

1. **Activate the environment**: `./activate_env.sh`
2. **Test the system**: `python demo_prediction_system.py`
3. **Explore the configuration**: `python config.py`
4. **Run backtesting**: `python backtesting.py`
5. **Customize as needed**: Edit `config.py` for your setup

## Support

For issues or questions:
1. Check this README
2. Run `python config.py` to verify setup
3. Ensure virtual environment is activated
4. Verify all required files are present
