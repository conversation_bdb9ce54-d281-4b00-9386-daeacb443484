#!/usr/bin/env python3
"""
SPX Trading System - Main Entry Point
====================================

Main program to run the complete SPX trading system with the new directory structure:
- src/: Source code files
- cfg/: Configuration files  
- output/: Generated reports, data, and charts

Usage: python main.py
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_dir = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_dir))

# Import the complete system
from main_complete_system import CompleteSPXSystem

def main():
    """Main entry point for the SPX trading system"""
    print("🚀 SPX TRADING SYSTEM")
    print("=" * 60)
    print("📁 Directory Structure:")
    print("   📂 src/     - Source code")
    print("   📂 cfg/     - Configuration")
    print("   📂 output/  - Reports & data")
    print("=" * 60)
    
    try:
        # Initialize and run the complete system
        system = CompleteSPXSystem()
        success = system.run_complete_pipeline()
        
        if success:
            print(f"\n🎉 SUCCESS! System execution completed")
            print(f"📊 HTML Report: output/spx_complete_report.html")
            print(f"📈 Equity Curve: output/spx_equity_curve.png")
            print(f"💾 Trade Data: output/spx_trades.csv")
            print(f"📋 Results: output/spx_results.json")
            
            # Show quick summary
            print(f"\n📊 QUICK SUMMARY:")
            print(f"   🎯 Win Rate: {system.system_results['win_rate']:.1%}")
            print(f"   💰 Total Return: {system.system_results['total_return']:.1f}%")
            print(f"   📊 Total Trades: {system.system_results['total_trades']}")
            print(f"   🚦 Current Signal: {system.current_prediction['trade_signal']}")
            
            return True
        else:
            print(f"\n❌ System execution failed")
            return False
            
    except Exception as e:
        print(f"\n❌ Error running system: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ System ready for trading analysis!")
    else:
        print(f"\n❌ System execution failed")
        sys.exit(1)
