#!/usr/bin/env python3
"""
SPX Trading System - Main Entry Point
====================================

Main program to run the complete SPX trading system with the new directory structure:
- src/: Source code files
- cfg/: Configuration files  
- output/: Generated reports, data, and charts

Usage: python main.py
"""

import sys
import os
import shutil
from pathlib import Path

# Add src directory to Python path
src_dir = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_dir))

# Import the complete system
from main_complete_system import CompleteSPXSystem

def clean_output_directory():
    """Clean output directory for fresh start"""
    output_dir = Path(__file__).parent / 'output'
    if output_dir.exists():
        print("🧹 CLEANING OUTPUT DIRECTORY")
        print("-" * 40)

        # Count files before deletion
        files_before = list(output_dir.glob('*'))
        file_count = len([f for f in files_before if f.is_file()])

        if file_count > 0:
            print(f"   🗑️  Removing {file_count} existing output files...")

            # Remove all files in output directory
            for item in output_dir.iterdir():
                if item.is_file():
                    item.unlink()
                    print(f"      ❌ Deleted: {item.name}")
                elif item.is_dir() and item.name != '.gitkeep':
                    shutil.rmtree(item)
                    print(f"      ❌ Deleted directory: {item.name}")

            print(f"   ✅ Output directory cleaned")
        else:
            print(f"   ✅ Output directory already clean")

        print()
    else:
        # Create output directory if it doesn't exist
        output_dir.mkdir(exist_ok=True)
        print("📁 Created output directory")
        print()

def main():
    """Main entry point for the SPX trading system"""
    print("🚀 SPX TRADING SYSTEM")
    print("=" * 60)
    print("📁 Directory Structure:")
    print("   📂 src/     - Source code")
    print("   📂 cfg/     - Configuration")
    print("   📂 output/  - Reports & data")
    print("=" * 60)
    print()

    # Clean output directory for fresh start
    clean_output_directory()
    
    try:
        # Initialize and run the complete system
        system = CompleteSPXSystem()
        success = system.run_complete_pipeline()
        
        if success:
            print(f"\n🎉 SUCCESS! System execution completed")
            print(f"📊 HTML Report: output/spx_complete_report.html")
            print(f"📈 Equity Curve: output/spx_equity_curve.png")
            print(f"💾 Trade Data: output/spx_trades.csv")
            print(f"📋 Results: output/spx_results.json")
            
            # Show quick summary
            print(f"\n📊 QUICK SUMMARY:")
            print(f"   🎯 Win Rate: {system.system_results['win_rate']:.1%}")
            print(f"   💰 Total Return: {system.system_results['total_return']:.1f}%")
            print(f"   📊 Total Trades: {system.system_results['total_trades']}")
            print(f"   🚦 Current Signal: {system.current_prediction['trade_signal']}")
            
            return True
        else:
            print(f"\n❌ System execution failed")
            return False
            
    except Exception as e:
        print(f"\n❌ Error running system: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ System ready for trading analysis!")
    else:
        print(f"\n❌ System execution failed")
        sys.exit(1)
