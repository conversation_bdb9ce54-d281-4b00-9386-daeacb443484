#!/usr/bin/env python3
"""
SPX Predictive System - Main Pipeline
====================================

Complete pipeline for loading data, training models, and backtesting.
This script orchestrates the entire machine learning workflow.

Usage:
    python main.py [options]

Options:
    --skip-training     Skip model training if models exist
    --force-retrain     Force retraining even if models exist
    --skip-backtest     Skip backtesting phase
    --data-only         Only run data processing and feature engineering
    --verbose           Enable verbose logging

Author: Manus AI
Date: June 23, 2025
"""

import argparse
import sys
import os
from datetime import datetime
from pathlib import Path

# Import configuration and logging
from config import config
from pipeline_logger import create_pipeline_logger, PipelineLogger

def print_banner():
    """Print system banner"""
    print("=" * 80)
    print("🚀 SPX PREDICTIVE SYSTEM - COMPLETE PIPELINE")
    print("=" * 80)
    print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Base Directory: {config.base_dir}")
    print("=" * 80)

def print_stage(stage_name, stage_num, total_stages):
    """Print stage header"""
    print(f"\n{'='*20} STAGE {stage_num}/{total_stages}: {stage_name.upper()} {'='*20}")
    print(f"⏰ {datetime.now().strftime('%H:%M:%S')}")

def check_file_exists(file_path, description):
    """Check if a file exists and print status"""
    exists = Path(file_path).exists()
    status = "✅" if exists else "❌"
    print(f"  {status} {description}: {file_path}")
    return exists

def run_data_exploration(logger: PipelineLogger, verbose=False):
    """Run data exploration and processing"""
    logger.start_stage("Data Exploration & Processing", 1)

    try:
        # Check if processed data already exists
        processed_data_exists = logger.file_status(
            config.get_data_file('processed_data'),
            "Processed Data"
        )

        if processed_data_exists and config.get_pipeline_config('auto_skip_existing'):
            logger.info("📊 Processed data already exists, skipping data exploration...")
            logger.complete_stage(True)
            return True

        logger.info("🔍 Running data exploration and processing...")

        # Import and run data exploration
        import subprocess
        result = subprocess.run([
            sys.executable, 'data_exploration.py'
        ], capture_output=not verbose, text=True, cwd=config.base_dir)

        if result.returncode == 0:
            logger.info("✅ Data exploration completed successfully")
            logger.complete_stage(True)
            return True
        else:
            logger.error(f"❌ Data exploration failed: {result.stderr if not verbose else ''}")
            logger.complete_stage(False)
            return False

    except Exception as e:
        logger.error(f"❌ Error in data exploration: {str(e)}")
        logger.complete_stage(False)
        return False

def run_feature_engineering(logger: PipelineLogger, verbose=False):
    """Run feature engineering"""
    logger.start_stage("Feature Engineering", 2)

    try:
        # Check if engineered data already exists
        engineered_data_exists = logger.file_status(
            config.get_data_file('engineered_data'),
            "Engineered Data"
        )
        feature_info_exists = logger.file_status(
            config.get_data_file('feature_info'),
            "Feature Info"
        )

        if engineered_data_exists and feature_info_exists and config.get_pipeline_config('auto_skip_existing'):
            logger.info("🔧 Engineered features already exist, skipping feature engineering...")
            logger.complete_stage(True)
            return True

        logger.info("🔧 Running feature engineering...")

        # Import and run feature engineering
        import subprocess
        result = subprocess.run([
            sys.executable, 'feature_engineering.py'
        ], capture_output=not verbose, text=True, cwd=config.base_dir)

        if result.returncode == 0:
            logger.info("✅ Feature engineering completed successfully")
            logger.complete_stage(True)
            return True
        else:
            logger.error(f"❌ Feature engineering failed: {result.stderr if not verbose else ''}")
            logger.complete_stage(False)
            return False

    except Exception as e:
        logger.error(f"❌ Error in feature engineering: {str(e)}")
        logger.complete_stage(False)
        return False

def run_model_training(logger: PipelineLogger, force_retrain=False, skip_training=False, verbose=False):
    """Run model training"""
    logger.start_stage("Model Training", 3)
    
    try:
        # Check if models already exist
        model_exists = logger.file_status(
            config.get_model_file('best_model'),
            "Best Model"
        )
        scaler_exists = logger.file_status(
            config.get_model_file('scaler'),
            "Scaler"
        )
        features_exist = logger.file_status(
            config.get_data_file('selected_features'),
            "Selected Features"
        )

        if skip_training:
            logger.info("⏭️  Skipping model training as requested...")
            logger.complete_stage(model_exists and scaler_exists and features_exist)
            return model_exists and scaler_exists and features_exist

        if model_exists and scaler_exists and features_exist and not force_retrain and config.get_pipeline_config('auto_skip_existing'):
            logger.info("🤖 Trained models already exist, skipping training...")
            logger.info("   Use --force-retrain to retrain models")
            logger.complete_stage(True)
            return True

        if force_retrain:
            logger.info("🔄 Force retraining models...")
        else:
            logger.info("🤖 Training models...")

        # Import and run enhanced model training
        import subprocess
        result = subprocess.run([
            sys.executable, 'model_development_enhanced.py'
        ], capture_output=not verbose, text=True, cwd=config.base_dir)

        if result.returncode == 0:
            logger.info("✅ Model training completed successfully")
            logger.complete_stage(True)
            return True
        else:
            logger.error(f"❌ Model training failed: {result.stderr if not verbose else ''}")
            logger.complete_stage(False)
            return False

    except Exception as e:
        logger.error(f"❌ Error in model training: {str(e)}")
        logger.complete_stage(False)
        return False

def run_backtesting(logger: PipelineLogger, skip_backtest=False, verbose=False):
    """Run backtesting"""
    logger.start_stage("Backtesting", 4)
    
    try:
        if skip_backtest:
            logger.info("⏭️  Skipping backtesting as requested...")
            logger.complete_stage(True)
            return True

        # Check if backtest results already exist
        backtest_exists = logger.file_status(
            config.get_data_file('backtest_results'),
            "Backtest Results"
        )

        if backtest_exists and config.get_pipeline_config('auto_skip_existing'):
            logger.info("📈 Backtest results already exist, skipping backtesting...")
            logger.info("   Delete backtest_results.json to force re-run")
            logger.complete_stage(True)
            return True

        logger.info("📈 Running backtesting...")

        # Import and run R-squared optimized backtesting
        import subprocess
        result = subprocess.run([
            sys.executable, 'backtesting_rsquared_optimized.py'
        ], capture_output=not verbose, text=True, cwd=config.base_dir)

        if result.returncode == 0:
            logger.info("✅ Backtesting completed successfully")
            logger.complete_stage(True)
            return True
        else:
            logger.error(f"❌ Backtesting failed: {result.stderr if not verbose else ''}")
            logger.complete_stage(False)
            return False

    except Exception as e:
        logger.error(f"❌ Error in backtesting: {str(e)}")
        logger.complete_stage(False)
        return False

def run_visualization(logger: PipelineLogger, verbose=False):
    """Run visualization creation"""
    logger.start_stage("Visualization", 5)

    try:
        logger.info("📊 Creating visualizations...")

        # Import and run visualization
        import subprocess
        result = subprocess.run([
            sys.executable, 'create_visualizations.py'
        ], capture_output=not verbose, text=True, cwd=config.base_dir)

        if result.returncode == 0:
            logger.info("✅ Visualizations created successfully")

            # Generate HTML report
            logger.info("📄 Generating HTML performance report...")
            html_result = subprocess.run([
                sys.executable, 'create_html_report.py'
            ], capture_output=not verbose, text=True, cwd=config.base_dir)

            if html_result.returncode == 0:
                logger.info("✅ HTML report generated successfully")
                logger.complete_stage(True)
                return True
            else:
                logger.warning(f"⚠️  HTML report generation failed: {html_result.stderr if not verbose else ''}")
                logger.complete_stage(True)  # Don't fail the stage for HTML report issues
                return True
        else:
            logger.error(f"❌ Visualization creation failed: {result.stderr if not verbose else ''}")
            logger.complete_stage(False)
            return False

    except Exception as e:
        logger.error(f"❌ Error in visualization: {str(e)}")
        logger.complete_stage(False)
        return False

def print_summary():
    """Print pipeline summary"""
    print("\n" + "=" * 80)
    print("📋 PIPELINE SUMMARY")
    print("=" * 80)
    
    # Check final outputs
    files_to_check = [
        (config.get_data_file('engineered_data'), "Engineered Data"),
        (config.get_model_file('best_model'), "Best Model"),
        (config.get_model_file('scaler'), "Scaler"),
        (config.get_data_file('selected_features'), "Selected Features"),
        (config.get_data_file('backtest_results'), "Backtest Results"),
        (config.get_output_file('best_trades'), "Best Trades"),
        (config.get_output_file('trading_performance_chart'), "Performance Chart"),
        (config.get_output_file('html_report'), "HTML Report"),
    ]
    
    all_exist = True
    for file_path, description in files_to_check:
        exists = check_file_exists(file_path, description)
        if not exists:
            all_exist = False
    
    print("\n" + "=" * 80)
    if all_exist:
        print("🎉 PIPELINE COMPLETED SUCCESSFULLY!")
        print("\n📊 Next steps:")
        print("   python demo_prediction_system.py  # Run prediction system")
        print("   python config.py                  # View configuration")
        print(f"   open {config.get_output_file('html_report')}  # View HTML report")
    else:
        print("⚠️  PIPELINE COMPLETED WITH ISSUES")
        print("   Some files are missing. Check the logs above for errors.")
    
    print(f"\n⏰ Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

def main():
    """Main pipeline function"""
    parser = argparse.ArgumentParser(description='SPX Predictive System Pipeline')
    parser.add_argument('--skip-training', action='store_true',
                       help='Skip model training if models exist')
    parser.add_argument('--force-retrain', action='store_true',
                       help='Force retraining even if models exist')
    parser.add_argument('--skip-backtest', action='store_true',
                       help='Skip backtesting phase')
    parser.add_argument('--data-only', action='store_true',
                       help='Only run data processing and feature engineering')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--log-file', type=str,
                       help='Log file path (optional)')
    parser.add_argument('--log-level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')

    args = parser.parse_args()

    # Create logger
    log_level = args.log_level if args.verbose else config.get_pipeline_config('log_level')
    logger = create_pipeline_logger(log_level, args.log_file)

    # Print banner
    print_banner()

    # Set total stages
    total_stages = 2 if args.data_only else 5
    logger.set_total_stages(total_stages)

    # Ensure base directory exists
    config.ensure_directories_exist()

    # Run pipeline stages
    success = True

    # Stage 1: Data Exploration
    if not run_data_exploration(logger, args.verbose):
        success = False

    # Stage 2: Feature Engineering
    if success and not run_feature_engineering(logger, args.verbose):
        success = False

    # Stop here if data-only mode
    if args.data_only:
        logger.info("📊 Data processing completed (data-only mode)")
        logger.execution_summary()
        return

    # Stage 3: Model Training
    if success and not run_model_training(logger, args.force_retrain, args.skip_training, args.verbose):
        success = False

    # Stage 4: Backtesting
    if success and not run_backtesting(logger, args.skip_backtest, args.verbose):
        success = False

    # Stage 5: Visualization
    if success and not run_visualization(logger, args.verbose):
        success = False

    # Print summary
    print_summary()
    logger.execution_summary()

    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
