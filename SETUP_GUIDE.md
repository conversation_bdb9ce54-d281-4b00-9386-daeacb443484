# SPX Options Predictive Trading System - Setup Guide

## Quick Setup

1. **Extract the zip file** to your desired directory
2. **Install Python 3.11+** if not already installed
3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```
4. **Run the system:**
   ```bash
   python demo_prediction_system.py
   ```

## What's Included

### Main System Files
- `demo_prediction_system.py` - **START HERE** - Main executable system
- `spx_prediction_system.py` - Full-featured command-line system
- `requirements.txt` - Python dependencies

### Trained Models
- `best_model.pkl` - Trained LightGBM model (68.2% win rate)
- `scaler.pkl` - Feature scaling parameters
- `selected_features.json` - Top 30 predictive features

### Data Files
- `spx_engineered_data.csv` - Complete dataset with 153 features
- `best_trades.csv` - All 22 profitable trades from backtesting
- `backtest_results.json` - Performance metrics

### Documentation
- `SPX_Predictive_System_Report.pdf` - Complete technical report
- `DELIVERABLES_SUMMARY.md` - System overview and results

### Visualizations
- `trading_performance.png` - Strategy performance charts
- `correlation_analysis.png` - Feature correlation matrix
- `spx_price_analysis.png` - Price and return analysis
- Plus additional analysis charts

## Usage Examples

### Generate Trading Signal
```bash
python demo_prediction_system.py
```

### Command Line Interface
```bash
# Train new model
python spx_prediction_system.py --mode train --data your_data.csv

# Generate predictions
python spx_prediction_system.py --mode predict --data your_data.csv

# Run backtest
python spx_prediction_system.py --mode backtest --data your_data.csv

# Generate signal
python spx_prediction_system.py --mode signal --data your_data.csv
```

## System Requirements

- **Python:** 3.11 or higher
- **RAM:** 8GB minimum for model training
- **Storage:** 100MB for all files
- **OS:** Windows, macOS, or Linux

## Key Performance Metrics

- **Win Rate:** 68.2%
- **Total Return:** 17.21% over 22 trades
- **Average Return per Trade:** 0.782%
- **Maximum Drawdown:** 7.11%
- **Sharpe Ratio:** 0.298

## Support

For questions or issues:
1. Check the comprehensive technical report (PDF)
2. Review the DELIVERABLES_SUMMARY.md file
3. Examine the demo script for usage examples

## Next Steps

1. Run `demo_prediction_system.py` to see the system in action
2. Review the technical report for detailed methodology
3. Examine the backtesting results and trade history
4. Consider live deployment with appropriate risk management

**The system is ready for immediate use!**

