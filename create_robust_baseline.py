"""
Create Robust Baseline System
Use the same methodology that showed 66.5% accuracy
"""

import pandas as pd
import numpy as np
import pickle
import json
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import accuracy_score, roc_auc_score
from config import config

def create_robust_baseline():
    """Create baseline system using the methodology that worked"""
    print("🔧 CREATING ROBUST BASELINE SYSTEM")
    print("=" * 60)
    
    # 1. Load and prepare data (same as working baseline)
    print("1. Loading and preparing data...")
    
    data = pd.read_csv('spx_processed_data.csv')
    data['date'] = pd.to_datetime(data['date'])
    
    # Create features exactly as in working baseline
    data['spx_return_1d'] = data['spx_close'].pct_change(1)
    data['spx_return_5d'] = data['spx_close'].pct_change(5)
    data['spx_sma_5'] = data['spx_close'].rolling(5).mean()
    data['spx_sma_20'] = data['spx_close'].rolling(20).mean()
    data['spx_above_sma5'] = (data['spx_close'] > data['spx_sma_5']).astype(int)
    data['spx_above_sma20'] = (data['spx_close'] > data['spx_sma_20']).astype(int)
    data['vix_sma_5'] = data['vix_close'].rolling(5).mean()
    data['vix_sma_20'] = data['vix_close'].rolling(20).mean()
    data['vix_above_20'] = (data['vix_close'] > 20).astype(int)
    data['vix_above_30'] = (data['vix_close'] > 30).astype(int)
    data['volume_sma_5'] = data['total_volume'].rolling(5).mean()
    data['volume_sma_20'] = data['total_volume'].rolling(20).mean()
    data['volume_above_avg'] = (data['total_volume'] > data['volume_sma_20']).astype(int)
    data['gamma_normalized'] = data['total_gamma'] / data['total_gamma'].rolling(20).mean()
    data['put_call_ratio'] = data['put_notional'] / (data['call_notional'] + 1e-6)
    data['target'] = (data['next_day_return'] > 0).astype(int)
    
    # Baseline features
    baseline_features = [
        'spx_return_1d', 'spx_return_5d', 'spx_above_sma5', 'spx_above_sma20',
        'vix_close', 'vix_return', 'vix_above_20', 'vix_above_30',
        'total_volume', 'volume_above_avg',
        'total_gamma', 'gamma_normalized', 'put_call_ratio',
        'total_open_interest', 'unique_strikes'
    ]
    
    print(f"   📊 Data shape: {data.shape}")
    print(f"   🎯 Features: {len(baseline_features)}")
    
    # 2. Clean data
    print("2. Cleaning data...")
    
    clean_data = data.dropna(subset=['target'] + baseline_features).copy()
    X = clean_data[baseline_features].copy()
    y = clean_data['target'].copy()
    
    # Handle missing values
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    X = X.replace([np.inf, -np.inf], np.nan)
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    print(f"   📊 Clean data shape: {X.shape}")
    print(f"   📈 Target balance: {y.mean():.1%} positive")
    
    # 3. Use time series validation (like working baseline)
    print("3. Time series cross-validation...")
    
    tscv = TimeSeriesSplit(n_splits=5)
    cv_scores = []
    cv_models = []
    
    for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
        X_train_fold = X.iloc[train_idx]
        X_val_fold = X.iloc[val_idx]
        y_train_fold = y.iloc[train_idx]
        y_val_fold = y.iloc[val_idx]
        
        # Scale features
        scaler_fold = StandardScaler()
        X_train_scaled = scaler_fold.fit_transform(X_train_fold)
        X_val_scaled = scaler_fold.transform(X_val_fold)
        
        # Train model with conservative parameters
        model_fold = RandomForestClassifier(
            n_estimators=50,      # Fewer trees
            max_depth=6,          # Shallower trees
            min_samples_split=100, # More conservative splits
            min_samples_leaf=50,   # Larger leaves
            random_state=42,
            class_weight='balanced',
            max_features='sqrt'    # Limit feature selection
        )
        
        model_fold.fit(X_train_scaled, y_train_fold)
        
        # Validate
        val_pred = model_fold.predict(X_val_scaled)
        val_acc = accuracy_score(y_val_fold, val_pred)
        cv_scores.append(val_acc)
        cv_models.append((model_fold, scaler_fold))
        
        print(f"   Fold {fold+1}: {val_acc:.1%} accuracy")
    
    avg_cv_score = np.mean(cv_scores)
    cv_std = np.std(cv_scores)
    print(f"   📊 Average CV Score: {avg_cv_score:.1%} ± {cv_std:.1%}")
    
    # 4. Train final model on all data (like working baseline)
    print("4. Training final model on all data...")
    
    # Use conservative parameters that showed good CV performance
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    model = RandomForestClassifier(
        n_estimators=50,
        max_depth=6,
        min_samples_split=100,
        min_samples_leaf=50,
        random_state=42,
        class_weight='balanced',
        max_features='sqrt'
    )
    
    model.fit(X_scaled, y)
    
    # 5. Test on full dataset (like working baseline)
    print("5. Testing on full dataset...")
    
    full_pred = model.predict(X_scaled)
    full_proba = model.predict_proba(X_scaled)[:, 1]
    full_acc = accuracy_score(y, full_pred)
    
    print(f"   📊 Full Dataset Accuracy: {full_acc:.1%}")
    
    # Check prediction alignment
    correct_when_pred_up = clean_data[full_pred == 1]['target'].mean()
    correct_when_pred_down = clean_data[full_pred == 0]['target'].mean()
    
    print(f"   📈 When predicted UP: {correct_when_pred_up:.1%} actually went up")
    print(f"   📉 When predicted DOWN: {(1-correct_when_pred_down):.1%} actually went down")
    
    if correct_when_pred_up > 0.6 and correct_when_pred_down < 0.4:
        print("   ✅ Predictions are well-aligned")
        predictions_good = True
    else:
        print("   ⚠️ Predictions need improvement")
        predictions_good = False
    
    # 6. Feature importance
    feature_importance = pd.DataFrame({
        'feature': baseline_features,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"\\n   🎯 Top 5 Features:")
    for i, row in feature_importance.head().iterrows():
        print(f"      {row['feature']}: {row['importance']:.3f}")
    
    # 7. Save robust baseline
    print("\\n7. Saving robust baseline...")
    
    # Save model
    with open(config.get_model_file('best_model'), 'wb') as f:
        pickle.dump(model, f)
    
    # Save scaler
    with open(config.get_model_file('scaler'), 'wb') as f:
        pickle.dump(scaler, f)
    
    # Save features
    with open(config.get_data_file('selected_features'), 'w') as f:
        json.dump(baseline_features, f, indent=2)
    
    # Save feature info
    feature_info = {
        'all_features': baseline_features,
        'n_features': len(baseline_features),
        'cv_accuracy': avg_cv_score,
        'cv_std': cv_std,
        'full_accuracy': full_acc,
        'predictions_aligned': predictions_good,
        'feature_importance': feature_importance.to_dict('records')
    }
    
    with open(config.get_data_file('feature_info'), 'w') as f:
        json.dump(feature_info, f, indent=2)
    
    print(f"   ✅ Robust baseline saved")
    print(f"   ✅ CV Accuracy: {avg_cv_score:.1%}")
    print(f"   ✅ Full Accuracy: {full_acc:.1%}")
    
    print("\\n✅ ROBUST BASELINE SYSTEM CREATED!")
    
    if predictions_good and full_acc > 0.6:
        print("   🎉 Baseline system is robust and ready!")
        return True
    else:
        print("   ⚠️ Baseline system needs further tuning")
        return False

if __name__ == "__main__":
    success = create_robust_baseline()
    if success:
        print("\\n🚀 Ready for conservative enhancements!")
    else:
        print("\\n🔧 Need to fix baseline first")
