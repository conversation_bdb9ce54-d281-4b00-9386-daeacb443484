import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")

print("=== SPX Option Data Analysis ===")
print(f"Analysis started at: {datetime.now()}")

# Load the data
print("\n1. Loading data...")
q1_data = pd.read_csv('/home/<USER>/upload/spx_option_daily_analysis_2025_q1.csv')
q2_data = pd.read_csv('/home/<USER>/upload/spx_option_daily_analysis_2025_q2.csv')

# Combine the datasets
data = pd.concat([q1_data, q2_data], ignore_index=True)
data['date'] = pd.to_datetime(data['date'])
data = data.sort_values('date').reset_index(drop=True)

print(f"Q1 data shape: {q1_data.shape}")
print(f"Q2 data shape: {q2_data.shape}")
print(f"Combined data shape: {data.shape}")
print(f"Date range: {data['date'].min()} to {data['date'].max()}")

# Basic data info
print("\n2. Data Structure:")
print(data.info())

print("\n3. Column descriptions:")
columns_desc = {
    'date': 'Trading date',
    'spx_open': 'SPX opening price',
    'spx_high': 'SPX high price',
    'spx_low': 'SPX low price', 
    'spx_close': 'SPX closing price',
    'put_wall_strike': 'Strike price with highest put open interest',
    'put_wall_oi': 'Open interest at put wall strike',
    'call_wall_strike': 'Strike price with highest call open interest',
    'call_wall_oi': 'Open interest at call wall strike',
    'gamma_put_wall_strike': 'Strike with highest put gamma exposure',
    'gamma_put_wall_exposure': 'Put gamma exposure value',
    'gamma_call_wall_strike': 'Strike with highest call gamma exposure',
    'gamma_call_wall_exposure': 'Call gamma exposure value',
    'total_gamma': 'Total gamma across all options',
    'total_vega': 'Total vega across all options',
    'total_open_interest': 'Total open interest',
    'total_volume': 'Total options volume',
    'unique_strikes': 'Number of unique strike prices',
    'total_options': 'Total number of options contracts',
    'call_notional': 'Total notional value of calls',
    'put_notional': 'Total notional value of puts',
    'total_notional': 'Total notional value'
}

for col, desc in columns_desc.items():
    if col in data.columns:
        print(f"  {col}: {desc}")

# Check for missing values
print("\n4. Missing values:")
missing_values = data.isnull().sum()
print(missing_values[missing_values > 0])

# Basic statistics
print("\n5. Basic Statistics for key variables:")
key_vars = ['spx_close', 'total_gamma', 'total_vega', 'total_open_interest', 
           'total_volume', 'call_notional', 'put_notional']
print(data[key_vars].describe())

# Calculate daily returns and other derived features
print("\n6. Creating derived features...")
data['spx_return'] = data['spx_close'].pct_change()
data['spx_return_next'] = data['spx_return'].shift(-1)  # Next day return (target)
data['spx_direction_next'] = (data['spx_return_next'] > 0).astype(int)  # 1 for up, 0 for down

# Intraday range
data['spx_range'] = data['spx_high'] - data['spx_low']
data['spx_range_pct'] = data['spx_range'] / data['spx_open'] * 100

# Options metrics
data['put_call_oi_ratio'] = data['put_wall_oi'] / data['call_wall_oi']
data['put_call_notional_ratio'] = data['put_notional'] / data['call_notional']
data['gamma_exposure_ratio'] = data['gamma_call_wall_exposure'] / (data['gamma_put_wall_exposure'] + 1e-10)

# Distance from walls
data['spx_to_put_wall'] = (data['spx_close'] - data['put_wall_strike']) / data['spx_close'] * 100
data['spx_to_call_wall'] = (data['call_wall_strike'] - data['spx_close']) / data['spx_close'] * 100

print("Derived features created:")
derived_features = ['spx_return', 'spx_return_next', 'spx_direction_next', 'spx_range_pct',
                   'put_call_oi_ratio', 'put_call_notional_ratio', 'gamma_exposure_ratio',
                   'spx_to_put_wall', 'spx_to_call_wall']
for feature in derived_features:
    print(f"  {feature}: {data[feature].describe().loc[['mean', 'std']]}")

# Save processed data
data.to_csv('/home/<USER>/spx_processed_data.csv', index=False)
print(f"\nProcessed data saved to: /home/<USER>/spx_processed_data.csv")

print("\n=== Data exploration completed ===")

