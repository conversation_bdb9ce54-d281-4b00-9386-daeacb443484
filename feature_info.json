{"all_features": ["spx_range_pct_lag2", "put_call_oi_ratio_lag3", "total_volume_lag3", "total_gamma_lag5", "gamma_vol_interaction", "spx_range_pct_std10", "total_volume_std10", "spx_to_put_wall_lag3", "spx_return_max3", "put_wall_oi", "gamma_pc_interaction", "spx_range_pct_lag5", "spx_to_put_wall_lag4", "spx_return_ma5", "high_vol_regime", "spx_momentum_5d", "return_momentum_3d", "put_wall_distance_change", "spx_range_pct_std5", "spx_range_pct_lag3", "put_wall_strike", "volume_surge_3d", "put_call_oi_ratio_ma3", "put_call_oi_ratio_lag1", "spx_range_pct_ma3", "log_total_oi", "put_call_oi_ratio_min10", "spx_momentum_10d", "total_volume_max10", "pc_ratio_change_3d", "total_volume_min3", "total_volume", "put_call_oi_ratio_max10", "gamma_change_3d", "total_volume_lag4", "put_call_notional_ratio", "spx_to_put_wall_lag1", "spx_return_lag5", "spx_to_call_wall_lag2", "spx_range_pct_std3", "vol_pc_interaction", "put_call_oi_ratio_ma5", "total_vega_lag2", "total_gamma_min10", "spx_to_put_wall_lag5", "spx_to_call_wall_lag1", "spx_to_call_wall_lag4", "put_call_oi_ratio_lag5", "spx_range_pct_lag4", "total_volume_ma3", "spx_return_ma10", "total_vega_lag1", "spx_return_min10", "total_volume_ma5", "spx_return_std10", "total_gamma_std10", "spx_range_pct_ma5", "pc_ratio_change_1d", "put_call_oi_ratio_lag2", "spx_range_pct_min5", "total_gamma_min3", "spx_range_pct_max5", "put_call_oi_ratio_std10", "total_volume_min10", "put_call_oi_ratio_lag4", "gamma_call_wall_exposure", "vol_gamma_interaction", "put_call_oi_ratio_max5", "spx_range_pct_max3", "spx_to_put_wall", "spx_to_call_wall_lag5", "spx_return_lag2", "put_call_notional_ratio_lag4", "total_volume_std3", "call_wall_distance_change", "spx_range_pct_max10", "sqrt_range", "spx_return_min5", "put_call_notional_ratio_lag5", "total_gamma_lag2", "put_call_notional_ratio_lag3", "uptrend_regime", "total_gamma", "total_gamma_max3", "total_gamma_max10", "gamma_put_wall_strike", "total_gamma_ma10", "total_gamma_lag4", "spx_return_max10", "gamma_put_wall_exposure", "put_call_oi_ratio_ma10", "spx_return_min3", "total_gamma_lag1", "total_vega_lag5", "total_gamma_ma3", "spx_return_lag4", "call_wall_strike", "realized_vol_10d", "total_volume_min5", "put_call_oi_ratio_min3", "spx_return_std3", "total_vega_lag3", "total_volume_rank", "total_gamma_std3", "total_gamma_ma5", "volume_surge_5d", "total_gamma_lag3", "spx_range_pct_min3", "spx_range_pct_min10", "spx_return_std5", "log_total_volume", "total_volume_lag2", "put_call_oi_ratio_std3", "wall_asymmetry", "total_volume_lag5", "high_options_activity", "spx_range_pct_ma10", "put_call_oi_ratio_std5", "realized_vol_5d", "put_call_oi_ratio_rank", "put_call_oi_ratio_max3", "wall_squeeze", "gamma_exposure_ratio", "put_call_oi_ratio_min5", "gamma_change_1d", "total_volume_max3", "spx_range_pct_lag1", "total_volume_std5", "spx_to_call_wall", "total_volume_max5", "call_wall_oi", "total_vega", "put_call_oi_ratio", "spx_return_lag3", "spx_return_ma3", "total_gamma_max5", "total_volume_lag1", "spx_return_lag1", "total_gamma_min5", "total_gamma_rank", "gamma_call_wall_strike", "put_call_notional_ratio_lag1", "spx_range_pct", "spx_to_call_wall_lag3", "total_gamma_std5", "spx_return_max5", "spx_momentum_3d", "spx_to_put_wall_lag2", "total_volume_ma10", "put_call_notional_ratio_lag2", "return_momentum_5d", "spx_return", "total_vega_lag4"], "base_features": ["spx_return", "spx_range_pct", "put_call_oi_ratio", "put_call_notional_ratio", "total_gamma", "total_vega", "total_volume", "spx_to_put_wall", "spx_to_call_wall"], "lagged_features": ["spx_return_lag1", "spx_return_lag2", "spx_return_lag3", "spx_return_lag4", "spx_return_lag5", "spx_range_pct_lag1", "spx_range_pct_lag2", "spx_range_pct_lag3", "spx_range_pct_lag4", "spx_range_pct_lag5", "put_call_oi_ratio_lag1", "put_call_oi_ratio_lag2", "put_call_oi_ratio_lag3", "put_call_oi_ratio_lag4", "put_call_oi_ratio_lag5", "put_call_notional_ratio_lag1", "put_call_notional_ratio_lag2", "put_call_notional_ratio_lag3", "put_call_notional_ratio_lag4", "put_call_notional_ratio_lag5", "total_gamma_lag1", "total_gamma_lag2", "total_gamma_lag3", "total_gamma_lag4", "total_gamma_lag5", "total_vega_lag1", "total_vega_lag2", "total_vega_lag3", "total_vega_lag4", "total_vega_lag5", "total_volume_lag1", "total_volume_lag2", "total_volume_lag3", "total_volume_lag4", "total_volume_lag5", "spx_to_put_wall_lag1", "spx_to_put_wall_lag2", "spx_to_put_wall_lag3", "spx_to_put_wall_lag4", "spx_to_put_wall_lag5", "spx_to_call_wall_lag1", "spx_to_call_wall_lag2", "spx_to_call_wall_lag3", "spx_to_call_wall_lag4", "spx_to_call_wall_lag5"], "rolling_features": ["spx_return_ma3", "spx_return_std3", "spx_return_min3", "spx_return_max3", "spx_return_ma5", "spx_return_std5", "spx_return_min5", "spx_return_max5", "spx_return_ma10", "spx_return_std10", "spx_return_min10", "spx_return_max10", "spx_range_pct_ma3", "spx_range_pct_std3", "spx_range_pct_min3", "spx_range_pct_max3", "spx_range_pct_ma5", "spx_range_pct_std5", "spx_range_pct_min5", "spx_range_pct_max5", "spx_range_pct_ma10", "spx_range_pct_std10", "spx_range_pct_min10", "spx_range_pct_max10", "put_call_oi_ratio_ma3", "put_call_oi_ratio_std3", "put_call_oi_ratio_min3", "put_call_oi_ratio_max3", "put_call_oi_ratio_ma5", "put_call_oi_ratio_std5", "put_call_oi_ratio_min5", "put_call_oi_ratio_max5", "put_call_oi_ratio_ma10", "put_call_oi_ratio_std10", "put_call_oi_ratio_min10", "put_call_oi_ratio_max10", "total_gamma_ma3", "total_gamma_std3", "total_gamma_min3", "total_gamma_max3", "total_gamma_ma5", "total_gamma_std5", "total_gamma_min5", "total_gamma_max5", "total_gamma_ma10", "total_gamma_std10", "total_gamma_min10", "total_gamma_max10", "total_volume_ma3", "total_volume_std3", "total_volume_min3", "total_volume_max3", "total_volume_ma5", "total_volume_std5", "total_volume_min5", "total_volume_max5", "total_volume_ma10", "total_volume_std10", "total_volume_min10", "total_volume_max10"], "momentum_features": ["spx_momentum_3d", "spx_momentum_5d", "spx_momentum_10d", "return_momentum_3d", "return_momentum_5d", "realized_vol_5d", "realized_vol_10d", "gamma_vol_interaction", "vol_gamma_interaction", "vol_pc_interaction", "high_vol_regime"], "options_features": ["put_wall_strike", "put_wall_oi", "call_wall_strike", "call_wall_oi", "gamma_put_wall_strike", "gamma_put_wall_exposure", "gamma_call_wall_strike", "gamma_call_wall_exposure", "gamma_exposure_ratio", "total_gamma_lag1", "total_gamma_lag2", "total_gamma_lag3", "total_gamma_lag4", "total_gamma_lag5", "spx_to_put_wall_lag1", "spx_to_put_wall_lag2", "spx_to_put_wall_lag3", "spx_to_put_wall_lag4", "spx_to_put_wall_lag5", "spx_to_call_wall_lag1", "spx_to_call_wall_lag2", "spx_to_call_wall_lag3", "spx_to_call_wall_lag4", "spx_to_call_wall_lag5", "total_gamma_ma3", "total_gamma_std3", "total_gamma_min3", "total_gamma_max3", "total_gamma_ma5", "total_gamma_std5", "total_gamma_min5", "total_gamma_max5", "total_gamma_ma10", "total_gamma_std10", "total_gamma_min10", "total_gamma_max10", "gamma_change_1d", "gamma_change_3d", "pc_ratio_change_1d", "pc_ratio_change_3d", "volume_surge_3d", "volume_surge_5d", "put_wall_distance_change", "call_wall_distance_change", "gamma_pc_interaction", "gamma_vol_interaction", "wall_squeeze", "wall_asymmetry", "vol_gamma_interaction", "vol_pc_interaction", "total_gamma_rank"], "interaction_features": ["gamma_pc_interaction", "gamma_vol_interaction", "wall_squeeze", "wall_asymmetry", "vol_gamma_interaction", "vol_pc_interaction"], "warped_features": ["log_total_volume", "log_total_oi", "sqrt_range", "put_call_oi_ratio_rank", "total_gamma_rank", "total_volume_rank"], "regime_features": ["high_vol_regime", "uptrend_regime", "high_options_activity"], "target_features": ["target_direction", "target_return", "target_range", "target_direction_2d", "target_return_2d"]}