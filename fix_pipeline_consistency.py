"""
Fix Pipeline Consistency for Enhanced SPX + VIX System
Ensures scaler and selected features are consistent between training and backtesting
"""

import pandas as pd
import numpy as np
import pickle
import json
from sklearn.preprocessing import StandardScaler
from config import config

def fix_pipeline_consistency():
    """Fix consistency between model training and backtesting"""
    print("🔧 FIXING PIPELINE CONSISTENCY")
    print("=" * 60)
    
    # 1. Load engineered data
    print("1. Loading engineered data...")
    data = pd.read_csv(config.get_data_file('engineered_data'))
    print(f"   📊 Data shape: {data.shape}")
    
    # 2. Load feature info
    print("2. Loading feature info...")
    with open(config.get_data_file('feature_info'), 'r') as f:
        feature_info = json.load(f)
    
    all_features = feature_info['all_features']
    print(f"   📊 Total features: {len(all_features)}")
    
    # 3. Load selected features
    print("3. Loading selected features...")
    with open(config.get_data_file('selected_features'), 'r') as f:
        selected_features = json.load(f)
    print(f"   🎯 Selected features: {len(selected_features)}")
    
    # 4. Prepare data with only selected features
    print("4. Preparing data with selected features...")
    
    # Remove target and date columns
    feature_data = data[all_features].copy()
    
    # Check which selected features are available
    available_selected = [f for f in selected_features if f in feature_data.columns]
    missing_selected = [f for f in selected_features if f not in feature_data.columns]
    
    if missing_selected:
        print(f"   ⚠️  Missing {len(missing_selected)} selected features:")
        for feat in missing_selected[:5]:
            print(f"      - {feat}")
        if len(missing_selected) > 5:
            print(f"      ... and {len(missing_selected) - 5} more")
    
    print(f"   ✅ Available selected features: {len(available_selected)}")
    
    # Use only available selected features
    X_selected = feature_data[available_selected].copy()
    
    # 5. Handle missing values
    print("5. Handling missing values...")
    for col in X_selected.columns:
        if X_selected[col].dtype in ['float64', 'int64']:
            X_selected[col] = X_selected[col].fillna(X_selected[col].median())
    
    # Replace infinite values
    X_selected = X_selected.replace([np.inf, -np.inf], np.nan)
    for col in X_selected.columns:
        if X_selected[col].dtype in ['float64', 'int64']:
            X_selected[col] = X_selected[col].fillna(X_selected[col].median())
    
    print(f"   ✅ Clean data shape: {X_selected.shape}")
    
    # 6. Create new scaler for selected features only
    print("6. Creating new scaler for selected features...")
    scaler = StandardScaler()
    scaler.fit(X_selected)
    print(f"   ✅ Scaler fitted on {X_selected.shape[1]} features")
    
    # 7. Backup original files and save new ones
    print("7. Saving updated files...")
    
    # Backup original scaler if it exists
    try:
        with open(config.get_model_file('scaler'), 'rb') as f:
            original_scaler = pickle.load(f)
        with open(config.get_model_file('scaler').replace('.pkl', '_backup.pkl'), 'wb') as f:
            pickle.dump(original_scaler, f)
        print("   📁 Original scaler backed up")
    except:
        print("   📁 No original scaler to backup")
    
    # Save new scaler
    with open(config.get_model_file('scaler'), 'wb') as f:
        pickle.dump(scaler, f)
    print(f"   ✅ New scaler saved: {config.get_model_file('scaler')}")
    
    # Update selected features to only include available ones
    with open(config.get_data_file('selected_features'), 'w') as f:
        json.dump(available_selected, f, indent=2)
    print(f"   ✅ Updated selected features: {len(available_selected)}")
    
    # 8. Test the fix
    print("8. Testing the fix...")
    
    # Test scaler
    test_data = X_selected.iloc[:10]
    try:
        scaled_test = scaler.transform(test_data)
        print(f"   ✅ Scaler test successful: {test_data.shape} -> {scaled_test.shape}")
    except Exception as e:
        print(f"   ❌ Scaler test failed: {str(e)}")
        return False
    
    # Test feature consistency
    if len(available_selected) == X_selected.shape[1]:
        print(f"   ✅ Feature consistency verified: {len(available_selected)} features")
    else:
        print(f"   ❌ Feature mismatch: {len(available_selected)} vs {X_selected.shape[1]}")
        return False
    
    print("\n✅ PIPELINE CONSISTENCY FIXED!")
    print(f"   🔧 Scaler works with {len(available_selected)} selected features")
    print(f"   📁 Files updated and backed up")
    print(f"   🚀 Ready for consistent backtesting")
    
    return True

if __name__ == "__main__":
    success = fix_pipeline_consistency()
    if success:
        print("\n🎉 Pipeline consistency fix completed successfully!")
    else:
        print("\n❌ Pipeline consistency fix failed!")
